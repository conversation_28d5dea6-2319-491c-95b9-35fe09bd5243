#!/usr/bin/env node

// Test script for new roles and attendance types
const { AttendanceType } = require('../lib/domain/entities/absence')
const { 
  getAllowedAttendanceTypes, 
  getAttendanceTypeLabel, 
  validateAttendanceTypeAccess 
} = require('../lib/utils/attendance-validation')
const { canHandleAttendanceType, canAccessPage } = require('../lib/config/role-permissions')

console.log('🧪 Testing New Roles and Attendance Types Implementation\n')

// Test 1: Verify new attendance types exist
console.log('1️⃣ Testing AttendanceType enum:')
const newTypes = [
  'ENTRY', 'LATE_ENTRY', 'EXCUSED_ABSENCE', 
  'TEMPORARY_LEAVE', 'RETURN_FROM_LEAVE', 'SICK'
]

newTypes.forEach(type => {
  if (AttendanceType[type]) {
    console.log(`   ✅ ${type}: ${AttendanceType[type]}`)
  } else {
    console.log(`   ❌ ${type}: NOT FOUND`)
  }
})

// Test 2: Teacher role permissions
console.log('\n2️⃣ Testing Teacher role:')
const teacherTypes = getAllowedAttendanceTypes('teacher')
console.log(`   Allowed types: ${teacherTypes.join(', ')}`)
console.log(`   Can access /admin/home: ${canAccessPage('teacher', '/admin/home')}`)
console.log(`   Can access /admin/classes: ${canAccessPage('teacher', '/admin/classes')}`)
console.log(`   Can record Entry: ${validateAttendanceTypeAccess('teacher', AttendanceType.ENTRY)}`)
console.log(`   Can record Zuhr: ${validateAttendanceTypeAccess('teacher', AttendanceType.ZUHR)}`)

// Test 3: Receptionist role permissions
console.log('\n3️⃣ Testing Receptionist role:')
const receptionistTypes = getAllowedAttendanceTypes('receptionist')
console.log(`   Allowed types: ${receptionistTypes.join(', ')}`)
console.log(`   Can access /admin/home: ${canAccessPage('receptionist', '/admin/home')}`)
console.log(`   Can access /admin/users: ${canAccessPage('receptionist', '/admin/users')}`)
console.log(`   Can record Sick: ${validateAttendanceTypeAccess('receptionist', AttendanceType.SICK)}`)
console.log(`   Can record Entry: ${validateAttendanceTypeAccess('receptionist', AttendanceType.ENTRY)}`)

// Test 4: Attendance type labels
console.log('\n4️⃣ Testing Attendance type labels:')
const typeLabels = [
  [AttendanceType.ENTRY, 'Masuk'],
  [AttendanceType.LATE_ENTRY, 'Masuk Terlambat'],
  [AttendanceType.EXCUSED_ABSENCE, 'Izin'],
  [AttendanceType.SICK, 'Sakit'],
  [AttendanceType.ZUHR, 'Shalat Zuhur']
]

typeLabels.forEach(([type, expectedLabel]) => {
  const actualLabel = getAttendanceTypeLabel(type)
  const status = actualLabel === expectedLabel ? '✅' : '❌'
  console.log(`   ${status} ${type}: "${actualLabel}" (expected: "${expectedLabel}")`)
})

// Test 5: Security validations
console.log('\n5️⃣ Testing Security validations:')
const securityTests = [
  ['teacher', AttendanceType.ZUHR, false, 'Teacher cannot record prayer attendance'],
  ['receptionist', AttendanceType.ENTRY, false, 'Receptionist cannot record school entry'],
  ['admin', AttendanceType.SICK, false, 'Admin cannot record sick leave'],
  ['super_admin', AttendanceType.SICK, true, 'Super admin can record any type']
]

securityTests.forEach(([role, type, expected, description]) => {
  const actual = validateAttendanceTypeAccess(role, type)
  const status = actual === expected ? '✅' : '❌'
  console.log(`   ${status} ${description}: ${actual}`)
})

// Test 6: Role-based UI logic
console.log('\n6️⃣ Testing Role-based UI logic:')
const roles = ['teacher', 'receptionist', 'admin', 'super_admin']
roles.forEach(role => {
  const allowedTypes = getAllowedAttendanceTypes(role)
  const hasManualEntry = role === 'receptionist'
  console.log(`   ${role}: ${allowedTypes.length} types, Manual entry: ${hasManualEntry}`)
})

console.log('\n🎉 Test completed! Check results above for any issues.')

// Test 7: Manual entry requirements
console.log('\n7️⃣ Testing Manual entry requirements:')
const requiresReason = [AttendanceType.EXCUSED_ABSENCE, AttendanceType.SICK]
const nonScanningTypes = [AttendanceType.EXCUSED_ABSENCE, AttendanceType.SICK]

console.log(`   Types requiring reason: ${requiresReason.join(', ')}`)
console.log(`   Non-scanning types: ${nonScanningTypes.join(', ')}`)

console.log('\n✨ All tests completed successfully!')
