#!/usr/bin/env node

/**
 * Verification script for new roles and attendance types implementation
 * This script verifies that the implementation is working correctly
 */

console.log('🔍 Verifying New Roles and Attendance Types Implementation\n')

// Import required modules
const fs = require('fs')
const path = require('path')

// Test 1: Verify AttendanceType enum has all required values
console.log('1️⃣ Testing AttendanceType enum...')
try {

  const absenceFilePath = path.join(__dirname, '../lib/domain/entities/absence.ts')
  const absenceContent = fs.readFileSync(absenceFilePath, 'utf8')

  const requiredTypes = [
    'ZUHR', 'ASR', 'IJIN', 'DISMISSAL', // Existing
    'ENTRY', 'LATE_ENTRY', 'EXCUSED_ABSENCE', 'TEMPORARY_LEAVE', 'RETURN_FROM_LEAVE', 'SICK' // New
  ]

  let allTypesFound = true
  requiredTypes.forEach(type => {
    if (absenceContent.includes(`${type} =`)) {
      console.log(`   ✅ ${type} found`)
    } else {
      console.log(`   ❌ ${type} NOT found`)
      allTypesFound = false
    }
  })

  if (allTypesFound) {
    console.log('   ✅ All attendance types are present\n')
  } else {
    console.log('   ❌ Some attendance types are missing\n')
  }
} catch (error) {
  console.log(`   ❌ Error reading attendance types: ${error.message}\n`)
}

// Test 2: Verify role permissions configuration
console.log('2️⃣ Testing role permissions configuration...')
try {
  const rolePermissionsPath = path.join(__dirname, '../lib/config/role-permissions.ts')
  const rolePermissionsContent = fs.readFileSync(rolePermissionsPath, 'utf8')

  const requiredRoles = ['student', 'admin', 'super_admin', 'teacher', 'receptionist']

  let allRolesFound = true
  requiredRoles.forEach(role => {
    if (rolePermissionsContent.includes(`${role}:`)) {
      console.log(`   ✅ ${role} role configuration found`)
    } else {
      console.log(`   ❌ ${role} role configuration NOT found`)
      allRolesFound = false
    }
  })

  if (allRolesFound) {
    console.log('   ✅ All role configurations are present\n')
  } else {
    console.log('   ❌ Some role configurations are missing\n')
  }
} catch (error) {
  console.log(`   ❌ Error reading role permissions: ${error.message}\n`)
}

// Test 3: Verify database schema updates
console.log('3️⃣ Testing database schema updates...')
try {
  const schemaPath = path.join(__dirname, '../lib/data/drizzle/schema.ts')
  const schemaContent = fs.readFileSync(schemaPath, 'utf8')

  // Check for new roles in userRoleEnum
  const hasTeacherRole = schemaContent.includes("'teacher'")
  const hasReceptionistRole = schemaContent.includes("'receptionist'")

  // Check for new attendance types in attendanceTypeEnum
  const hasNewAttendanceTypes = [
    "'Entry'", "'Late Entry'", "'Excused Absence'",
    "'Temporary Leave'", "'Return from Leave'", "'Sick'"
  ].every(type => schemaContent.includes(type))

  if (hasTeacherRole) {
    console.log('   ✅ Teacher role found in schema')
  } else {
    console.log('   ❌ Teacher role NOT found in schema')
  }

  if (hasReceptionistRole) {
    console.log('   ✅ Receptionist role found in schema')
  } else {
    console.log('   ❌ Receptionist role NOT found in schema')
  }

  if (hasNewAttendanceTypes) {
    console.log('   ✅ All new attendance types found in schema')
  } else {
    console.log('   ❌ Some new attendance types NOT found in schema')
  }

  console.log('')
} catch (error) {
  console.log(`   ❌ Error reading database schema: ${error.message}\n`)
}

// Test 4: Verify API endpoints exist
console.log('4️⃣ Testing API endpoints...')
try {
  const apiPaths = [
    '../app/api/students/search/route.ts',
    '../app/api/absence/manual/route.ts'
  ]

  apiPaths.forEach(apiPath => {
    const fullPath = path.join(__dirname, apiPath)
    if (fs.existsSync(fullPath)) {
      console.log(`   ✅ ${apiPath} exists`)
    } else {
      console.log(`   ❌ ${apiPath} NOT found`)
    }
  })

  console.log('')
} catch (error) {
  console.log(`   ❌ Error checking API endpoints: ${error.message}\n`)
}

// Test 5: Verify UI components exist
console.log('5️⃣ Testing UI components...')
try {
  const uiPaths = [
    '../app/admin/home/<USER>'
  ]

  uiPaths.forEach(uiPath => {
    const fullPath = path.join(__dirname, uiPath)
    if (fs.existsSync(fullPath)) {
      console.log(`   ✅ ${uiPath} exists`)
    } else {
      console.log(`   ❌ ${uiPath} NOT found`)
    }
  })

  console.log('')
} catch (error) {
  console.log(`   ❌ Error checking UI components: ${error.message}\n`)
}

// Test 6: Verify migration script exists
console.log('6️⃣ Testing migration script...')
try {
  const migrationPath = path.join(__dirname, '../drizzle/migrations/add_new_roles_and_attendance_types.sql')
  if (fs.existsSync(migrationPath)) {
    console.log('   ✅ Migration script exists')

    const migrationContent = fs.readFileSync(migrationPath, 'utf8')
    const hasRoleUpdates = migrationContent.includes("ADD VALUE 'teacher'") &&
      migrationContent.includes("ADD VALUE 'receptionist'")
    const hasAttendanceUpdates = migrationContent.includes("ADD VALUE 'Entry'") &&
      migrationContent.includes("ADD VALUE 'Sick'")

    if (hasRoleUpdates) {
      console.log('   ✅ Role updates found in migration')
    } else {
      console.log('   ❌ Role updates NOT found in migration')
    }

    if (hasAttendanceUpdates) {
      console.log('   ✅ Attendance type updates found in migration')
    } else {
      console.log('   ❌ Attendance type updates NOT found in migration')
    }
  } else {
    console.log('   ❌ Migration script NOT found')
  }

  console.log('')
} catch (error) {
  console.log(`   ❌ Error checking migration script: ${error.message}\n`)
}

// Test 7: Verify documentation exists
console.log('7️⃣ Testing documentation...')
try {
  const docPaths = [
    '../docs/NEW-ROLES-AND-ATTENDANCE-TYPES.md',
    '../TODO-ATTENDANCE-TYPES-AND-ROLES.md'
  ]

  docPaths.forEach(docPath => {
    const fullPath = path.join(__dirname, docPath)
    if (fs.existsSync(fullPath)) {
      console.log(`   ✅ ${docPath} exists`)
    } else {
      console.log(`   ❌ ${docPath} NOT found`)
    }
  })

  console.log('')
} catch (error) {
  console.log(`   ❌ Error checking documentation: ${error.message}\n`)
}

console.log('🎉 Verification completed!')
console.log('\n📋 Next Steps:')
console.log('1. Run database migration: npm run db:migrate')
console.log('2. Test the application in development: npm run dev')
console.log('3. Create test users with teacher and receptionist roles')
console.log('4. Verify manual entry functionality works correctly')
console.log('5. Test role-based access control')

console.log('\n✨ Implementation appears to be complete and ready for testing!')
