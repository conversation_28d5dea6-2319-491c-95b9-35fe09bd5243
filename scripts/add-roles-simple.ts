import { db } from '../lib/data/drizzle/db'
import { sql } from 'drizzle-orm'

async function addNewRoles() {
  try {
    console.log('🔧 Adding new roles to user_role enum...')
    
    // Add teacher role
    await db.execute(sql`ALTER TYPE user_role ADD VALUE IF NOT EXISTS 'teacher'`)
    console.log('✅ Added teacher role')
    
    // Add receptionist role  
    await db.execute(sql`ALTER TYPE user_role ADD VALUE IF NOT EXISTS 'receptionist'`)
    console.log('✅ Added receptionist role')
    
    // Update constraint
    await db.execute(sql`
      ALTER TABLE users DROP CONSTRAINT IF EXISTS chk_role_data;
      ALTER TABLE users ADD CONSTRAINT chk_role_data CHECK (
        (role = 'student' AND unique_code IS NOT NULL AND username IS NOT NULL AND password_hash IS NOT NULL) OR 
        (role IN ('admin', 'super_admin', 'teacher', 'receptionist') AND google_email IS NULL AND unique_code IS NULL AND username IS NOT NULL AND password_hash IS NOT NULL)
      );
    `)
    console.log('✅ Updated constraint')
    
    console.log('🎉 Migration completed!')
    
  } catch (error) {
    console.error('❌ Error:', error)
  }
}

addNewRoles()
