#!/usr/bin/env tsx

/**
 * Migration script to add new roles (teacher, receptionist) to the user_role enum
 * 
 * This script safely adds the new enum values to the existing PostgreSQL enum type.
 * It's designed to be idempotent - can be run multiple times safely.
 */

import { drizzle } from 'drizzle-orm/postgres-js'
import postgres from 'postgres'
import { config } from 'dotenv'

// Load environment variables
config({ path: '.env.local' })

const DATABASE_URL = process.env.DATABASE_URL
if (!DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is required')
  process.exit(1)
}

console.log('🔧 Starting migration to add new user roles...')

async function addNewRolesToEnum() {
  const sql = postgres(DATABASE_URL)
  const db = drizzle(sql)

  try {
    console.log('📋 Checking current enum values...')
    
    // Check current enum values
    const currentEnumValues = await sql`
      SELECT enumlabel 
      FROM pg_enum 
      WHERE enumtypid = (
        SELECT oid 
        FROM pg_type 
        WHERE typname = 'user_role'
      )
      ORDER BY enumsortorder;
    `
    
    console.log('📋 Current user_role enum values:', currentEnumValues.map(row => row.enumlabel))
    
    // Check if teacher already exists
    const hasTeacher = currentEnumValues.some(row => row.enumlabel === 'teacher')
    const hasReceptionist = currentEnumValues.some(row => row.enumlabel === 'receptionist')
    
    if (hasTeacher && hasReceptionist) {
      console.log('✅ Both teacher and receptionist roles already exist in the enum')
      return
    }
    
    // Add teacher if it doesn't exist
    if (!hasTeacher) {
      console.log('➕ Adding "teacher" to user_role enum...')
      await sql`ALTER TYPE user_role ADD VALUE IF NOT EXISTS 'teacher'`
      console.log('✅ Added "teacher" role')
    } else {
      console.log('✅ "teacher" role already exists')
    }
    
    // Add receptionist if it doesn't exist
    if (!hasReceptionist) {
      console.log('➕ Adding "receptionist" to user_role enum...')
      await sql`ALTER TYPE user_role ADD VALUE IF NOT EXISTS 'receptionist'`
      console.log('✅ Added "receptionist" role')
    } else {
      console.log('✅ "receptionist" role already exists')
    }
    
    // Verify the changes
    console.log('🔍 Verifying changes...')
    const updatedEnumValues = await sql`
      SELECT enumlabel 
      FROM pg_enum 
      WHERE enumtypid = (
        SELECT oid 
        FROM pg_type 
        WHERE typname = 'user_role'
      )
      ORDER BY enumsortorder;
    `
    
    console.log('📋 Updated user_role enum values:', updatedEnumValues.map(row => row.enumlabel))
    
    // Update the constraint to include new roles
    console.log('🔧 Updating role data constraint...')
    
    await sql`
      -- Drop existing constraint
      ALTER TABLE users DROP CONSTRAINT IF EXISTS chk_role_data;
    `
    
    await sql`
      -- Add updated constraint that includes new roles
      ALTER TABLE users ADD CONSTRAINT chk_role_data CHECK (
        (role = 'student' AND unique_code IS NOT NULL AND username IS NOT NULL AND password_hash IS NOT NULL) OR 
        (role IN ('admin', 'super_admin', 'teacher', 'receptionist') AND google_email IS NULL AND unique_code IS NULL AND username IS NOT NULL AND password_hash IS NOT NULL)
      );
    `
    
    console.log('✅ Updated role data constraint to include new roles')
    
    console.log('🎉 Migration completed successfully!')
    console.log('📝 Summary:')
    console.log('   - Added "teacher" role to user_role enum')
    console.log('   - Added "receptionist" role to user_role enum') 
    console.log('   - Updated role data constraint')
    console.log('   - Super Admins can now create Teacher and Receptionist accounts')
    
  } catch (error) {
    console.error('❌ Migration failed:', error)
    throw error
  } finally {
    await sql.end()
  }
}

// Run the migration
addNewRolesToEnum()
  .then(() => {
    console.log('✅ Migration script completed successfully')
    process.exit(0)
  })
  .catch((error) => {
    console.error('❌ Migration script failed:', error)
    process.exit(1)
  })
