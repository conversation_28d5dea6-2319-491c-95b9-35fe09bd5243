-- Fix class_id constraint to allow NULL for admin users
-- This fixes the issue where creating teacher/receptionist accounts fails

-- Step 1: Remove NOT NULL constraint from class_id if it exists
ALTER TABLE users ALTER COLUMN class_id DROP NOT NULL;

-- Step 2: Update the role data constraint to properly handle new roles
ALTER TABLE users DROP CONSTRAINT IF EXISTS chk_role_data;

-- Step 3: Add updated constraint that allows NULL class_id for admin roles
ALTER TABLE users ADD CONSTRAINT chk_role_data CHECK (
  (role = 'student' AND unique_code IS NOT NULL AND username IS NOT NULL AND password_hash IS NOT NULL) OR 
  (role IN ('admin', 'super_admin', 'teacher', 'receptionist') AND google_email IS NULL AND unique_code IS NULL AND username IS NOT NULL AND password_hash IS NOT NULL AND class_id IS NULL)
);

-- Step 4: Verify the column allows NULL
SELECT 
  column_name, 
  is_nullable, 
  data_type 
FROM information_schema.columns 
WHERE table_name = 'users' AND column_name = 'class_id';
