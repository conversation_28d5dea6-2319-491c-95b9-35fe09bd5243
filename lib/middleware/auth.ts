import { NextRequest, NextResponse } from 'next/server'
import { verifyToken } from '@/lib/utils/auth'
import { serverConfig } from '@/lib/config'

/**
 * Middleware to authenticate requests based on role
 * @param req The incoming request
 * @param role The expected role ('admin' or 'student')
 * @returns The authenticated user ID and role, or throws an error
 */
export async function authenticate(
  req: NextRequest,
  role?: 'admin' | 'student'
): Promise<{ id: number; role: string }> {
  // If role is specified, use the role-specific token
  let authToken: string | undefined = undefined

  if (role === 'admin') {
    authToken = req.cookies.get('admin_auth_token')?.value
  } else if (role === 'student') {
    authToken = req.cookies.get('student_auth_token')?.value
  } else {
    // If no specific role, try to find any auth token
    authToken =
      req.cookies.get('admin_auth_token')?.value || req.cookies.get('student_auth_token')?.value
  }

  if (!authToken) {
    throw new Error('Authentication required')
  }

  try {
    const decoded = verifyToken(authToken, serverConfig.auth.jwtSecret || '')

    if (!decoded || !decoded.id) {
      console.error('Token verification failed: Missing ID in token payload')
      throw new Error('Invalid token')
    }

    return decoded
  } catch (error) {
    console.error('Token verification error:', error)
    throw new Error('Invalid or expired token')
  }
}

/**
 * Middleware to authenticate student requests
 * @param req The incoming request
 * @returns The authenticated student ID, or throws an error
 */
export async function authenticateStudent(req: NextRequest): Promise<number> {
  try {
    const decoded = await authenticate(req, 'student')

    if (decoded.role !== 'student') {
      console.error(`Access denied: Role "${decoded.role}" is not "student"`)
      throw new Error('Access denied: Student role required')
    }

    return decoded.id
  } catch (error) {
    console.error('Student authentication error:', error)
    throw error
  }
}

/**
 * Middleware to authenticate admin requests
 * @param req The incoming request
 * @returns The authenticated admin ID, or throws an error
 */
export async function authenticateAdmin(req: NextRequest): Promise<number> {
  try {
    const decoded = await authenticate(req, 'admin')

    if (!['admin', 'super_admin', 'teacher', 'receptionist'].includes(decoded.role)) {
      console.error(`Access denied: Role "${decoded.role}" is not a valid admin role`)
      throw new Error('Access denied: Admin role required')
    }

    return decoded.id
  } catch (error) {
    console.error('Admin authentication error:', error)
    throw error
  }
}

/**
 * Handle authentication errors
 * @param error The error that occurred
 * @returns A NextResponse with the appropriate error message and status code
 */
export function handleAuthError(error: unknown): NextResponse {
  const message = error instanceof Error ? error.message : 'Authentication failed'
  console.error('Auth middleware error:', message)

  if (message.includes('Authentication required')) {
    return NextResponse.json({ error: message }, { status: 401 })
  }

  if (message.includes('Invalid') || message.includes('expired')) {
    return NextResponse.json({ error: message }, { status: 401 })
  }

  if (message.includes('Access denied')) {
    return NextResponse.json({ error: message }, { status: 403 })
  }

  return NextResponse.json({ error: 'Authentication failed' }, { status: 401 })
}
