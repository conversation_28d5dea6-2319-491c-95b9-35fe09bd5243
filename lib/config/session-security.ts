/**
 * Session Security Configuration
 *
 * This module defines security policies and constants for session management
 * Based on industry best practices and OWASP recommendations
 */

import { serverConfig } from '../config'

/**
 * Session Security Constants
 * Based on OWASP Session Management Cheat Sheet
 */
export const SESSION_SECURITY_CONFIG = {
  // Maximum concurrent sessions per user (prevents session flooding attacks)
  MAX_CONCURRENT_SESSIONS: 3,

  // Session duration settings
  SESSION_DURATION: {
    DEFAULT_SECONDS: 3600, // 1 hour
    REMEMBER_ME_SECONDS: 7 * 24 * 3600, // 7 days
    ADMIN_SECONDS: 2 * 3600, // 2 hours (shorter for admin security)
    STUDENT_SECONDS: 4 * 3600, // 4 hours
  },

  // Session refresh settings
  REFRESH_THRESHOLD: {
    MINUTES_BEFORE_EXPIRY: 10, // Refresh token if within 10 minutes of expiry
    MAX_REFRESH_ATTEMPTS: 3, // Maximum refresh attempts per session
  },

  // Device security settings
  DEVICE_SECURITY: {
    ENABLE_FINGERPRINTING: true,
    STRICT_IP_VALIDATION: false, // Set to true for high-security environments
    ALLOW_IP_SUBNET_CHANGES: true, // Allow mobile network changes
    USER_AGENT_VALIDATION: true,
  },

  // Session monitoring settings
  MONITORING: {
    CHECK_INTERVAL_SECONDS: 60, // Check session validity every 60 seconds
    ENABLE_REAL_TIME_VALIDATION: true,
    LOG_SECURITY_EVENTS: true,
  },

  // Rate limiting for session operations
  RATE_LIMITS: {
    LOGIN_ATTEMPTS_PER_MINUTE: 5,
    SESSION_CREATION_PER_MINUTE: 10,
    VALIDATION_REQUESTS_PER_MINUTE: 60,
  },

  // Security headers
  SECURITY_HEADERS: {
    ENABLE_CSRF_PROTECTION: true,
    ENABLE_XSS_PROTECTION: true,
    ENABLE_CONTENT_TYPE_NOSNIFF: true,
    ENABLE_FRAME_OPTIONS: true,
  },
} as const

/**
 * Get session duration based on role and remember me preference
 */
export function getSessionDuration(
  role: 'student' | 'admin' | 'super_admin' | 'teacher' | 'receptionist',
  rememberMe: boolean = false
): number {
  if (rememberMe) {
    return SESSION_SECURITY_CONFIG.SESSION_DURATION.REMEMBER_ME_SECONDS
  }

  switch (role) {
    case 'admin':
    case 'super_admin':
    case 'teacher':
    case 'receptionist':
      return SESSION_SECURITY_CONFIG.SESSION_DURATION.ADMIN_SECONDS
    case 'student':
      return SESSION_SECURITY_CONFIG.SESSION_DURATION.STUDENT_SECONDS
    default:
      return SESSION_SECURITY_CONFIG.SESSION_DURATION.DEFAULT_SECONDS
  }
}

/**
 * Validate session security configuration
 */
export function validateSessionSecurityConfig(): {
  isValid: boolean
  errors: string[]
  warnings: string[]
} {
  const errors: string[] = []
  const warnings: string[] = []

  // Check if running in production with secure settings
  if (serverConfig.environment.isProduction) {
    if (!SESSION_SECURITY_CONFIG.DEVICE_SECURITY.ENABLE_FINGERPRINTING) {
      warnings.push('Device fingerprinting is disabled in production')
    }

    if (!SESSION_SECURITY_CONFIG.DEVICE_SECURITY.USER_AGENT_VALIDATION) {
      errors.push('User agent validation should be enabled in production')
    }

    if (SESSION_SECURITY_CONFIG.SESSION_DURATION.ADMIN_SECONDS > 4 * 3600) {
      warnings.push('Admin session duration is longer than recommended (4 hours)')
    }

    if (!SESSION_SECURITY_CONFIG.SECURITY_HEADERS.ENABLE_CSRF_PROTECTION) {
      errors.push('CSRF protection should be enabled in production')
    }
  }

  // Check rate limits
  if (SESSION_SECURITY_CONFIG.RATE_LIMITS.LOGIN_ATTEMPTS_PER_MINUTE > 10) {
    warnings.push('Login rate limit is higher than recommended (10/minute)')
  }

  // Check concurrent sessions
  if (SESSION_SECURITY_CONFIG.MAX_CONCURRENT_SESSIONS > 5) {
    warnings.push('Maximum concurrent sessions is higher than recommended (5)')
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  }
}

/**
 * Session Security Policy Enforcement
 */
export class SessionSecurityPolicy {
  /**
   * Check if user can create a new session
   */
  static canCreateSession(
    existingSessionCount: number,
    _role: 'student' | 'admin' | 'super_admin' | 'teacher' | 'receptionist' // Prefixed with _ to indicate intentionally unused for now
  ): { allowed: boolean; reason?: string } {
    if (existingSessionCount >= SESSION_SECURITY_CONFIG.MAX_CONCURRENT_SESSIONS) {
      return {
        allowed: false,
        reason: `Maximum concurrent sessions exceeded (${SESSION_SECURITY_CONFIG.MAX_CONCURRENT_SESSIONS})`,
      }
    }

    return { allowed: true }
  }

  /**
   * Determine which sessions to invalidate when creating a new one
   */
  static getSessionsToInvalidate(
    existingSessions: Array<{ sessionId: string; lastAccessedAt: Date; deviceId: string }>,
    newDeviceId: string
  ): string[] {
    const sessionsToInvalidate: string[] = []

    // Always invalidate sessions from the same device
    existingSessions.forEach(session => {
      if (session.deviceId === newDeviceId) {
        sessionsToInvalidate.push(session.sessionId)
      }
    })

    // If we're at the limit, invalidate the oldest session
    if (existingSessions.length >= SESSION_SECURITY_CONFIG.MAX_CONCURRENT_SESSIONS) {
      const oldestSession = existingSessions
        .filter(s => s.deviceId !== newDeviceId) // Don't double-count same device
        .sort((a, b) => a.lastAccessedAt.getTime() - b.lastAccessedAt.getTime())[0]

      if (oldestSession && !sessionsToInvalidate.includes(oldestSession.sessionId)) {
        sessionsToInvalidate.push(oldestSession.sessionId)
      }
    }

    return sessionsToInvalidate
  }

  /**
   * Check if session needs security validation
   */
  static needsSecurityValidation(
    session: { createdAt: Date; lastAccessedAt: Date },
    _requestInfo: { userAgent: string; ipAddress: string } // Prefixed with _ to indicate intentionally unused for now
  ): boolean {
    // Always validate if device security is enabled
    if (SESSION_SECURITY_CONFIG.DEVICE_SECURITY.ENABLE_FINGERPRINTING) {
      return true
    }

    // Validate if session is older than 1 hour
    const oneHourAgo = new Date(Date.now() - 3600 * 1000)
    if (session.createdAt < oneHourAgo) {
      return true
    }

    return false
  }
}

/**
 * Security event logging
 */
export function logSecurityEvent(
  event:
    | 'session_created'
    | 'session_invalidated'
    | 'device_mismatch'
    | 'ip_change'
    | 'suspicious_activity',
  details: {
    userId: number
    sessionId?: string
    deviceId?: string
    ipAddress?: string
    userAgent?: string
    reason?: string
  }
): void {
  if (!SESSION_SECURITY_CONFIG.MONITORING.LOG_SECURITY_EVENTS) {
    return
  }

  const logEntry = {
    timestamp: new Date().toISOString(),
    event,
    ...details,
    environment: serverConfig.environment.nodeEnv,
  }

  // In production, this should go to a security monitoring system
  if (serverConfig.environment.isProduction) {
    console.log(`🔒 SECURITY EVENT: ${JSON.stringify(logEntry)}`)
  } else {
    console.log(
      `🔒 [${event.toUpperCase()}] User ${details.userId}:`,
      details.reason || 'No reason provided'
    )
  }
}

/**
 * Export configuration for use in other modules
 */
export default SESSION_SECURITY_CONFIG
