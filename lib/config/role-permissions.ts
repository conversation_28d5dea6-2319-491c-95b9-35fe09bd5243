import { AttendanceType } from '@/lib/domain/entities/absence'

// Use existing role types from the codebase (SECURE)
export type UserRole = 'student' | 'admin' | 'super_admin' | 'teacher' | 'receptionist'

export interface RoleConfig {
  allowedPages: string[] // Page patterns this role can access
  redirectTo: string // Default redirect for this role
  attendanceTypes: AttendanceType[] | ['qr-display'] | ['all'] // Which attendance types they can handle
  navigation: {
    label: string
    path: string
    icon?: string
  }[]
}

export const ROLE_CONFIG: Record<UserRole, RoleConfig> = {
  student: {
    allowedPages: ['/student/home', '/student/profile'],
    redirectTo: '/student/home',
    attendanceTypes: ['qr-display'], // Can only display QR code
    navigation: [
      { label: 'Home', path: '/student/home', icon: 'home' },
      { label: 'Profil', path: '/student/profile', icon: 'user' }, // Indonesian label
    ],
  },

  admin: {
    // FIXED: Admin uses /admin/home (scanner) not /admin/scanner
    allowedPages: ['/admin/home', '/admin/prayer-reports', '/admin/analytics', '/admin/profile'],
    redirectTo: '/admin/home', // FIXED: Admin redirects to scanner page (home)
    attendanceTypes: [
      AttendanceType.ZUHR,
      AttendanceType.ASR,
      AttendanceType.DISMISSAL,
      AttendanceType.IJIN,
    ], // Use actual enum values
    navigation: [
      { label: 'Scanner', path: '/admin/home', icon: 'camera' }, // FIXED: Scanner is at /admin/home
      { label: 'Analytics', path: '/admin/analytics', icon: 'bar-chart-3' }, // Analytics Dashboard
      { label: 'Laporan Shalat', path: '/admin/prayer-reports', icon: 'file-text' }, // Prayer Reports only
      { label: 'Profil', path: '/admin/profile', icon: 'user' }, // Indonesian label
    ],
  },

  super_admin: {
    allowedPages: ['/admin/*'], // Full system access
    redirectTo: '/admin/home',
    attendanceTypes: ['all'], // All attendance types
    navigation: [
      { label: 'Scanner', path: '/admin/home', icon: 'camera' }, // FIXED: Scanner is at /admin/home
      { label: 'Analytics', path: '/admin/analytics', icon: 'bar-chart-3' }, // Analytics Dashboard
      { label: 'Laporan Shalat', path: '/admin/prayer-reports', icon: 'file-text' }, // Prayer Reports
      { label: 'Laporan Sekolah', path: '/admin/school-reports', icon: 'graduation-cap' }, // School Reports
      { label: 'Kelas', path: '/admin/classes', icon: 'users' }, // Indonesian label
      { label: 'Siswa', path: '/admin/users', icon: 'user' }, // Indonesian label
      { label: 'Admin', path: '/admin/admins', icon: 'shield' }, // Indonesian label
      { label: 'Sesi', path: '/admin/sessions', icon: 'clock' }, // Indonesian label
      { label: 'Profil', path: '/admin/profile', icon: 'user' }, // Indonesian label
    ],
  },

  teacher: {
    allowedPages: ['/admin/home', '/admin/school-reports', '/admin/profile'],
    redirectTo: '/admin/home',
    attendanceTypes: [AttendanceType.ENTRY],
    navigation: [
      { label: 'Scanner', path: '/admin/home', icon: 'camera' },
      { label: 'Laporan Sekolah', path: '/admin/school-reports', icon: 'graduation-cap' }, // School Reports only
      { label: 'Profil', path: '/admin/profile', icon: 'user' },
    ],
  },

  receptionist: {
    allowedPages: ['/admin/home', '/admin/school-reports', '/admin/profile'],
    redirectTo: '/admin/home',
    attendanceTypes: [
      AttendanceType.LATE_ENTRY,
      AttendanceType.EXCUSED_ABSENCE,
      AttendanceType.TEMPORARY_LEAVE,
      AttendanceType.RETURN_FROM_LEAVE,
      AttendanceType.SICK,
    ],
    navigation: [
      { label: 'Scanner', path: '/admin/home', icon: 'camera' },
      { label: 'Laporan Sekolah', path: '/admin/school-reports', icon: 'graduation-cap' }, // School Reports only
      { label: 'Profil', path: '/admin/profile', icon: 'user' },
    ],
  },
}

// SECURE helper functions with proper validation
export function canAccessPage(role: UserRole, page: string): boolean {
  if (!role || !page) return false

  const config = ROLE_CONFIG[role]
  if (!config) return false

  return config.allowedPages.some(pattern => {
    if (pattern.startsWith('!')) {
      // Exclusion pattern (e.g., '!/admin/users/*')
      const excludePattern = pattern.slice(1)
      return !matchesPattern(page, excludePattern)
    }
    return matchesPattern(page, pattern)
  })
}

export function getNavigationItems(role: UserRole) {
  const config = ROLE_CONFIG[role]
  return config ? config.navigation : []
}

export function canHandleAttendanceType(role: UserRole, type: AttendanceType | string): boolean {
  if (!role || !type) return false

  const config = ROLE_CONFIG[role]
  if (!config) return false

  // Special case for students - they can only display QR
  if (role === 'student') {
    return type === 'qr-display'
  }

  // Special case for super_admin - they can handle all types
  if (role === 'super_admin') {
    return true
  }

  // For admin role, check against specific attendance types
  // Use type-safe approach to handle the union type
  const attendanceTypes = config.attendanceTypes as (AttendanceType | string)[]

  // Check if 'all' is in the array (for super_admin case, but handled above)
  if (attendanceTypes.includes('all')) {
    return true
  }

  // Check if the specific type is in the array
  return attendanceTypes.includes(type)
}

export function getDefaultRedirect(role: UserRole): string {
  const config = ROLE_CONFIG[role]
  return config ? config.redirectTo : '/'
}

// SECURE pattern matching with input validation
function matchesPattern(path: string, pattern: string): boolean {
  if (!path || !pattern) return false

  if (pattern.endsWith('/*')) {
    const prefix = pattern.slice(0, -2)
    return path.startsWith(prefix)
  }
  return path === pattern
}
