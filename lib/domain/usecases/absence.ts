import { Absence, AttendanceType, AttendanceSummary, CreateAbsenceDTO } from '../entities/absence'
import { Student } from '../entities/student'
import { NotFoundError, DuplicateError } from '../errors'
import { PrayerNotCompletedError } from '../errors/PrayerNotCompletedError'
import { getCurrentWITATime } from '../../utils/date'

/**
 * Interface for absence repository
 */
export interface AbsenceRepository {
  findByUniqueCodeAndTypeAndDate(
    uniqueCode: string,
    type: AttendanceType,
    date: Date
  ): Promise<Absence | null>
  findByUniqueCodeAndDate(uniqueCode: string, date: Date): Promise<Absence[]>
  create(absence: CreateAbsenceDTO): Promise<Absence>
  update(id: number, data: { recordedAt: Date }): Promise<Absence>
  deleteByUniqueCodeAndTypeAndDate(
    uniqueCode: string,
    type: AttendanceType,
    date: Date
  ): Promise<boolean>
  deleteAllByUniqueCode(uniqueCode: string): Promise<void>
  getAttendanceSummary(
    date?: Date,
    className?: string,
    isWeekFilter?: boolean
  ): Promise<AttendanceSummary[]>
  refreshMaterializedView(): Promise<void>
}

/**
 * Interface for student repository
 */
export interface StudentRepository {
  findByUniqueCode(uniqueCode: string): Promise<Student | null>
}

/**
 * Interface for cache service
 */
export interface CacheService {
  get(key: string): Promise<string | null>
  set(key: string, value: string, ttlSeconds: number): Promise<void>
  del(key: string): Promise<void>
}

/**
 * Absence use cases
 */
export class AbsenceUseCases {
  constructor(
    private absenceRepo: AbsenceRepository,
    private studentRepo: StudentRepository,
    private cache: CacheService
  ) {}

  /**
   * Record a new absence
   * @param uniqueCode - The unique code of the student
   * @param type - The type of attendance
   * @param force - Whether to force update an existing attendance record
   */
  async recordAbsence(
    uniqueCode: string,
    type: AttendanceType,
    force: boolean = false
  ): Promise<Absence> {
    // Check if student exists
    const student = await this.studentRepo.findByUniqueCode(uniqueCode)

    if (!student) {
      throw new NotFoundError('Student not found')
    }

    // If this is a dismissal (Pulang) attendance, check if the student has completed Zuhr and Asr prayers
    if (type === AttendanceType.DISMISSAL) {
      await this.validatePrayerAttendance(uniqueCode)
    }

    // Check for duplicate attendance on the same day
    // Use WITA time for consistent date handling
    const today = getCurrentWITATime()
    today.setHours(0, 0, 0, 0)

    // Always check the database directly for existing attendance records
    // This ensures we have the most up-to-date information and prevents race conditions
    const existingAbsence = await this.absenceRepo.findByUniqueCodeAndTypeAndDate(
      uniqueCode,
      type,
      today
    )

    // If there's an existing absence and force is false, throw an error
    if (existingAbsence && !force) {
      throw new DuplicateError('Attendance already recorded for today')
    }

    // If there's an existing absence and force is true, update the existing record
    // Otherwise, create a new record
    let absence: Absence

    if (existingAbsence && force) {
      // Update the existing record with the current WITA time
      absence = await this.absenceRepo.update(existingAbsence.id, {
        recordedAt: getCurrentWITATime(), // Current time in WITA
      })
    } else {
      // Create a new record
      absence = await this.absenceRepo.create({
        uniqueCode,
        type,
        recordedAt: getCurrentWITATime(), // Current time in WITA
      })
    }

    // Invalidate cache for attendance summary
    const todayStr = today.toISOString().split('T')[0]

    // Invalidate all possible cache keys for today
    await this.cache.del(`absence:reports:${todayStr}:all:day`)
    await this.cache.del(`absence:reports:${todayStr}:all:week`)

    // Invalidate the special parameter cache keys
    await this.cache.del('absence:reports:today:all:day')
    await this.cache.del('absence:reports:week:all:week')

    // Invalidate the student's attendance cache
    await this.cache.del(`absence:student:${uniqueCode}:${todayStr}`)

    return absence
  }

  /**
   * Validate that the student has completed Zuhr and Asr prayers before allowing dismissal
   * @param uniqueCode - The unique code of the student
   * @private
   */
  private async validatePrayerAttendance(uniqueCode: string): Promise<void> {
    // Use WITA time for consistent date handling
    const today = getCurrentWITATime()
    today.setHours(0, 0, 0, 0)

    // Get all attendance records for today
    const attendanceRecords = await this.absenceRepo.findByUniqueCodeAndDate(uniqueCode, today)

    // First check if student has an Ijin record for today
    const hasIjinExemption = attendanceRecords.some(record => record.type === AttendanceType.IJIN)

    // If student has Ijin exemption, skip prayer validation
    if (hasIjinExemption) {
      return
    }

    // Check if Zuhr prayer is completed
    const hasZuhrAttendance = attendanceRecords.some(record => record.type === AttendanceType.ZUHR)

    // Check if Asr prayer is completed
    const hasAsrAttendance = attendanceRecords.some(record => record.type === AttendanceType.ASR)

    // If either Zuhr or Asr prayer is not completed, throw an error
    if (!hasZuhrAttendance || !hasAsrAttendance) {
      const missingShalat = []
      if (!hasZuhrAttendance) missingShalat.push('Zuhur')
      if (!hasAsrAttendance) missingShalat.push('Ashar')

      throw new PrayerNotCompletedError(
        `Absensi pulang tidak diizinkan. Shalat ${missingShalat.join(' dan ')} belum dilakukan.`
      )
    }
  }

  /**
   * Check if a student has already recorded attendance for a specific type today
   */
  async checkDuplicateAttendance(uniqueCode: string, type: AttendanceType): Promise<boolean> {
    // Check if student exists
    const student = await this.studentRepo.findByUniqueCode(uniqueCode)

    if (!student) {
      throw new NotFoundError('Student not found')
    }

    // Check for duplicate attendance on the same day
    // Use WITA time for consistent date handling
    const today = getCurrentWITATime()
    today.setHours(0, 0, 0, 0)

    // Always check the database directly for duplicate attendance checks
    // This ensures we have the most up-to-date information and prevents race conditions
    const existingAbsence = await this.absenceRepo.findByUniqueCodeAndTypeAndDate(
      uniqueCode,
      type,
      today
    )

    return !!existingAbsence
  }

  /**
   * Get attendance summary for reporting with report type filtering
   */
  async getAttendanceSummaryByType(
    date?: Date,
    className?: string,
    isWeekFilter?: boolean,
    reportType: 'prayer' | 'school' | 'all' = 'all',
    forceFresh?: boolean
  ): Promise<AttendanceSummary[]> {
    // Get the full attendance summary first
    const fullSummary = await this.getAttendanceSummary(date, className, isWeekFilter, forceFresh)

    // If reportType is 'all', return the full summary
    if (reportType === 'all') {
      return fullSummary
    }

    // Filter the summary based on report type
    return this.filterSummaryByReportType(fullSummary, reportType)
  }

  /**
   * Get attendance summary for reporting (original method)
   */
  async getAttendanceSummary(
    date?: Date,
    className?: string,
    isWeekFilter?: boolean,
    forceFresh?: boolean
  ): Promise<AttendanceSummary[]> {
    // Generate a cache key that includes the isWeekFilter flag
    const dateKey = date
      ? `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
      : isWeekFilter
        ? 'week'
        : 'all'

    // Cache class name, defaulting to 'all' if not provided
    const classKey = className || 'all'

    // Cache key for report type (day or week)
    const typeKey = isWeekFilter ? 'week' : 'day'

    // Complete cache key without timestamp to ensure consistent caching
    const cacheKey = `absence:reports:${dateKey}:${classKey}:${typeKey}`

    // For debugging, check if old cache still exists
    try {
      // Use the old cache key format to check if it exists
      const oldCacheKey = `absence:reports:${dateKey}:${classKey}:${typeKey}`
      const oldCachedData = await this.cache.get(oldCacheKey)
      if (oldCachedData) {
      }
    } catch (error) {
      // Ignore error, just for debugging
    }

    // If forceFresh is true, skip the cache
    if (forceFresh) {
      // Invalidate any existing cache for this data
      try {
        await this.cache.del(`absence:reports:${dateKey}:${classKey}:${typeKey}`)
      } catch (cacheError) {
        console.error('Error invalidating cache:', cacheError)
      }
    }

    // Try to get data from cache first if not forcing fresh data
    if (!forceFresh) {
      try {
        const cachedData = await this.cache.get(cacheKey)
        if (cachedData) {
          console.log(`Cache hit for ${cacheKey}`)
          return JSON.parse(cachedData)
        }
        console.log(`Cache miss for ${cacheKey}`)
      } catch (cacheError) {
        console.error('Error reading from cache:', cacheError)
      }
    }

    // If we reach here, either cache miss or forceFresh is true
    console.log('Fetching data from database')

    // Use the efficient method for both daily and weekly reports
    const rawSummary = await this.absenceRepo.getAttendanceSummary(date, className, isWeekFilter)

    let summary
    if (isWeekFilter) {
      // For weekly reports, transform the data to the format expected by the frontend
      summary = this.transformToWeeklyFormat(rawSummary)
    } else {
      // For daily reports, use the data as-is
      summary = rawSummary
    }

    // Cache the result for 5 minutes (300 seconds)
    try {
      await this.cache.set(cacheKey, JSON.stringify(summary), 300)
      console.log(`Cached data at ${cacheKey} for 5 minutes`)
    } catch (cacheError) {
      console.error('Error setting cache:', cacheError)
    }

    return summary
  }

  /**
   * Refresh the materialized view for attendance summary
   */
  async refreshAttendanceSummary(): Promise<void> {
    await this.absenceRepo.refreshMaterializedView()

    // Invalidate all cache keys for attendance summary
    // In a real implementation, we would use a pattern-based deletion
    await this.cache.del('absence:reports:all:all:day')
    await this.cache.del('absence:reports:all:all:week')

    // Invalidate today's cache
    const todayStr = new Date().toISOString().split('T')[0]
    await this.cache.del(`absence:reports:${todayStr}:all:day`)
    await this.cache.del(`absence:reports:${todayStr}:all:week`)

    // Invalidate the special parameter cache keys
    await this.cache.del('absence:reports:today:all:day')
    await this.cache.del('absence:reports:week:all:week')
  }

  /**
   * Delete an attendance record
   * @param uniqueCode - The unique code of the student
   * @param type - The type of attendance to delete
   * @param date - The date of the attendance record
   */
  async deleteAttendance(uniqueCode: string, type: AttendanceType, date: Date): Promise<boolean> {
    try {
      // Check if student exists
      try {
        const student = await this.studentRepo.findByUniqueCode(uniqueCode)

        if (!student) {
          throw new NotFoundError('Student not found')
        }
      } catch (error) {
        console.error('Error finding student:', error)
        // Continue with deletion even if student is not found
        // This allows deleting records for users that might have been converted to admins
      }

      // Ensure we have a valid date
      if (!date || isNaN(date.getTime())) {
        console.error('Invalid date provided to deleteAttendance:', date)
        throw new Error('Invalid date')
      }

      // Delete the attendance record
      const deleted = await this.absenceRepo.deleteByUniqueCodeAndTypeAndDate(
        uniqueCode,
        type,
        date
      )

      if (!deleted) {
        return false // Record not found or deletion failed
      }

      // Invalidate cache for attendance summary and student attendance
      const dateStr = date.toISOString().split('T')[0] // Get YYYY-MM-DD part

      // Invalidate all possible cache keys for this date
      await this.cache.del(`absence:reports:${dateStr}:all:day`)
      await this.cache.del(`absence:reports:${dateStr}:all:week`)
      await this.cache.del(`absence:student:${uniqueCode}:${dateStr}`)

      // Also invalidate today's cache
      const todayStr = new Date().toISOString().split('T')[0]
      await this.cache.del(`absence:reports:${todayStr}:all:day`)
      await this.cache.del(`absence:reports:${todayStr}:all:week`)

      // Invalidate the "all" cache keys for reports
      await this.cache.del('absence:reports:all:all:day')
      await this.cache.del('absence:reports:all:all:week')

      // Invalidate the special parameter cache keys
      await this.cache.del('absence:reports:today:all:day')
      await this.cache.del('absence:reports:week:all:week')

      return true
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error // Re-throw NotFoundError to be handled by the API
      }
      return false
    }
  }

  /**
   * Get all attendance records for a student on a specific day
   */
  async getAttendanceForDay(uniqueCode: string, date: Date): Promise<Absence[]> {
    try {
      // Validate inputs
      if (!uniqueCode) {
        console.error('Invalid uniqueCode provided to getAttendanceForDay')
        return []
      }

      if (!date || isNaN(date.getTime())) {
        console.error('Invalid date provided to getAttendanceForDay:', date)
        // Create a fallback date (today)
        date = new Date()
        date.setHours(0, 0, 0, 0)
      }

      // Check if student exists
      const student = await this.studentRepo.findByUniqueCode(uniqueCode)

      if (!student) {
        console.warn(`Student with uniqueCode ${uniqueCode} not found`)
        return []
      }

      // Get the date string for cache key
      const dateStr = date.toISOString().split('T')[0] // Get YYYY-MM-DD part
      const cacheKey = `absence:student:${uniqueCode}:${dateStr}`

      let attendanceRecords: Absence[] = []
      let useCache = true

      // Try to get from cache first
      try {
        const cachedData = await this.cache.get(cacheKey)

        if (cachedData) {
          attendanceRecords = JSON.parse(cachedData)
        } else {
          useCache = false
        }
      } catch (cacheError) {
        console.error('Error getting data from cache:', cacheError)
        useCache = false
      }

      // If not in cache or there was an error, get from database
      if (!useCache) {
        attendanceRecords = await this.absenceRepo.findByUniqueCodeAndDate(uniqueCode, date)

        // Cache the result for 1 minute
        try {
          await this.cache.set(cacheKey, JSON.stringify(attendanceRecords), 60)
        } catch (cacheError) {
          console.error('Error setting data in cache:', cacheError)
          // Continue execution to return data from database
        }
      }

      return attendanceRecords
    } catch (error) {
      console.error('Error in getAttendanceForDay:', error)
      return []
    }
  }

  /**
   * Transform efficient backend data (multiple records per student)
   * into frontend format (one record per student with weeklyRecords array)
   */
  private transformToWeeklyFormat(rawSummary: any[]): any[] {
    // Group records by student
    const studentMap = new Map<string, any>()

    for (const record of rawSummary) {
      const { uniqueCode, name, className } = record

      if (!studentMap.has(uniqueCode)) {
        studentMap.set(uniqueCode, {
          uniqueCode,
          name,
          className,
          weeklyRecords: [],
          // Summary flags (will be set to true if any day has attendance)
          zuhr: false,
          asr: false,
          dismissal: false,
          ijin: false,
          summaryDate: record.summaryDate, // Use the actual date, will be updated to latest
          latestDate: record.summaryDate, // Track the latest date for this student
        })
      }

      const student = studentMap.get(uniqueCode)!

      // Format date using WITA timezone (consistent with detailed method)
      const formattedDate = record.summaryDate.toLocaleDateString('id-ID', {
        day: '2-digit',
        month: 'short',
        year: 'numeric',
        timeZone: 'Asia/Makassar',
      })

      // Add this day's record to weeklyRecords
      student.weeklyRecords.push({
        date: formattedDate,
        zuhr: record.zuhr,
        zuhrTime: record.zuhr ? '✓' : '',
        asr: record.asr,
        asrTime: record.asr ? '✓' : '',
        dismissal: record.dismissal,
        dismissalTime: record.dismissal ? '✓' : '',
        ijin: record.ijin,
        ijinTime: record.ijin ? '✓' : '',
        rawDate: record.summaryDate,
        isEmpty: !record.zuhr && !record.asr && !record.dismissal && !record.ijin,
      })

      // Update summary flags
      if (record.zuhr) student.zuhr = true
      if (record.asr) student.asr = true
      if (record.dismissal) student.dismissal = true
      if (record.ijin) student.ijin = true

      // Update to the latest date for this student
      if (record.summaryDate > student.latestDate) {
        student.latestDate = record.summaryDate
        student.summaryDate = record.summaryDate
      }
    }

    // Convert map to array and sort weeklyRecords by date (newest first)
    const result = Array.from(studentMap.values()).map(student => {
      const { latestDate, ...studentWithoutLatestDate } = student
      return {
        ...studentWithoutLatestDate,
        weeklyRecords: student.weeklyRecords.sort(
          (a: any, b: any) => b.rawDate.getTime() - a.rawDate.getTime()
        ),
      }
    })

    console.log(
      `Transformed ${rawSummary.length} raw records into ${result.length} student records for weekly view`
    )
    return result
  }

  /**
   * Filter attendance summary by report type
   */
  private filterSummaryByReportType(
    summary: AttendanceSummary[],
    reportType: 'prayer' | 'school'
  ): AttendanceSummary[] {
    return summary.map(record => {
      if (reportType === 'prayer') {
        // Prayer report: Keep only Zuhr, Asr, Ijin, Pulang
        const filteredRecord = {
          ...record,
          // Keep prayer-related fields
          zuhr: record.zuhr,
          zuhrTime: record.zuhrTime,
          asr: record.asr,
          asrTime: record.asrTime,
          dismissal: record.dismissal,
          dismissalTime: record.dismissalTime,
          ijin: record.ijin,
          ijinTime: record.ijinTime,
          // Clear school attendance fields
          entry: false,
          entryTime: null,
          lateEntry: false,
          lateEntryTime: null,
          excusedAbsence: false,
          excusedAbsenceTime: null,
          temporaryLeave: false,
          temporaryLeaveTime: null,
          returnFromLeave: false,
          returnFromLeaveTime: null,
          sick: false,
          sickTime: null,
        }

        // Filter weekly records if they exist
        if (record.weeklyRecords) {
          filteredRecord.weeklyRecords = record.weeklyRecords.map((weekRecord: any) => ({
            ...weekRecord,
            // Keep prayer fields
            zuhr: weekRecord.zuhr,
            zuhrTime: weekRecord.zuhrTime,
            asr: weekRecord.asr,
            asrTime: weekRecord.asrTime,
            dismissal: weekRecord.dismissal,
            dismissalTime: weekRecord.dismissalTime,
            ijin: weekRecord.ijin,
            ijinTime: weekRecord.ijinTime,
            // Clear school fields
            entry: false,
            entryTime: null,
            lateEntry: false,
            lateEntryTime: null,
            excusedAbsence: false,
            excusedAbsenceTime: null,
            temporaryLeave: false,
            temporaryLeaveTime: null,
            returnFromLeave: false,
            returnFromLeaveTime: null,
            sick: false,
            sickTime: null,
          }))
        }

        return filteredRecord
      } else if (reportType === 'school') {
        // School report: Keep Entry, Late Entry, Excused Absence, Temporary Leave, Return from Leave, Sick, Pulang
        const filteredRecord = {
          ...record,
          // Clear prayer-specific fields (except Pulang which is shared)
          zuhr: false,
          zuhrTime: null,
          asr: false,
          asrTime: null,
          ijin: false,
          ijinTime: null,
          // Keep school attendance fields and Pulang
          dismissal: record.dismissal,
          dismissalTime: record.dismissalTime,
          entry: record.entry || false,
          entryTime: record.entryTime || null,
          lateEntry: record.lateEntry || false,
          lateEntryTime: record.lateEntryTime || null,
          excusedAbsence: record.excusedAbsence || false,
          excusedAbsenceTime: record.excusedAbsenceTime || null,
          temporaryLeave: record.temporaryLeave || false,
          temporaryLeaveTime: record.temporaryLeaveTime || null,
          returnFromLeave: record.returnFromLeave || false,
          returnFromLeaveTime: record.returnFromLeaveTime || null,
          sick: record.sick || false,
          sickTime: record.sickTime || null,
        }

        // Filter weekly records if they exist
        if (record.weeklyRecords) {
          filteredRecord.weeklyRecords = record.weeklyRecords.map((weekRecord: any) => ({
            ...weekRecord,
            // Clear prayer fields
            zuhr: false,
            zuhrTime: null,
            asr: false,
            asrTime: null,
            ijin: false,
            ijinTime: null,
            // Keep school fields and Pulang
            dismissal: weekRecord.dismissal,
            dismissalTime: weekRecord.dismissalTime,
            entry: weekRecord.entry || false,
            entryTime: weekRecord.entryTime || null,
            lateEntry: weekRecord.lateEntry || false,
            lateEntryTime: weekRecord.lateEntryTime || null,
            excusedAbsence: weekRecord.excusedAbsence || false,
            excusedAbsenceTime: weekRecord.excusedAbsenceTime || null,
            temporaryLeave: weekRecord.temporaryLeave || false,
            temporaryLeaveTime: weekRecord.temporaryLeaveTime || null,
            returnFromLeave: weekRecord.returnFromLeave || false,
            returnFromLeaveTime: weekRecord.returnFromLeaveTime || null,
            sick: weekRecord.sick || false,
            sickTime: weekRecord.sickTime || null,
          }))
        }

        return filteredRecord
      }

      return record
    })
  }
}
