import { Student } from '../entities/student'
import { Admin } from '../entities/admin'
import { NotFoundError, ValidationError, AuthenticationError } from '../errors'
import { v4 as uuidv4 } from 'uuid'
import {
  generateToken,
  generateRefreshToken,
  hashPassword,
  comparePassword,
} from '@/lib/utils/auth'
import { serverConfig } from '@/lib/config'
import crypto from 'crypto'
import {
  generateOTP,
  validateRateLimit,
  trackFailedAttempt,
  resetFailedAttempts,
  formatPhoneNumber,
} from '@/lib/utils/otp'

/**
 * Interface for student repository
 */
export interface StudentRepository {
  findByUniqueCode(uniqueCode: string): Promise<Student | null>
  findById(id: number): Promise<Student | null>
  findByUsername(username: string): Promise<Student | null>
  findByWhatsApp(whatsapp: string): Promise<Student | null>
  verifyPassword(student: Student, password: string): Promise<boolean>
  updateWhatsApp(id: number, whatsapp: string): Promise<Student>
  updateNIS(id: number, nis: string): Promise<Student>
  updateCredentials(
    id: number,
    username?: string | null,
    newPassword?: string | null
  ): Promise<Student>
}

/**
 * Interface for admin repository
 */
export interface AdminRepository {
  findByUsername(username: string): Promise<Admin | null>
  findById(id: number): Promise<Admin | null>
  verifyPassword(admin: Admin, password: string): Promise<boolean>
}

/**
 * Interface for cache service
 */
export interface CacheService {
  get(key: string): Promise<string | null>
  set(key: string, value: string, ttlSeconds: number): Promise<void>
  del(key: string): Promise<void>
}

/**
 * Authentication use cases
 */
export class AuthUseCases {
  constructor(
    private studentRepo: StudentRepository,
    private adminRepo: AdminRepository,
    private cache: CacheService,
    private jwtSecret: string
  ) {}

  /**
   * Login an admin with username and password
   */
  async loginAdmin(
    username: string,
    password: string
  ): Promise<{ token: string; refreshToken: string; admin: Admin }> {
    const admin = await this.adminRepo.findByUsername(username)

    if (!admin) {
      throw new NotFoundError('Admin not found')
    }

    const isPasswordValid = await this.adminRepo.verifyPassword(admin, password)

    if (!isPasswordValid) {
      throw new AuthenticationError('Invalid password')
    }

    // Generate JWT token and refresh token
    const token = this.generateToken(
      admin.id,
      admin.role as 'admin' | 'super_admin' | 'teacher' | 'receptionist'
    )
    const refreshToken = this.generateRefreshToken(admin.id)

    // Store refresh token in cache
    await this.cache.set(`auth:refresh:${admin.id}`, refreshToken, 7 * 24 * 60 * 60) // 7 days

    return { token, refreshToken, admin }
  }

  /**
   * Login a student with username and password
   */
  async loginStudentWithCredentials(
    username: string,
    password: string
  ): Promise<{ token: string; refreshToken: string; student: Student }> {
    const student = await this.studentRepo.findByUsername(username)

    if (!student) {
      throw new NotFoundError('Student not found')
    }

    const isPasswordValid = await this.studentRepo.verifyPassword(student, password)

    if (!isPasswordValid) {
      throw new AuthenticationError('Invalid password')
    }

    // Generate JWT token and refresh token
    const token = this.generateToken(student.id, 'student')
    const refreshToken = this.generateRefreshToken(student.id)

    // Store refresh token in cache
    await this.cache.set(`auth:refresh:${student.id}`, refreshToken, 7 * 24 * 60 * 60) // 7 days

    return { token, refreshToken, student }
  }

  /**
   * Send WhatsApp OTP for verification
   */
  async sendWhatsAppOtp(whatsapp: string, studentId: number): Promise<void> {
    const student = await this.studentRepo.findById(studentId)

    if (!student) {
      throw new NotFoundError('Student not found')
    }

    // Normalize and format WhatsApp number
    const normalizedWhatsapp = formatPhoneNumber(whatsapp)

    // Check rate limit before sending OTP
    const isWithinRateLimit = await validateRateLimit(normalizedWhatsapp, this.cache)
    if (!isWithinRateLimit) {
      console.error(`Rate limit exceeded for WhatsApp number: ${normalizedWhatsapp}`)
      throw new ValidationError('Too many OTP requests. Please try again later.')
    }

    // Generate 6-digit OTP
    const otp = generateOTP()

    // Store OTP in cache with 5-minute expiration
    const cacheKey = `otp:${normalizedWhatsapp}`
    await this.cache.set(cacheKey, otp, 5 * 60)

    // Send OTP via n8n webhook
    if (serverConfig.webhooks.n8nWhatsapp) {
      try {
        const response = await fetch(serverConfig.webhooks.n8nWhatsapp, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            phoneNumber: normalizedWhatsapp,
            message: `Kode OTP Anda untuk ShalatYuk adalah: *${otp}*. Kode ini berlaku selama 5 menit.`,
          }),
        })

        if (!response.ok) {
          console.error('Failed to send OTP via webhook:', await response.text())
          throw new Error('WhatsApp service is currently unavailable. Please try again later.')
        } else {
        }
      } catch (error) {
        console.error('Error sending OTP via webhook:', error)
        throw new Error(
          'Failed to send WhatsApp message. Please check your connection and try again.'
        )
      }
    } else {
      // Fallback to logging if webhook URL is not configured
    }
  }

  /**
   * Verify WhatsApp OTP and update student's WhatsApp number
   */
  async verifyWhatsAppOtp(whatsapp: string, otp: string, studentId: number): Promise<void> {
    // Normalize WhatsApp number
    const normalizedWhatsapp = formatPhoneNumber(whatsapp)

    // Check if there have been too many failed attempts
    const attemptsAllowed = await trackFailedAttempt(normalizedWhatsapp, this.cache)
    if (!attemptsAllowed) {
      console.error(`Too many failed OTP attempts for ${normalizedWhatsapp}`)
      throw new ValidationError('Too many failed attempts. Please request a new OTP.')
    }

    const cacheKey = `otp:${normalizedWhatsapp}`
    const storedOtp = await this.cache.get(cacheKey)

    if (!storedOtp) {
      console.error(`No OTP found in cache for ${normalizedWhatsapp}`)
      throw new ValidationError('OTP not found or expired. Please request a new OTP.')
    }

    if (storedOtp !== otp) {
      console.error(`OTP mismatch: Provided ${otp}, Stored: ${storedOtp}`)
      throw new ValidationError('Invalid OTP. Please check and try again.')
    }

    // Update student's WhatsApp number
    const updatedStudent = await this.studentRepo.updateWhatsApp(studentId, normalizedWhatsapp)

    // Reset failed attempts counter after successful verification
    await resetFailedAttempts(normalizedWhatsapp, this.cache)

    // Delete OTP from cache
    await this.cache.del(cacheKey)
  }

  /**
   * Logout a user by removing their refresh token
   */
  async logout(userId: number): Promise<void> {
    await this.cache.del(`auth:refresh:${userId}`)
  }

  /**
   * Send password reset OTP via WhatsApp
   */
  async sendPasswordResetOTP(whatsapp: string): Promise<void> {
    // Normalize WhatsApp number
    const normalizedWhatsapp = formatPhoneNumber(whatsapp)

    // Find student by WhatsApp number
    const student = await this.studentRepo.findByWhatsApp(normalizedWhatsapp)
    if (!student) {
      throw new NotFoundError('No account found with this WhatsApp number')
    }

    // Check rate limit before sending OTP
    const isWithinRateLimit = await validateRateLimit(normalizedWhatsapp, this.cache)
    if (!isWithinRateLimit) {
      console.error(`Rate limit exceeded for password reset: ${normalizedWhatsapp}`)
      throw new ValidationError('Too many password reset requests. Please try again later.')
    }

    // Generate 6-digit OTP
    const otp = generateOTP()

    // Store OTP in cache with 5-minute expiration and special prefix for password reset
    const cacheKey = `password_reset:otp:${normalizedWhatsapp}`
    await this.cache.set(cacheKey, otp, 5 * 60)

    // Send OTP via n8n webhook
    if (serverConfig.webhooks.n8nWhatsapp) {
      try {
        const response = await fetch(serverConfig.webhooks.n8nWhatsapp, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            phoneNumber: normalizedWhatsapp,
            message: `Kode Reset Password ShalatYuk: *${otp}*. Kode ini berlaku selama 5 menit. Jangan bagikan ke siapapun.`,
          }),
        })

        if (!response.ok) {
          console.error('Failed to send OTP via webhook:', await response.text())
          throw new Error('WhatsApp service is currently unavailable. Please try again later.')
        } else {
        }
      } catch (error) {
        console.error('Error sending OTP via webhook:', error)
        throw new Error(
          'Failed to send WhatsApp message. Please check your connection and try again.'
        )
      }
    } else {
      // Fallback to logging if webhook URL is not configured
    }
  }

  /**
   * Send password reset email
   */
  async sendPasswordResetEmail(email: string): Promise<void> {
    // Find student by email
    const student = await this.studentRepo.findByGoogleEmail(email)
    if (!student) {
      throw new NotFoundError('No account found with this email')
    }

    // Generate a secure token
    const resetToken = crypto.randomBytes(32).toString('hex')

    // Store token in cache with 1-hour expiration
    const cacheKey = `password_reset:token:${resetToken}`
    await this.cache.set(cacheKey, student.id.toString(), 60 * 60)

    // Generate reset URL
    const resetUrl = `${serverConfig.appUrl}/student/reset-password?token=${resetToken}`

    // For now, we'll just log the URL since email sending is beyond the scope

    // In a real implementation, you would use an email service here
  }

  /**
   * Reset password with OTP (for WhatsApp verification)
   */
  async resetPasswordWithOTP(whatsapp: string, otp: string, newPassword: string): Promise<void> {
    // Normalize WhatsApp number
    const normalizedWhatsapp = formatPhoneNumber(whatsapp)

    // Check if there have been too many failed attempts
    const attemptsAllowed = await trackFailedAttempt(normalizedWhatsapp, this.cache)
    if (!attemptsAllowed) {
      console.error(`Too many failed password reset attempts for ${normalizedWhatsapp}`)
      throw new ValidationError('Too many failed attempts. Please request a new OTP.')
    }

    // Verify OTP
    const cacheKey = `password_reset:otp:${normalizedWhatsapp}`
    const storedOtp = await this.cache.get(cacheKey)

    if (!storedOtp || storedOtp !== otp) {
      console.error(`Invalid password reset OTP. Provided: ${otp}, Stored: ${storedOtp || 'none'}`)
      throw new ValidationError('Invalid OTP or OTP expired. Please request a new one.')
    }

    // Find student by WhatsApp
    const student = await this.studentRepo.findByWhatsApp(normalizedWhatsapp)
    if (!student) {
      throw new NotFoundError('Student not found')
    }

    // Update password
    const hashedPassword = await hashPassword(newPassword)
    await this.studentRepo.updateCredentials(student.id, undefined, newPassword)

    // Reset failed attempts counter after successful verification
    await resetFailedAttempts(normalizedWhatsapp, this.cache)

    // Delete OTP from cache
    await this.cache.del(cacheKey)
  }

  /**
   * Reset password with token (for email verification)
   */
  async resetPasswordWithToken(token: string, newPassword: string): Promise<void> {
    // Verify token
    const cacheKey = `password_reset:token:${token}`
    const studentIdStr = await this.cache.get(cacheKey)

    if (!studentIdStr) {
      throw new ValidationError('Invalid or expired reset token')
    }

    const studentId = parseInt(studentIdStr, 10)

    // Find student by ID
    const student = await this.studentRepo.findById(studentId)
    if (!student) {
      throw new NotFoundError('Student not found')
    }

    // Update password
    await this.studentRepo.updateCredentials(student.id, undefined, newPassword)

    // Delete token from cache
    await this.cache.del(cacheKey)
  }

  /**
   * Change student's password with old password verification
   */
  async changeStudentPassword(
    studentId: number,
    oldPassword: string,
    newPassword: string
  ): Promise<void> {
    const student = await this.studentRepo.findById(studentId)

    if (!student) {
      throw new NotFoundError('Student not found')
    }

    // Verify the old password first
    const isPasswordValid = await this.studentRepo.verifyPassword(student, oldPassword)

    if (!isPasswordValid) {
      throw new AuthenticationError('Current password is incorrect')
    }

    // Update the student's password only, don't touch the username
    await this.studentRepo.updateCredentials(studentId, undefined, newPassword)

    // Invalidate any existing refresh tokens for security
    await this.cache.del(`auth:refresh:${studentId}`)
  }

  /**
   * Generate a JWT token
   */
  private generateToken(
    userId: number,
    role: 'student' | 'admin' | 'super_admin' | 'teacher' | 'receptionist'
  ): string {
    return generateToken({ id: userId, role }, this.jwtSecret)
  }

  /**
   * Generate a refresh token
   */
  private generateRefreshToken(userId: number): string {
    return generateRefreshToken({ id: userId }, this.jwtSecret)
  }
}
