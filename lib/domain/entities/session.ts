/**
 * Session entity representing an active user session in the system
 */
export interface SessionData {
  /** Unique session identifier */
  sessionId: string

  /** User ID from the database */
  userId: number

  /** User role */
  role: 'student' | 'admin' | 'super_admin' | 'teacher' | 'receptionist'

  /** Device identifier for single-device enforcement */
  deviceId: string

  /** IP address when session was created */
  ipAddress: string

  /** User agent string for device/browser identification */
  userAgent: string

  /** Timestamp when session was created */
  createdAt: Date

  /** Timestamp when session was last accessed */
  lastAccessedAt: Date

  /** Timestamp when session expires */
  expiresAt: Date

  /** Whether this session is currently active */
  isActive: boolean

  /** Additional metadata about the session */
  metadata?: {
    /** Device type (mobile, desktop, tablet) */
    deviceType?: string

    /** Browser name */
    browser?: string

    /** Operating system */
    os?: string

    /** Location information if available */
    location?: string

    /** Security: Device fingerprint for session validation */
    deviceFingerprint?: string

    /** Security: Session creation timestamp */
    createdAt?: string

    /** Security: Security level indicator */
    securityLevel?: string
  }
}

/**
 * Data required to create a new session
 */
export interface CreateSessionDTO {
  /** User ID from the database */
  userId: number

  /** User role */
  role: 'student' | 'admin' | 'super_admin' | 'teacher' | 'receptionist'

  /** Device identifier for single-device enforcement */
  deviceId: string

  /** IP address when session was created */
  ipAddress: string

  /** User agent string for device/browser identification */
  userAgent: string

  /** Session duration in seconds (defaults to 1 hour) */
  durationSeconds?: number

  /** Additional metadata about the session */
  metadata?: {
    deviceType?: string
    browser?: string
    os?: string
    location?: string
    /** Security: Device fingerprint for session validation */
    deviceFingerprint?: string
    /** Security: Session creation timestamp */
    createdAt?: string
    /** Security: Security level indicator */
    securityLevel?: string
  }
}

/**
 * Session summary for listing and monitoring
 */
export interface SessionSummary {
  /** Unique session identifier */
  sessionId: string

  /** User ID from the database */
  userId: number

  /** User name for display */
  userName: string

  /** User role */
  role: 'student' | 'admin' | 'super_admin' | 'teacher' | 'receptionist'

  /** Device identifier */
  deviceId: string

  /** IP address */
  ipAddress: string

  /** Device type for display */
  deviceType: string

  /** Browser name for display */
  browser: string

  /** Timestamp when session was created */
  createdAt: Date

  /** Timestamp when session was last accessed */
  lastAccessedAt: Date

  /** Timestamp when session expires */
  expiresAt: Date

  /** Whether this session is currently active */
  isActive: boolean
}

/**
 * Session filter criteria for listing sessions
 */
export interface SessionFilter {
  /** Filter by user ID */
  userId?: number

  /** Filter by user role */
  role?: 'student' | 'admin' | 'super_admin'

  /** Filter by active status */
  isActive?: boolean

  /** Filter by device ID */
  deviceId?: string

  /** Filter sessions created after this date */
  createdAfter?: Date

  /** Filter sessions created before this date */
  createdBefore?: Date

  /** Limit number of results */
  limit?: number

  /** Offset for pagination */
  offset?: number
}

/**
 * Session validation result
 */
export interface SessionValidationResult {
  /** Whether the session is valid */
  isValid: boolean

  /** Session data if valid */
  session?: SessionData

  /** Error message if invalid */
  error?: string

  /** Whether the session was refreshed */
  refreshed?: boolean
}
