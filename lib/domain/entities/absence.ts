/**
 * Attendance type enum
 */
export enum AttendanceType {
  // Prayer attendance (existing)
  ZUHR = 'Zuhr',
  ASR = 'Asr',
  IJIN = 'Ijin',

  // Departure (existing)
  DISMISSAL = 'Pulang',

  // School attendance (new)
  ENTRY = 'Entry',
  LATE_ENTRY = 'Late Entry',
  EXCUSED_ABSENCE = 'Excused Absence',
  TEMPORARY_LEAVE = 'Temporary Leave',
  RETURN_FROM_LEAVE = 'Return from Leave',
  SICK = 'Sick',
}

/**
 * Absence entity representing a student's attendance record
 */
export interface Absence {
  id: number
  uniqueCode: string // References Student.uniqueCode
  type: AttendanceType
  recordedAt: Date // Timestamp when the attendance was recorded
  createdAt: Date
}

/**
 * Data required to create a new absence record
 */
export interface CreateAbsenceDTO {
  uniqueCode: string
  type: AttendanceType
  recordedAt: Date
}

/**
 * Attendance summary for reporting
 */
export interface AttendanceSummary {
  summaryDate: Date
  uniqueCode: string
  name: string
  className: string
  zuhr: boolean
  asr: boolean
  dismissal: boolean
  ijin: boolean
  updatedAt: Date
}
