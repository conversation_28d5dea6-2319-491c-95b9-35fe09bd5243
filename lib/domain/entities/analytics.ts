/**
 * Analytics Domain Entities
 * Clean Architecture - Domain Layer
 */

/**
 * Prayer attendance analytics data
 */
export interface PrayerAnalytics {
  date: string
  totalStudents: number
  zuhrAttendance: number
  asrAttendance: number
  dismissalAttendance: number
  ijinCount: number
  zuhrPercentage: number
  asrPercentage: number
  dismissalPercentage: number
  ijinPercentage: number
  prayerComplianceRate: number // Overall prayer compliance (0-100)
  attendanceScore: number // Combined attendance score
}

/**
 * Class-based analytics
 */
export interface ClassAnalytics {
  className: string
  totalStudents: number
  presentStudents: number
  absentStudents: number
  attendanceRate: number
  prayerComplianceRate: number
  zuhrRate: number
  asrRate: number
  dismissalRate: number
  ijinRate: number
  trend: 'improving' | 'declining' | 'stable'
  riskLevel: 'low' | 'medium' | 'high'
}

/**
 * Student performance analytics
 */
export interface StudentAnalytics {
  uniqueCode: string
  name: string
  className: string
  attendanceRate: number
  prayerComplianceRate: number
  consistencyScore: number // How consistent is the student (0-100)
  riskScore: number // Risk of poor attendance (0-100)
  trend: 'improving' | 'declining' | 'stable'
  lastAttendance: string | null
  streakDays: number // Consecutive days of good attendance
  totalPrayersAttended: number
  totalPrayersMissed: number
  recommendations: string[]
}

/**
 * Time-based trend data
 */
export interface TrendData {
  date: string
  value: number
  label: string
  change?: number // Percentage change from previous period
  changeType?: 'increase' | 'decrease' | 'stable'
}

/**
 * Dashboard KPI metrics
 */
export interface DashboardKPIs {
  todayAttendance: {
    total: number
    present: number
    absent: number
    percentage: number
    change: number
  }
  prayerMetrics: {
    zuhrAttendance: number
    asrAttendance: number
    overallCompliance: number
    change: number
  }
  weeklyTrend: {
    currentWeek: number
    previousWeek: number
    change: number
    trend: 'up' | 'down' | 'stable'
  }
  alerts: {
    count: number
    critical: number
    warning: number
    info: number
  }
}

/**
 * Attendance heatmap data
 */
export interface HeatmapData {
  date: string
  value: number
  level: 0 | 1 | 2 | 3 | 4 // Intensity level for color coding
  tooltip: string
}

/**
 * Chart data structure for various visualizations
 */
export interface ChartData {
  labels: string[]
  datasets: {
    label: string
    data: number[]
    backgroundColor?: string | string[]
    borderColor?: string
    borderWidth?: number
    fill?: boolean
  }[]
}

/**
 * Analytics filter options
 */
export interface AnalyticsFilters {
  dateRange: {
    start: Date
    end: Date
  }
  classes?: string[]
  attendanceTypes?: ('zuhr' | 'asr' | 'dismissal' | 'ijin')[]
  riskLevels?: ('low' | 'medium' | 'high')[]
  trends?: ('improving' | 'declining' | 'stable')[]
}

/**
 * Export configuration for analytics reports
 */
export interface AnalyticsExportConfig {
  format: 'csv' | 'pdf' | 'excel'
  includeCharts: boolean
  includeRawData: boolean
  includeSummary: boolean
  includeRecommendations: boolean
  dateRange: {
    start: Date
    end: Date
  }
  filters: AnalyticsFilters
}

/**
 * Real-time alert data
 */
export interface AttendanceAlert {
  id: string
  type: 'critical' | 'warning' | 'info'
  title: string
  message: string
  studentCode?: string
  studentName?: string
  className?: string
  timestamp: Date
  isRead: boolean
  actionRequired: boolean
  recommendations?: string[]
}

/**
 * Performance metrics for monitoring
 */
export interface PerformanceMetrics {
  queryTime: number
  cacheHitRate: number
  dataFreshness: number // Minutes since last update
  recordsProcessed: number
  errorRate: number
}

/**
 * Analytics summary for executive dashboard
 */
export interface AnalyticsSummary {
  period: string
  totalStudents: number
  overallAttendanceRate: number
  prayerComplianceRate: number
  classPerformance: ClassAnalytics[]
  topPerformers: StudentAnalytics[]
  atRiskStudents: StudentAnalytics[]
  trends: {
    attendance: TrendData[]
    prayer: TrendData[]
    classes: TrendData[]
  }
  insights: {
    key: string
    value: string | number
    trend: 'positive' | 'negative' | 'neutral'
    description: string
  }[]
  recommendations: {
    priority: 'high' | 'medium' | 'low'
    category: 'attendance' | 'prayer' | 'class' | 'student'
    title: string
    description: string
    actionItems: string[]
  }[]
}

/**
 * Predictive analytics data
 */
export interface PredictiveAnalytics {
  studentCode: string
  studentName: string
  className: string
  riskScore: number // 0-100, higher means more at risk
  riskFactors: string[]
  predictedOutcome: 'success' | 'at_risk' | 'critical'
  confidence: number // 0-100, confidence in prediction
  recommendations: string[]
  interventionSuggestions: string[]
  timeframe: string // When intervention should happen
}
