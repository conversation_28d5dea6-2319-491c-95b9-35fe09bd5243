import { AttendanceType } from '@/lib/domain/entities/absence'
import { canHandleAttendanceType, type UserRole } from '@/lib/config/role-permissions'

/**
 * SECURE: Validate if a user role can handle a specific attendance type
 * Used in API routes to prevent unauthorized attendance recording
 */
export function validateAttendanceTypeAccess(
  userRole: UserRole,
  attendanceType: AttendanceType | string
): boolean {
  return canHandleAttendanceType(userRole, attendanceType)
}

/**
 * SECURE: Get allowed attendance types for a role
 * Used in UI components to show only relevant options
 */
export function getAllowedAttendanceTypes(userRole: UserRole): AttendanceType[] | string[] {
  if (userRole === 'student') {
    return ['qr-display']
  }

  if (userRole === 'admin') {
    return [AttendanceType.ZUHR, AttendanceType.ASR, AttendanceType.DISMISSAL, AttendanceType.IJIN]
  }

  if (userRole === 'super_admin') {
    return [AttendanceType.ZUHR, AttendanceType.ASR, AttendanceType.DISMISSAL, AttendanceType.IJIN]
  }

  if (userRole === 'teacher') {
    return [AttendanceType.ENTRY]
  }

  if (userRole === 'receptionist') {
    return [
      AttendanceType.LATE_ENTRY,
      AttendanceType.EXCUSED_ABSENCE,
      AttendanceType.TEMPORARY_LEAVE,
      AttendanceType.RETURN_FROM_LEAVE,
      AttendanceType.SICK,
    ]
  }

  return []
}

/**
 * SECURE: Get attendance type labels for UI display
 * Maps attendance types to Indonesian labels
 */
export function getAttendanceTypeLabel(attendanceType: AttendanceType | string): string {
  const labels: Record<string, string> = {
    // Prayer attendance
    [AttendanceType.ZUHR]: 'Shalat Zuhur',
    [AttendanceType.ASR]: 'Shalat Ashar',
    [AttendanceType.IJIN]: 'Izin Tidak Shalat',

    // Departure
    [AttendanceType.DISMISSAL]: 'Pulang',

    // School attendance
    [AttendanceType.ENTRY]: 'Masuk',
    [AttendanceType.LATE_ENTRY]: 'Masuk Terlambat',
    [AttendanceType.EXCUSED_ABSENCE]: 'Izin',
    [AttendanceType.TEMPORARY_LEAVE]: 'Izin Sementara',
    [AttendanceType.RETURN_FROM_LEAVE]: 'Kembali dari Izin',
    [AttendanceType.SICK]: 'Sakit',

    // Special
    'qr-display': 'Tampilkan QR Code',
  }

  return labels[attendanceType] || attendanceType
}

/**
 * SECURE: Validate attendance type exists and is valid
 */
export function isValidAttendanceType(type: string): type is AttendanceType {
  return Object.values(AttendanceType).includes(type as AttendanceType)
}
