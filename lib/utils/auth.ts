import jwt, { Secret, SignOptions } from 'jsonwebtoken'
// Import StringValue type from ms package
import type { StringValue } from 'ms'
import bcrypt from 'bcryptjs'

/**
 * Enhanced JWT token payload interface
 */
export interface JWTPayload {
  id: number
  role: 'student' | 'admin' | 'super_admin' | 'teacher' | 'receptionist'
  deviceId?: string
  sessionId?: string
}

/**
 * Verify a JWT token
 * @param token The JWT token to verify
 * @param secret The secret used to sign the token
 * @returns The decoded token payload
 * @throws Error if the token is invalid
 */
export function verifyToken(token: string, secret: Secret): JWTPayload {
  try {
    const decoded = jwt.verify(token, secret) as any

    // Validate required fields
    if (!decoded.id || typeof decoded.id !== 'number') {
      throw new Error('Invalid token: missing or invalid user ID')
    }

    if (
      !decoded.role ||
      !['student', 'admin', 'super_admin', 'teacher', 'receptionist'].includes(decoded.role)
    ) {
      throw new Error(`Invalid role in token: ${decoded.role}`)
    }

    return {
      id: decoded.id,
      role: decoded.role,
      deviceId: decoded.deviceId,
      sessionId: decoded.sessionId,
    }
  } catch (error) {
    // Re-throw specific error if it's our custom one, otherwise generic
    if (
      error instanceof Error &&
      (error.message.startsWith('Invalid role in token') ||
        error.message.startsWith('Invalid token:'))
    ) {
      throw error
    }
    throw new Error('Invalid token')
  }
}

/**
 * Generate a JWT token with enhanced payload
 * @param payload The data to include in the token
 * @param secret The secret to sign the token with
 * @param expiresIn The expiration time for the token
 * @returns The generated JWT token
 */
export function generateToken(
  payload: JWTPayload,
  secret: Secret,
  expiresIn: StringValue | number = '1h'
): string {
  const options: SignOptions = { expiresIn }
  return jwt.sign(payload, secret, options)
}

/**
 * Generate a JWT token with legacy payload (for backward compatibility)
 * @param payload The data to include in the token
 * @param secret The secret to sign the token with
 * @param expiresIn The expiration time for the token
 * @returns The generated JWT token
 */
export function generateLegacyToken(
  payload: { id: number; role: string },
  secret: Secret,
  expiresIn: StringValue | number = '1h'
): string {
  const options: SignOptions = { expiresIn }
  return jwt.sign(payload, secret, options)
}

/**
 * Generate a refresh token
 * @param payload The data to include in the token
 * @param secret The secret to sign the token with
 * @param expiresIn The expiration time for the token
 * @returns The generated refresh token
 */
export function generateRefreshToken(
  payload: { id: number },
  secret: Secret,
  expiresIn: StringValue | number = '7d'
): string {
  const options: SignOptions = { expiresIn }
  return jwt.sign(payload, secret, options)
}

const SALT_ROUNDS = 10

export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, SALT_ROUNDS)
}

export async function comparePassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash)
}
