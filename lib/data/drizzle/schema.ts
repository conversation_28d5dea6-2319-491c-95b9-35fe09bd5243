import {
  pgTable,
  serial,
  varchar,
  timestamp,
  boolean,
  pgEnum,
  integer,
  check,
} from 'drizzle-orm/pg-core'

/**
 * User role enum
 */
export const userRoleEnum = pgEnum('user_role', [
  'student',
  'admin',
  'super_admin',
  'teacher',
  'receptionist',
])

/**
 * Attendance type enum
 */
export const attendanceTypeEnum = pgEnum('attendance_type', [
  'Zuhr',
  'Asr',
  'Pulang',
  'Ijin',
  'Entry',
  'Late Entry',
  'Excused Absence',
  'Temporary Leave',
  'Return from Leave',
  'Sick',
])

/**
 * Classes table
 * Updated to allow flexible class naming (removed class name format constraint)
 */
export const classes = pgTable('classes', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 50 }).notNull().unique(), // Increased length for more flexibility
  createdAt: timestamp('created_at').defaultNow().notNull(),
})

/**
 * Users table
 */
export const users = pgTable('users', {
  id: serial('id').primaryKey(),
  role: userRoleEnum('role').notNull(),
  uniqueCode: varchar('unique_code', { length: 36 }).unique(),
  googleEmail: varchar('google_email', { length: 255 }).unique(),
  nis: varchar('nis', { length: 10 }),
  username: varchar('username', { length: 50 }).unique(),
  name: varchar('name', { length: 100 }).notNull(),
  whatsapp: varchar('whatsapp', { length: 15 }),
  classId: integer('class_id'),
  passwordHash: varchar('password_hash', { length: 255 }),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at'),
})

/**
 * Absences table
 */
export const absences = pgTable('absences', {
  id: serial('id').primaryKey(),
  uniqueCode: varchar('unique_code', { length: 36 })
    .notNull()
    .references(() => users.uniqueCode),
  type: attendanceTypeEnum('type').notNull(),
  recordedAt: timestamp('recorded_at').notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
})

/**
 * SQL to create the attendance_summary materialized view
 *
 * Note: This is raw SQL because Drizzle ORM doesn't directly support materialized views.
 * This SQL needs to be executed separately after the tables are created.
 */
export const createAttendanceSummaryViewSQL = `
CREATE MATERIALIZED VIEW IF NOT EXISTS attendance_summary AS
WITH pivoted AS (
  SELECT
    DATE(recorded_at) AS summary_date,
    unique_code,
    BOOL_OR(CASE WHEN type = 'Zuhr' THEN TRUE ELSE FALSE END) AS zuhr,
    BOOL_OR(CASE WHEN type = 'Asr' THEN TRUE ELSE FALSE END) AS asr,
    BOOL_OR(CASE WHEN type = 'Pulang' THEN TRUE ELSE FALSE END) AS pulang,
    BOOL_OR(CASE WHEN type = 'Ijin' THEN TRUE ELSE FALSE END) AS ijin,
    MAX(recorded_at) AS last_updated
  FROM absences
  GROUP BY DATE(recorded_at), unique_code
)
SELECT
  p.summary_date,
  u.unique_code,
  u.name,
  c.name AS class_name,
  p.zuhr,
  p.asr,
  p.pulang,
  p.ijin,
  p.last_updated AS updated_at
FROM pivoted p
JOIN users u ON p.unique_code = u.unique_code
JOIN classes c ON u.class_id = c.id
WITH NO DATA;

CREATE INDEX IF NOT EXISTS idx_attendance_summary_date ON attendance_summary(summary_date);
`

/**
 * SQL to refresh the attendance_summary materialized view
 */
export const refreshAttendanceSummaryViewSQL = `
REFRESH MATERIALIZED VIEW attendance_summary;
`

/**
 * SQL to fix constraint chk_role_data to allow student without google_email
 * and enforce username and password requirements
 */
export const fixConstraintSQL = `
-- Drop existing constraint
ALTER TABLE users DROP CONSTRAINT IF EXISTS chk_role_data;

-- Add updated constraint
ALTER TABLE users ADD CONSTRAINT chk_role_data CHECK (
  (role = 'student' AND unique_code IS NOT NULL AND username IS NOT NULL AND password_hash IS NOT NULL) OR 
  (role = 'admin' AND google_email IS NULL AND unique_code IS NULL AND username IS NOT NULL AND password_hash IS NOT NULL)
);

-- Create index for username-based login
CREATE INDEX IF NOT EXISTS idx_users_username_role ON users(username, role);
`
