/**
 * Analytics Repository Implementation
 * Clean Architecture - Infrastructure Layer
 */

import { db, schema } from '../drizzle/db'
import { eq, and, gte, lte, sql, desc, asc } from 'drizzle-orm'
import {
  PrayerAnalytics,
  ClassAnalytics,
  StudentAnalytics,
  DashboardKPIs,
  TrendData,
  HeatmapData,
  AnalyticsFilters,
  AttendanceAlert,
  PredictiveAnalytics,
} from '../../domain/entities/analytics'
import { IAnalyticsRepository } from '../../domain/usecases/analytics'

/**
 * Analytics repository implementation using Drizzle ORM
 */
export class AnalyticsRepository implements IAnalyticsRepository {
  /**
   * Get prayer attendance analytics
   */
  async getPrayerAnalytics(filters: AnalyticsFilters): Promise<PrayerAnalytics[]> {
    const { dateRange } = filters

    // Query attendance summary for prayer data
    const results = await db
      .select({
        date: sql<string>`DATE(${schema.absences.recordedAt})`,
        totalStudents: sql<number>`COUNT(DISTINCT ${schema.absences.uniqueCode})`,
        zuhrCount: sql<number>`COUNT(CASE WHEN ${schema.absences.type} = 'Zuhr' THEN 1 END)`,
        asrCount: sql<number>`COUNT(CASE WHEN ${schema.absences.type} = 'Asr' THEN 1 END)`,
        dismissalCount: sql<number>`COUNT(CASE WHEN ${schema.absences.type} = 'Pulang' THEN 1 END)`,
        ijinCount: sql<number>`COUNT(CASE WHEN ${schema.absences.type} = 'Ijin' THEN 1 END)`,
      })
      .from(schema.absences)
      .innerJoin(schema.users, eq(schema.absences.uniqueCode, schema.users.uniqueCode))
      .where(
        and(
          gte(schema.absences.recordedAt, dateRange.start),
          lte(schema.absences.recordedAt, dateRange.end),
          ...(filters.classes ? [sql`${schema.users.classId} IN (SELECT id FROM classes WHERE name = ANY(${filters.classes}))`] : [])
        )
      )
      .groupBy(sql`DATE(${schema.absences.recordedAt})`)
      .orderBy(asc(sql`DATE(${schema.absences.recordedAt})`))

    return results.map(row => {
      const zuhrPercentage = row.totalStudents > 0 ? Math.round((row.zuhrCount / row.totalStudents) * 100) : 0
      const asrPercentage = row.totalStudents > 0 ? Math.round((row.asrCount / row.totalStudents) * 100) : 0
      const dismissalPercentage = row.totalStudents > 0 ? Math.round((row.dismissalCount / row.totalStudents) * 100) : 0
      const ijinPercentage = row.totalStudents > 0 ? Math.round((row.ijinCount / row.totalStudents) * 100) : 0
      
      // Calculate prayer compliance rate (excluding ijin students)
      const eligibleStudents = row.totalStudents - row.ijinCount
      const prayerComplianceRate = eligibleStudents > 0 
        ? Math.round(((row.zuhrCount + row.asrCount) / (eligibleStudents * 2)) * 100)
        : 0

      // Calculate overall attendance score
      const attendanceScore = Math.round((zuhrPercentage + asrPercentage + dismissalPercentage) / 3)

      return {
        date: row.date,
        totalStudents: row.totalStudents,
        zuhrAttendance: row.zuhrCount,
        asrAttendance: row.asrCount,
        dismissalAttendance: row.dismissalCount,
        ijinCount: row.ijinCount,
        zuhrPercentage,
        asrPercentage,
        dismissalPercentage,
        ijinPercentage,
        prayerComplianceRate,
        attendanceScore,
      }
    })
  }

  /**
   * Get class performance analytics
   */
  async getClassAnalytics(filters: AnalyticsFilters): Promise<ClassAnalytics[]> {
    const { dateRange } = filters

    const results = await db
      .select({
        className: schema.classes.name,
        totalStudents: sql<number>`COUNT(DISTINCT ${schema.users.uniqueCode})`,
        zuhrCount: sql<number>`COUNT(CASE WHEN ${schema.absences.type} = 'Zuhr' THEN 1 END)`,
        asrCount: sql<number>`COUNT(CASE WHEN ${schema.absences.type} = 'Asr' THEN 1 END)`,
        dismissalCount: sql<number>`COUNT(CASE WHEN ${schema.absences.type} = 'Pulang' THEN 1 END)`,
        ijinCount: sql<number>`COUNT(CASE WHEN ${schema.absences.type} = 'Ijin' THEN 1 END)`,
      })
      .from(schema.users)
      .innerJoin(schema.classes, eq(schema.users.classId, schema.classes.id))
      .leftJoin(
        schema.absences,
        and(
          eq(schema.absences.uniqueCode, schema.users.uniqueCode),
          gte(schema.absences.recordedAt, dateRange.start),
          lte(schema.absences.recordedAt, dateRange.end)
        )
      )
      .where(eq(schema.users.role, 'student'))
      .groupBy(schema.classes.id, schema.classes.name)
      .orderBy(asc(schema.classes.name))

    return results.map(row => {
      const presentStudents = Math.max(row.zuhrCount, row.asrCount, row.dismissalCount)
      const absentStudents = row.totalStudents - presentStudents
      const attendanceRate = row.totalStudents > 0 ? Math.round((presentStudents / row.totalStudents) * 100) : 0
      
      const eligibleStudents = row.totalStudents - row.ijinCount
      const prayerComplianceRate = eligibleStudents > 0 
        ? Math.round(((row.zuhrCount + row.asrCount) / (eligibleStudents * 2)) * 100)
        : 0

      const zuhrRate = row.totalStudents > 0 ? Math.round((row.zuhrCount / row.totalStudents) * 100) : 0
      const asrRate = row.totalStudents > 0 ? Math.round((row.asrCount / row.totalStudents) * 100) : 0
      const dismissalRate = row.totalStudents > 0 ? Math.round((row.dismissalCount / row.totalStudents) * 100) : 0
      const ijinRate = row.totalStudents > 0 ? Math.round((row.ijinCount / row.totalStudents) * 100) : 0

      // Determine trend (simplified - in real implementation, compare with previous period)
      const trend: 'improving' | 'declining' | 'stable' = attendanceRate >= 80 ? 'stable' : 'declining'
      
      // Determine risk level
      const riskLevel: 'low' | 'medium' | 'high' = 
        attendanceRate >= 90 ? 'low' : 
        attendanceRate >= 70 ? 'medium' : 'high'

      return {
        className: row.className,
        totalStudents: row.totalStudents,
        presentStudents,
        absentStudents,
        attendanceRate,
        prayerComplianceRate,
        zuhrRate,
        asrRate,
        dismissalRate,
        ijinRate,
        trend,
        riskLevel,
      }
    })
  }

  /**
   * Get student performance analytics
   */
  async getStudentAnalytics(filters: AnalyticsFilters): Promise<StudentAnalytics[]> {
    const { dateRange } = filters

    const results = await db
      .select({
        uniqueCode: schema.users.uniqueCode,
        name: schema.users.name,
        className: schema.classes.name,
        zuhrCount: sql<number>`COUNT(CASE WHEN ${schema.absences.type} = 'Zuhr' THEN 1 END)`,
        asrCount: sql<number>`COUNT(CASE WHEN ${schema.absences.type} = 'Asr' THEN 1 END)`,
        dismissalCount: sql<number>`COUNT(CASE WHEN ${schema.absences.type} = 'Pulang' THEN 1 END)`,
        ijinCount: sql<number>`COUNT(CASE WHEN ${schema.absences.type} = 'Ijin' THEN 1 END)`,
        lastAttendance: sql<string>`MAX(${schema.absences.recordedAt})`,
      })
      .from(schema.users)
      .innerJoin(schema.classes, eq(schema.users.classId, schema.classes.id))
      .leftJoin(
        schema.absences,
        and(
          eq(schema.absences.uniqueCode, schema.users.uniqueCode),
          gte(schema.absences.recordedAt, dateRange.start),
          lte(schema.absences.recordedAt, dateRange.end)
        )
      )
      .where(eq(schema.users.role, 'student'))
      .groupBy(schema.users.uniqueCode, schema.users.name, schema.classes.name)
      .orderBy(asc(schema.users.name))

    // Calculate the number of possible attendance days
    const daysDiff = Math.ceil((dateRange.end.getTime() - dateRange.start.getTime()) / (1000 * 60 * 60 * 24))
    const possibleAttendanceDays = Math.max(1, daysDiff)

    return results.map(row => {
      const totalPrayersAttended = row.zuhrCount + row.asrCount
      const totalPrayersMissed = (possibleAttendanceDays * 2) - totalPrayersAttended - (row.ijinCount * 2)
      
      const attendanceRate = Math.round((Math.max(row.zuhrCount, row.asrCount, row.dismissalCount) / possibleAttendanceDays) * 100)
      const prayerComplianceRate = possibleAttendanceDays > 0 
        ? Math.round((totalPrayersAttended / (possibleAttendanceDays * 2)) * 100)
        : 0

      // Calculate consistency score (how regular is the attendance)
      const consistencyScore = Math.min(100, Math.round((totalPrayersAttended / Math.max(1, possibleAttendanceDays)) * 50))

      // Calculate risk score (higher = more at risk)
      const riskScore = Math.max(0, 100 - attendanceRate)

      // Determine trend
      const trend: 'improving' | 'declining' | 'stable' = 
        attendanceRate >= 90 ? 'stable' : 
        attendanceRate >= 70 ? 'improving' : 'declining'

      // Calculate streak days (simplified)
      const streakDays = Math.min(7, Math.round(attendanceRate / 15))

      // Generate recommendations
      const recommendations = []
      if (riskScore >= 70) {
        recommendations.push('Immediate intervention required')
        recommendations.push('Schedule parent meeting')
      } else if (riskScore >= 40) {
        recommendations.push('Monitor closely')
        recommendations.push('Provide additional support')
      } else {
        recommendations.push('Maintain current performance')
      }

      return {
        uniqueCode: row.uniqueCode,
        name: row.name,
        className: row.className,
        attendanceRate,
        prayerComplianceRate,
        consistencyScore,
        riskScore,
        trend,
        lastAttendance: row.lastAttendance,
        streakDays,
        totalPrayersAttended,
        totalPrayersMissed: Math.max(0, totalPrayersMissed),
        recommendations,
      }
    })
  }

  /**
   * Get dashboard KPIs
   */
  async getDashboardKPIs(date: Date): Promise<DashboardKPIs> {
    const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate())
    const endOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59)
    
    // Get yesterday's date for comparison
    const yesterday = new Date(date.getTime() - 24 * 60 * 60 * 1000)
    const startOfYesterday = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate())
    const endOfYesterday = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59)

    // Get today's data
    const [todayData] = await db
      .select({
        totalStudents: sql<number>`COUNT(DISTINCT CASE WHEN ${schema.users.role} = 'student' THEN ${schema.users.uniqueCode} END)`,
        presentStudents: sql<number>`COUNT(DISTINCT CASE WHEN ${schema.absences.type} IN ('Zuhr', 'Asr', 'Pulang') THEN ${schema.absences.uniqueCode} END)`,
        zuhrCount: sql<number>`COUNT(CASE WHEN ${schema.absences.type} = 'Zuhr' THEN 1 END)`,
        asrCount: sql<number>`COUNT(CASE WHEN ${schema.absences.type} = 'Asr' THEN 1 END)`,
        alertCount: sql<number>`0`, // Placeholder - implement alert logic
      })
      .from(schema.users)
      .leftJoin(
        schema.absences,
        and(
          eq(schema.absences.uniqueCode, schema.users.uniqueCode),
          gte(schema.absences.recordedAt, startOfDay),
          lte(schema.absences.recordedAt, endOfDay)
        )
      )

    // Get yesterday's data for comparison
    const [yesterdayData] = await db
      .select({
        presentStudents: sql<number>`COUNT(DISTINCT CASE WHEN ${schema.absences.type} IN ('Zuhr', 'Asr', 'Pulang') THEN ${schema.absences.uniqueCode} END)`,
        zuhrCount: sql<number>`COUNT(CASE WHEN ${schema.absences.type} = 'Zuhr' THEN 1 END)`,
        asrCount: sql<number>`COUNT(CASE WHEN ${schema.absences.type} = 'Asr' THEN 1 END)`,
      })
      .from(schema.absences)
      .where(
        and(
          gte(schema.absences.recordedAt, startOfYesterday),
          lte(schema.absences.recordedAt, endOfYesterday)
        )
      )

    const todayPercentage = todayData.totalStudents > 0 
      ? Math.round((todayData.presentStudents / todayData.totalStudents) * 100)
      : 0

    const yesterdayPercentage = todayData.totalStudents > 0 
      ? Math.round((yesterdayData.presentStudents / todayData.totalStudents) * 100)
      : 0

    const attendanceChange = todayPercentage - yesterdayPercentage

    const overallCompliance = todayData.totalStudents > 0
      ? Math.round(((todayData.zuhrCount + todayData.asrCount) / (todayData.totalStudents * 2)) * 100)
      : 0

    const yesterdayCompliance = todayData.totalStudents > 0
      ? Math.round(((yesterdayData.zuhrCount + yesterdayData.asrCount) / (todayData.totalStudents * 2)) * 100)
      : 0

    const prayerChange = overallCompliance - yesterdayCompliance

    return {
      todayAttendance: {
        total: todayData.totalStudents,
        present: todayData.presentStudents,
        absent: todayData.totalStudents - todayData.presentStudents,
        percentage: todayPercentage,
        change: attendanceChange,
      },
      prayerMetrics: {
        zuhrAttendance: todayData.zuhrCount,
        asrAttendance: todayData.asrCount,
        overallCompliance,
        change: prayerChange,
      },
      weeklyTrend: {
        currentWeek: todayPercentage,
        previousWeek: yesterdayPercentage, // Simplified - should be actual previous week
        change: attendanceChange,
        trend: attendanceChange > 0 ? 'up' : attendanceChange < 0 ? 'down' : 'stable',
      },
      alerts: {
        count: todayData.alertCount,
        critical: 0,
        warning: 0,
        info: todayData.alertCount,
      },
    }
  }

  /**
   * Get trend data for charts
   */
  async getTrendData(type: 'attendance' | 'prayer' | 'class', filters: AnalyticsFilters): Promise<TrendData[]> {
    // Implementation depends on the type of trend requested
    // This is a simplified version
    const { dateRange } = filters
    
    const results = await db
      .select({
        date: sql<string>`DATE(${schema.absences.recordedAt})`,
        value: sql<number>`COUNT(DISTINCT ${schema.absences.uniqueCode})`,
      })
      .from(schema.absences)
      .where(
        and(
          gte(schema.absences.recordedAt, dateRange.start),
          lte(schema.absences.recordedAt, dateRange.end)
        )
      )
      .groupBy(sql`DATE(${schema.absences.recordedAt})`)
      .orderBy(asc(sql`DATE(${schema.absences.recordedAt})`))

    return results.map((row, index) => ({
      date: row.date,
      value: row.value,
      label: new Date(row.date).toLocaleDateString('id-ID', { 
        day: '2-digit', 
        month: 'short' 
      }),
      change: index > 0 ? ((row.value - results[index - 1].value) / results[index - 1].value) * 100 : 0,
      changeType: index > 0 
        ? (row.value > results[index - 1].value ? 'increase' : 
           row.value < results[index - 1].value ? 'decrease' : 'stable')
        : 'stable'
    }))
  }

  /**
   * Get heatmap data for calendar visualization
   */
  async getHeatmapData(filters: AnalyticsFilters): Promise<HeatmapData[]> {
    const { dateRange } = filters

    const results = await db
      .select({
        date: sql<string>`DATE(${schema.absences.recordedAt})`,
        attendanceCount: sql<number>`COUNT(DISTINCT ${schema.absences.uniqueCode})`,
        totalStudents: sql<number>`(SELECT COUNT(*) FROM users WHERE role = 'student')`,
      })
      .from(schema.absences)
      .where(
        and(
          gte(schema.absences.recordedAt, dateRange.start),
          lte(schema.absences.recordedAt, dateRange.end)
        )
      )
      .groupBy(sql`DATE(${schema.absences.recordedAt})`)

    return results.map(row => {
      const percentage = row.totalStudents > 0 
        ? Math.round((row.attendanceCount / row.totalStudents) * 100)
        : 0

      const level = percentage >= 90 ? 4 : 
                   percentage >= 75 ? 3 : 
                   percentage >= 50 ? 2 : 
                   percentage >= 25 ? 1 : 0

      return {
        date: row.date,
        value: percentage,
        level: level as 0 | 1 | 2 | 3 | 4,
        tooltip: `${row.date}: ${percentage}% attendance (${row.attendanceCount}/${row.totalStudents} students)`
      }
    })
  }

  /**
   * Get attendance alerts
   */
  async getAttendanceAlerts(limit: number = 10): Promise<AttendanceAlert[]> {
    // This is a simplified implementation
    // In a real system, you'd have a dedicated alerts table
    return []
  }

  /**
   * Get predictive analytics
   */
  async getPredictiveAnalytics(studentCode?: string): Promise<PredictiveAnalytics[]> {
    // This would require machine learning models
    // For now, return empty array
    return []
  }
}
