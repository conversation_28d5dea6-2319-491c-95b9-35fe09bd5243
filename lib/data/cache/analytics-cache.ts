/**
 * Analytics Cache Implementation
 * Clean Architecture - Infrastructure Layer
 */

import { IAnalyticsCache } from '../../domain/usecases/analytics'
import { getRedisCache } from './redis'

/**
 * Redis-based analytics cache implementation
 */
export class AnalyticsCache implements IAnalyticsCache {
  private redis = getRedisCache()
  private keyPrefix = 'analytics:'

  /**
   * Get cached data
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      const fullKey = this.keyPrefix + key
      const cached = await this.redis.get(fullKey)
      
      if (!cached) {
        return null
      }

      return JSON.parse(cached) as T
    } catch (error) {
      console.error('Analytics cache get error:', error)
      return null
    }
  }

  /**
   * Set cached data with TTL
   */
  async set<T>(key: string, value: T, ttlSeconds: number = 300): Promise<void> {
    try {
      const fullKey = this.keyPrefix + key
      const serialized = JSON.stringify(value)
      
      await this.redis.setex(fullKey, ttlSeconds, serialized)
    } catch (error) {
      console.error('Analytics cache set error:', error)
      // Don't throw error - cache failures shouldn't break the application
    }
  }

  /**
   * Invalidate cache entries matching pattern
   */
  async invalidate(pattern: string): Promise<void> {
    try {
      const fullPattern = this.keyPrefix + pattern
      const keys = await this.redis.keys(fullPattern)
      
      if (keys.length > 0) {
        await this.redis.del(...keys)
      }
    } catch (error) {
      console.error('Analytics cache invalidate error:', error)
      // Don't throw error - cache failures shouldn't break the application
    }
  }

  /**
   * Clear all analytics cache
   */
  async clear(): Promise<void> {
    await this.invalidate('*')
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<{
    totalKeys: number
    memoryUsage: string
    hitRate: number
  }> {
    try {
      const keys = await this.redis.keys(this.keyPrefix + '*')
      const info = await this.redis.info('memory')
      
      // Parse memory info (simplified)
      const memoryMatch = info.match(/used_memory_human:([^\r\n]+)/)
      const memoryUsage = memoryMatch ? memoryMatch[1].trim() : 'Unknown'

      return {
        totalKeys: keys.length,
        memoryUsage,
        hitRate: 0, // Would need to track hits/misses for accurate calculation
      }
    } catch (error) {
      console.error('Analytics cache stats error:', error)
      return {
        totalKeys: 0,
        memoryUsage: 'Unknown',
        hitRate: 0,
      }
    }
  }
}

/**
 * Singleton instance of analytics cache
 */
let analyticsCache: AnalyticsCache | null = null

/**
 * Get analytics cache instance
 */
export function getAnalyticsCache(): AnalyticsCache {
  if (!analyticsCache) {
    analyticsCache = new AnalyticsCache()
  }
  return analyticsCache
}
