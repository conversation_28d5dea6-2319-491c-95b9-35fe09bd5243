-- Migration: Add teacher and receptionist roles to user_role enum
-- Run this SQL in your PostgreSQL database

-- Check current enum values (optional)
SELECT enumlabel 
FROM pg_enum 
WHERE enumtypid = (
  SELECT oid 
  FROM pg_type 
  WHERE typname = 'user_role'
)
ORDER BY enumsortorder;

-- Add new enum values (these commands are safe to run multiple times)
ALTER TYPE user_role ADD VALUE IF NOT EXISTS 'teacher';
ALTER TYPE user_role ADD VALUE IF NOT EXISTS 'receptionist';

-- Update the role data constraint to include new roles
ALTER TABLE users DROP CONSTRAINT IF EXISTS chk_role_data;

ALTER TABLE users ADD CONSTRAINT chk_role_data CHECK (
  (role = 'student' AND unique_code IS NOT NULL AND username IS NOT NULL AND password_hash IS NOT NULL) OR 
  (role IN ('admin', 'super_admin', 'teacher', 'receptionist') AND google_email IS NULL AND unique_code IS NULL AND username IS NOT NULL AND password_hash IS NOT NULL)
);

-- Verify the changes (optional)
SELECT enumlabel 
FROM pg_enum 
WHERE enumtypid = (
  SELECT oid 
  FROM pg_type 
  WHERE typname = 'user_role'
)
ORDER BY enumsortorder;
