import { NextRequest, NextResponse } from 'next/server'
import { UserUseCases } from '@/lib/domain/usecases/user'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { ClassRepository } from '@/lib/data/repositories/class'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import { getRedisCache } from '@/lib/data/cache/redis'
import { authenticateAdmin, handleAuthError } from '@/lib/middleware/auth'
import { z } from 'zod'

// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository(cache)
const adminRepo = new AdminRepository(cache)
const classRepo = new ClassRepository()
const absenceRepo = new AbsenceRepository()
const userUseCases = new UserUseCases(studentRepo, adminRepo, classRepo, cache, absenceRepo)

// Schema for creating admin
const createAdminSchema = z.object({
  role: z.enum(['admin', 'super_admin', 'teacher', 'receptionist']),
  name: z.string().min(1, 'Nama wajib diisi'),
  username: z
    .string()
    .min(3, 'Username minimal 3 karakter')
    .max(50, 'Username maksimal 50 karakter'),
  password: z.string().min(6, 'Password minimal 6 karakter'),
})

/**
 * GET /api/admins
 * Get all admin users (admin, super_admin, teacher, and receptionist)
 */
export async function GET(req: NextRequest) {
  try {
    // Authenticate the admin
    try {
      await authenticateAdmin(req)
    } catch (authError) {
      return handleAuthError(authError)
    }

    console.info('Fetching all admin users')
    const users = await userUseCases.getAllUsers()

    // Filter admin, super_admin, teacher, and receptionist users
    const adminUsers = users.filter(
      user =>
        user.role === 'admin' ||
        user.role === 'super_admin' ||
        user.role === 'teacher' ||
        user.role === 'receptionist'
    )

    console.info(`Found ${adminUsers.length} admin users`)
    return NextResponse.json(adminUsers)
  } catch (error) {
    console.error('Error fetching admin users:', error)
    return NextResponse.json({ message: 'Gagal mengambil data admin' }, { status: 500 })
  }
}

/**
 * POST /api/admins
 * Create a new admin user
 */
export async function POST(req: NextRequest) {
  try {
    // Authenticate the admin
    try {
      await authenticateAdmin(req)
    } catch (authError) {
      return handleAuthError(authError)
    }

    const body = await req.json()
    const validation = createAdminSchema.safeParse(body)

    if (!validation.success) {
      console.error('Admin creation validation failed:', validation.error.format())
      return NextResponse.json(
        { message: 'Validasi gagal', errors: validation.error.format() },
        { status: 400 }
      )
    }

    const validatedData = validation.data

    console.info('Attempting to create Admin:', {
      username: validatedData.username,
      name: validatedData.name,
      role: validatedData.role,
    })

    const newAdmin = await userUseCases.createAdmin({
      username: validatedData.username,
      name: validatedData.name,
      password: validatedData.password,
      role: validatedData.role,
    })

    console.info('Admin created successfully:', { id: newAdmin.id })

    // Clear cache
    await cache.del('users:all')

    return NextResponse.json(
      {
        id: newAdmin.id,
        name: newAdmin.name,
        username: newAdmin.username,
        role: newAdmin.role,
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('Error creating admin:', error)

    // Handle specific error types
    if (error instanceof Error) {
      if (
        error.message.includes('username already exists') ||
        error.message.includes('duplicate')
      ) {
        return NextResponse.json({ message: 'Username sudah digunakan' }, { status: 409 })
      }
    }

    return NextResponse.json({ message: 'Gagal membuat admin baru' }, { status: 500 })
  }
}
