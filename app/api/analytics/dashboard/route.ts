/**
 * Analytics Dashboard API
 * Clean Architecture - Presentation Layer
 */

import { NextRequest, NextResponse } from 'next/server'
import { AnalyticsUseCases } from '@/lib/domain/usecases/analytics'
import { AnalyticsRepository } from '@/lib/data/repositories/analytics'
import { getAnalyticsCache } from '@/lib/data/cache/analytics-cache'
import { authenticateAdmin, handleAuthError } from '@/lib/middleware/auth'
import { z } from 'zod'

// Initialize dependencies with clean architecture
const cache = getAnalyticsCache()
const analyticsRepository = new AnalyticsRepository()
const analyticsUseCases = new AnalyticsUseCases(analyticsRepository, cache)

// Request validation schema
const DashboardRequestSchema = z.object({
  date: z.string().optional(),
  refresh: z.boolean().optional(),
})

/**
 * GET /api/analytics/dashboard
 * Get dashboard analytics overview
 */
export async function GET(req: NextRequest) {
  try {
    // Authenticate admin with role-based access
    let adminRole: string
    try {
      const admin = await authenticateAdmin(req)
      adminRole = admin.role
      
      // Only Super Admin and Admin can access analytics dashboard
      if (!['super_admin', 'admin'].includes(adminRole)) {
        return NextResponse.json(
          { 
            error: 'Access denied',
            message: 'Insufficient permissions to access analytics dashboard'
          },
          { status: 403 }
        )
      }
    } catch (authError) {
      return handleAuthError(authError)
    }

    // Parse and validate query parameters
    const searchParams = req.nextUrl.searchParams
    const dateParam = searchParams.get('date')
    const refreshParam = searchParams.get('refresh') === 'true'

    let requestData
    try {
      requestData = DashboardRequestSchema.parse({
        date: dateParam,
        refresh: refreshParam,
      })
    } catch (validationError) {
      return NextResponse.json(
        {
          error: 'Invalid request parameters',
          details: validationError instanceof z.ZodError ? validationError.errors : 'Validation failed'
        },
        { status: 400 }
      )
    }

    // Parse date or use today
    let targetDate: Date
    if (requestData.date) {
      targetDate = new Date(requestData.date)
      if (isNaN(targetDate.getTime())) {
        return NextResponse.json(
          { error: 'Invalid date format' },
          { status: 400 }
        )
      }
    } else {
      targetDate = new Date()
    }

    // Clear cache if refresh requested
    if (requestData.refresh) {
      await analyticsUseCases.invalidateCache()
    }

    // Get dashboard data
    const startTime = Date.now()
    const dashboardData = await analyticsUseCases.getDashboardOverview(targetDate)
    const queryTime = Date.now() - startTime

    // Add performance metadata
    const response = {
      ...dashboardData,
      metadata: {
        requestId: Math.random().toString(36).substring(2, 8),
        timestamp: new Date().toISOString(),
        queryTime,
        cached: !requestData.refresh,
        date: targetDate.toISOString().split('T')[0],
        timezone: 'Asia/Makassar',
      }
    }

    // Set cache headers for client-side caching
    const headers = new Headers()
    headers.set('Cache-Control', 'public, max-age=300, stale-while-revalidate=600') // 5 min cache, 10 min stale
    headers.set('X-Request-ID', response.metadata.requestId)
    headers.set('X-Query-Time', queryTime.toString())

    return NextResponse.json(response, { headers })

  } catch (error) {
    console.error('Analytics dashboard API error:', error)
    
    // Return structured error response
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: 'Failed to fetch dashboard analytics',
        timestamp: new Date().toISOString(),
        requestId: Math.random().toString(36).substring(2, 8),
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/analytics/dashboard/refresh
 * Force refresh dashboard cache
 */
export async function POST(req: NextRequest) {
  try {
    // Authenticate admin
    try {
      const admin = await authenticateAdmin(req)
      
      // Only Super Admin and Admin can refresh cache
      if (!['super_admin', 'admin'].includes(admin.role)) {
        return NextResponse.json(
          { error: 'Access denied' },
          { status: 403 }
        )
      }
    } catch (authError) {
      return handleAuthError(authError)
    }

    // Clear analytics cache
    await analyticsUseCases.invalidateCache()

    return NextResponse.json({
      success: true,
      message: 'Analytics cache refreshed successfully',
      timestamp: new Date().toISOString(),
    })

  } catch (error) {
    console.error('Analytics cache refresh error:', error)
    
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: 'Failed to refresh analytics cache',
      },
      { status: 500 }
    )
  }
}
