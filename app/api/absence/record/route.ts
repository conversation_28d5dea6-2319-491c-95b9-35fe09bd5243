import { NextRequest, NextResponse } from 'next/server'
import { AbsenceUseCases } from '@/lib/domain/usecases/absence'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import { StudentRepository } from '@/lib/data/repositories/student'
import { getRedisCache } from '@/lib/data/cache/redis'
import { AttendanceType } from '@/lib/domain/entities/absence'
import { authenticateAdmin, handleAuthError } from '@/lib/middleware/auth'
import { z } from 'zod'
import { PrayerNotCompletedError } from '@/lib/domain/errors/PrayerNotCompletedError'
// SECURE: Import attendance type validation
import { validateAttendanceTypeAccess } from '@/lib/utils/attendance-validation'
import { verifyToken } from '@/lib/utils/auth'
import { serverConfig } from '@/lib/config'

// Initialize dependencies
const cache = getRedisCache()
const absenceRepo = new AbsenceRepository()
const studentRepo = new StudentRepository(cache)
const absenceUseCases = new AbsenceUseCases(absenceRepo, studentRepo, cache)

// Validation schema for recording absence with more robust validation
const recordAbsenceSchema = z.object({
  // More flexible uniqueCode handling that still ensures we get a valid UUID
  uniqueCode: z
    .string()
    .min(32, 'Unique code must be at least 32 characters (with or without hyphens)')
    .max(40, 'Unique code too long')
    .transform(value => {
      // If it's already a valid UUID format, return it
      if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(value)) {
        return value
      }

      // If it's a UUID without hyphens, format it
      if (/^[0-9a-f]{32}$/i.test(value)) {
        return `${value.substring(0, 8)}-${value.substring(8, 12)}-${value.substring(12, 16)}-${value.substring(16, 20)}-${value.substring(20)}`
      }

      // If it contains a UUID somewhere, extract it
      const match = value.match(/([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})/i)
      if (match && match[1]) {
        return match[1]
      }

      // Return the original value, further validation will happen in the handler
      return value
    }),

  // Be more flexible with attendance type
  type: z.union([
    z.enum([
      AttendanceType.ZUHR,
      AttendanceType.ASR,
      AttendanceType.DISMISSAL,
      AttendanceType.IJIN,
      AttendanceType.ENTRY,
      AttendanceType.LATE_ENTRY,
      AttendanceType.EXCUSED_ABSENCE,
      AttendanceType.TEMPORARY_LEAVE,
      AttendanceType.RETURN_FROM_LEAVE,
      AttendanceType.SICK,
    ]),
    z.string().transform(value => {
      // Allow for case insensitive matching and some variations
      const normalized = value.toLowerCase()
      if (normalized === 'zuhr' || normalized === 'zuhur' || normalized === 'duhur') {
        return AttendanceType.ZUHR
      }
      if (normalized === 'asr' || normalized === 'ashar' || normalized === 'ashr') {
        return AttendanceType.ASR
      }
      if (normalized === 'dismissal' || normalized === 'pulang' || normalized === 'home') {
        return AttendanceType.DISMISSAL
      }
      if (normalized === 'ijin' || normalized === 'izin' || normalized === 'permit') {
        return AttendanceType.IJIN
      }
      if (normalized === 'entry' || normalized === 'masuk') {
        return AttendanceType.ENTRY
      }
      if (normalized === 'late entry' || normalized === 'masuk terlambat') {
        return AttendanceType.LATE_ENTRY
      }
      if (normalized === 'excused absence' || normalized === 'izin') {
        return AttendanceType.EXCUSED_ABSENCE
      }
      if (normalized === 'temporary leave' || normalized === 'izin sementara') {
        return AttendanceType.TEMPORARY_LEAVE
      }
      if (normalized === 'return from leave' || normalized === 'kembali dari izin') {
        return AttendanceType.RETURN_FROM_LEAVE
      }
      if (normalized === 'sick' || normalized === 'sakit') {
        return AttendanceType.SICK
      }
      // Default to the provided value, will be caught by the enum check
      return value as AttendanceType
    }),
  ]),

  force: z.boolean().optional().default(false), // Add force parameter to allow overriding duplicate check
})

/**
 * POST /api/absence/record
 * Record a new absence
 */
export async function POST(req: NextRequest) {
  try {
    // Authenticate the admin and get role information
    let adminRole: 'admin' | 'super_admin' | 'teacher' | 'receptionist'
    try {
      await authenticateAdmin(req)

      // SECURE: Get admin role for attendance type validation
      const authToken = req.cookies.get('admin_auth_token')?.value
      if (authToken) {
        const decoded = verifyToken(authToken, serverConfig.auth.jwtSecret || '')
        adminRole = decoded.role as 'admin' | 'super_admin' | 'teacher' | 'receptionist'
      } else {
        throw new Error('No auth token found')
      }
    } catch (authError) {
      return handleAuthError(authError)
    }

    // Parse and validate the request body
    let body
    try {
      body = await req.json()
    } catch (jsonError) {
      console.error('Error parsing JSON:', jsonError)
      return NextResponse.json(
        {
          error: 'Invalid JSON in request body',
          details: 'The request body could not be parsed as JSON',
        },
        { status: 400 }
      )
    }

    try {
      const { uniqueCode, type, force } = recordAbsenceSchema.parse(body)

      // SECURITY: Validate role can handle this attendance type
      if (!validateAttendanceTypeAccess(adminRole, type)) {
        console.warn(
          `🚨 SECURITY: Admin role ${adminRole} attempted to record unauthorized attendance type: ${type}`
        )
        return NextResponse.json(
          {
            error: 'Unauthorized attendance type access',
            details: `Role ${adminRole} is not authorized to record ${type} attendance`,
          },
          { status: 403 }
        )
      }

      // Final validation to ensure we have a proper UUID
      if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(uniqueCode)) {
        console.error(`Invalid UUID format after transform: ${uniqueCode}`)
        return NextResponse.json(
          {
            error: 'Invalid format for uniqueCode',
            details: 'The uniqueCode must be a valid UUID',
          },
          { status: 400 }
        )
      }

      console.log(
        `Recording attendance: uniqueCode=${uniqueCode.substring(0, 8)}..., type=${type}, force=${force}`
      )

      // Record the absence
      const absence = await absenceUseCases.recordAbsence(uniqueCode, type, force)

      // Return the recorded absence
      return NextResponse.json({
        id: absence.id,
        uniqueCode: absence.uniqueCode,
        type: absence.type,
        recordedAt: absence.recordedAt,
      })
    } catch (validationError) {
      if (validationError instanceof z.ZodError) {
        console.error('Validation error:', validationError.errors)
        return NextResponse.json(
          {
            error: 'Invalid input',
            details: validationError.errors,
            message: 'Please provide a valid uniqueCode and attendance type.',
          },
          { status: 400 }
        )
      }
      throw validationError // Re-throw if it's not a Zod error
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('Validation error:', error.errors)
      return NextResponse.json(
        {
          error: 'Invalid input',
          details: error.errors,
          message: 'Please provide a valid uniqueCode and attendance type.',
        },
        { status: 400 }
      )
    }

    // Handle prayer not completed error
    if (error instanceof PrayerNotCompletedError) {
      return NextResponse.json(
        {
          error: 'Prayer not completed',
          message: error.message,
          code: 'PRAYER_NOT_COMPLETED',
        },
        { status: 403 }
      )
    }

    // Handle duplicate attendance error
    if (error instanceof Error && error.name === 'DuplicateError') {
      return NextResponse.json(
        {
          error: error.message,
          isDuplicate: true,
          message: 'Attendance has already been recorded for today.',
        },
        { status: 409 }
      )
    }

    // Handle not found error
    if (error instanceof Error && error.name === 'NotFoundError') {
      return NextResponse.json(
        {
          error: error.message,
          message: 'Student not found with the provided uniqueCode.',
        },
        { status: 404 }
      )
    }

    // Handle authentication errors
    if (
      error instanceof Error &&
      (error.message.includes('Authentication') ||
        error.message.includes('token') ||
        error.message.includes('Access denied'))
    ) {
      return handleAuthError(error)
    }

    console.error('Error recording absence:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: 'An unexpected error occurred while recording attendance. Please try again.',
      },
      { status: 500 }
    )
  }
}
