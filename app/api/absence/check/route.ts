import { NextRequest, NextResponse } from 'next/server'
import { AbsenceUseCases } from '@/lib/domain/usecases/absence'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import { StudentRepository } from '@/lib/data/repositories/student'
import { getRedisCache } from '@/lib/data/cache/redis'
import { AttendanceType } from '@/lib/domain/entities/absence'
import { verifyToken } from '@/lib/utils/auth'
import { serverConfig } from '@/lib/config'
import { formatTimeWITA } from '@/lib/utils/date'
import { z } from 'zod'

// Initialize dependencies
const cache = getRedisCache()
const absenceRepo = new AbsenceRepository()
const studentRepo = new StudentRepository(cache)
const absenceUseCases = new AbsenceUseCases(absenceRepo, studentRepo, cache)

// Validation schema for checking absence with more robust validation
const checkAbsenceSchema = z.object({
  // Accept any string format that might be a UUID and validate in the handler
  uniqueCode: z
    .string()
    .min(32, 'Unique code must be at least 32 characters (with or without hyphens)')
    .max(40, 'Unique code too long')
    .transform(value => {
      // If it's already a valid UUID format, return it
      if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(value)) {
        return value
      }

      // If it's a UUID without hyphens, format it
      if (/^[0-9a-f]{32}$/i.test(value)) {
        return `${value.substring(0, 8)}-${value.substring(8, 12)}-${value.substring(12, 16)}-${value.substring(16, 20)}-${value.substring(20)}`
      }

      // If it contains a UUID somewhere, extract it
      const match = value.match(/([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})/i)
      if (match && match[1]) {
        return match[1]
      }

      // Return the original value, further validation will happen in the handler
      return value
    }),

  // Be more flexible with attendance type
  type: z.union([
    z.enum([
      AttendanceType.ZUHR,
      AttendanceType.ASR,
      AttendanceType.DISMISSAL,
      AttendanceType.IJIN,
      AttendanceType.ENTRY,
      AttendanceType.LATE_ENTRY,
      AttendanceType.EXCUSED_ABSENCE,
      AttendanceType.TEMPORARY_LEAVE,
      AttendanceType.RETURN_FROM_LEAVE,
      AttendanceType.SICK,
    ]),
    z.string().transform(value => {
      // Allow for case insensitive matching and some variations
      const normalized = value.toLowerCase()
      if (normalized === 'zuhr' || normalized === 'zuhur' || normalized === 'duhur') {
        return AttendanceType.ZUHR
      }
      if (normalized === 'asr' || normalized === 'ashar' || normalized === 'ashr') {
        return AttendanceType.ASR
      }
      if (normalized === 'dismissal' || normalized === 'pulang' || normalized === 'home') {
        return AttendanceType.DISMISSAL
      }
      if (normalized === 'ijin' || normalized === 'izin' || normalized === 'permit') {
        return AttendanceType.IJIN
      }
      if (normalized === 'entry' || normalized === 'masuk') {
        return AttendanceType.ENTRY
      }
      if (normalized === 'late entry' || normalized === 'masuk terlambat') {
        return AttendanceType.LATE_ENTRY
      }
      if (normalized === 'excused absence' || normalized === 'izin') {
        return AttendanceType.EXCUSED_ABSENCE
      }
      if (normalized === 'temporary leave' || normalized === 'izin sementara') {
        return AttendanceType.TEMPORARY_LEAVE
      }
      if (normalized === 'return from leave' || normalized === 'kembali dari izin') {
        return AttendanceType.RETURN_FROM_LEAVE
      }
      if (normalized === 'sick' || normalized === 'sakit') {
        return AttendanceType.SICK
      }
      // Default to the provided value, will be caught by the enum check
      return value as AttendanceType
    }),
  ]),
})

/**
 * POST /api/absence/check
 * Check if a student has already recorded attendance for a specific type today
 */
export async function POST(req: NextRequest) {
  try {
    // Authenticate the request - check both student and admin tokens
    const adminAuthToken = req.cookies.get('admin_auth_token')?.value
    const studentAuthToken = req.cookies.get('student_auth_token')?.value
    const authToken = adminAuthToken || studentAuthToken

    if (!authToken) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = verifyToken(authToken, serverConfig.auth.jwtSecret || '')

      if (!decoded || !decoded.id) {
        return NextResponse.json({ error: 'Invalid or expired token' }, { status: 401 })
      }

      // Parse and validate the request body
      let body
      try {
        body = await req.json()
      } catch (jsonError) {
        console.error('Error parsing JSON:', jsonError)
        return NextResponse.json(
          {
            error: 'Invalid JSON in request body',
            details: 'The request body could not be parsed as JSON',
          },
          { status: 400 }
        )
      }

      try {
        const { uniqueCode, type } = checkAbsenceSchema.parse(body)

        // Additional validation to ensure UUID format
        if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(uniqueCode)) {
          console.error(`Invalid UUID format after transform: ${uniqueCode}`)
          return NextResponse.json(
            {
              error: 'Invalid format for uniqueCode',
              details: 'The uniqueCode must be a valid UUID',
            },
            { status: 400 }
          )
        }

        console.log(
          `Processing check request: uniqueCode=${uniqueCode.substring(0, 8)}..., type=${type}`
        )

        // Check for duplicate attendance - type is already an AttendanceType enum value
        const isDuplicate = await absenceUseCases.checkDuplicateAttendance(uniqueCode, type)

        // Return the result
        return NextResponse.json({ isDuplicate })
      } catch (zodError) {
        if (zodError instanceof z.ZodError) {
          console.error('Zod validation failed:', zodError.errors)
          return NextResponse.json(
            {
              error: 'Invalid input',
              details: zodError.errors,
            },
            { status: 400 }
          )
        }
        throw zodError // Re-throw if it's not a Zod error
      }
    } catch (error) {
      console.error('Token verification failed:', error)
      return NextResponse.json({ error: 'Invalid or expired token' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error checking absence:', error)

    // More specific error handling
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid input',
          details: error.errors,
          message: 'Please ensure the unique code and attendance type are valid.',
        },
        { status: 400 }
      )
    }

    if (error instanceof Error) {
      return NextResponse.json(
        {
          error: 'Internal server error',
          message: error.message || 'An unexpected error occurred when checking attendance.',
        },
        { status: 500 }
      )
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

/**
 * GET /api/absence/check
 * Get attendance status for a student for today
 */
export async function GET(req: NextRequest) {
  try {
    // Get the uniqueCode from query parameters
    const searchParams = req.nextUrl.searchParams
    let uniqueCode = searchParams.get('uniqueCode')

    if (!uniqueCode) {
      return NextResponse.json({ error: 'Missing uniqueCode parameter' }, { status: 400 })
    }

    // Validate the uniqueCode format
    if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(uniqueCode)) {
      console.warn(`Invalid UUID format in GET request: ${uniqueCode}`)
      // Try to normalize it
      let normalizedUniqueCode = uniqueCode

      // Check if it's a UUID without hyphens
      if (/^[0-9a-f]{32}$/i.test(uniqueCode)) {
        normalizedUniqueCode = `${uniqueCode.substring(0, 8)}-${uniqueCode.substring(8, 12)}-${uniqueCode.substring(12, 16)}-${uniqueCode.substring(16, 20)}-${uniqueCode.substring(20)}`
        console.log(`Normalized UUID without hyphens to: ${normalizedUniqueCode}`)
      } else {
        // Try to extract a UUID if embedded in the string
        const match = uniqueCode.match(
          /([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})/i
        )
        if (match && match[1]) {
          normalizedUniqueCode = match[1]
          console.log(`Extracted UUID from string: ${normalizedUniqueCode}`)
        } else {
          return NextResponse.json(
            {
              error: 'Invalid uniqueCode format',
              message: 'The uniqueCode must be a valid UUID',
            },
            { status: 400 }
          )
        }
      }

      // Continue with the normalized code
      console.log(
        `Continuing with normalized uniqueCode: ${normalizedUniqueCode.substring(0, 8)}...`
      )
      uniqueCode = normalizedUniqueCode
    }

    // Get attendance status for today
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    try {
      // Get attendance records for today
      const attendanceRecords = await absenceUseCases.getAttendanceForDay(uniqueCode, today)

      // No mock data - use only real data from the database

      // Format the response
      const response: {
        zuhr: boolean
        zuhrTime: string | null
        asr: boolean
        asrTime: string | null
        pulang: boolean
        pulangTime: string | null
        ijin: boolean
        ijinTime: string | null
      } = {
        zuhr: false,
        zuhrTime: null,
        asr: false,
        asrTime: null,
        pulang: false,
        pulangTime: null,
        ijin: false,
        ijinTime: null,
      }

      // Log the attendance records for debugging
      console.log(
        `Retrieved ${attendanceRecords.length} attendance records for uniqueCode=${uniqueCode.substring(0, 8)}...`
      )

      // Update response based on attendance records
      for (const record of attendanceRecords) {
        // Use safe type checking to avoid issues with Date objects
        const recordType = record.type?.toString()

        if (recordType === AttendanceType.ZUHR) {
          response.zuhr = true
          // Format the time safely
          if (record.recordedAt) {
            const recordDate = new Date(record.recordedAt)
            if (!isNaN(recordDate.getTime())) {
              response.zuhrTime = formatTimeWITA(recordDate)
            }
          }
        } else if (recordType === AttendanceType.ASR) {
          response.asr = true
          // Format the time safely
          if (record.recordedAt) {
            const recordDate = new Date(record.recordedAt)
            if (!isNaN(recordDate.getTime())) {
              response.asrTime = formatTimeWITA(recordDate)
            }
          }
        } else if (recordType === AttendanceType.DISMISSAL) {
          response.pulang = true
          // Format the time safely
          if (record.recordedAt) {
            const recordDate = new Date(record.recordedAt)
            if (!isNaN(recordDate.getTime())) {
              response.pulangTime = formatTimeWITA(recordDate)
            }
          }
        } else if (recordType === AttendanceType.IJIN) {
          response.ijin = true
          // Format the time safely
          if (record.recordedAt) {
            const recordDate = new Date(record.recordedAt)
            if (!isNaN(recordDate.getTime())) {
              response.ijinTime = formatTimeWITA(recordDate)
            }
          }
        }
      }

      return NextResponse.json(response)
    } catch (innerError) {
      console.error('Error fetching attendance records:', innerError)

      // Fallback to mock data if there's an error
      return NextResponse.json({
        zuhr: false,
        zuhrTime: null,
        asr: false,
        asrTime: null,
        pulang: false,
        pulangTime: null,
        ijin: false,
        ijinTime: null,
      })
    }
  } catch (error) {
    console.error('Error getting attendance status:', error)
    return NextResponse.json({ error: 'Failed to get attendance status' }, { status: 500 })
  }
}
