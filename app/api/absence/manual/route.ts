import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { AttendanceType } from '@/lib/domain/entities/absence'
import { validateAttendanceTypeAccess } from '@/lib/utils/attendance-validation'
import { AbsenceUseCases } from '@/lib/domain/usecases/absence'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import { StudentRepository } from '@/lib/data/repositories/student'
import { getRedisCache } from '@/lib/data/cache/redis'
import { authenticateAdmin, handleAuthError } from '@/lib/middleware/auth'
import { verifyToken } from '@/lib/utils/auth'
import { serverConfig } from '@/lib/config'
import { NotFoundError, ValidationError } from '@/lib/domain/errors'

// Initialize dependencies
const cache = getRedisCache()
const absenceRepo = new AbsenceRepository()
const studentRepo = new StudentRepository(cache)
const absenceUseCases = new AbsenceUseCases(absenceRepo, studentRepo, cache)

const manualEntrySchema = z.object({
  uniqueCode: z.string().uuid('Invalid unique code format'),
  type: z.nativeEnum(AttendanceType, { message: 'Invalid attendance type' }),
  recordedAt: z.string().datetime('Invalid datetime format'),
  reason: z.string().optional(),
})

export async function POST(request: NextRequest) {
  try {
    // Authenticate the admin and get role information
    let adminRole: 'admin' | 'super_admin' | 'teacher' | 'receptionist'
    try {
      await authenticateAdmin(request)

      // SECURE: Get admin role for attendance type validation
      const authToken = request.cookies.get('admin_auth_token')?.value
      if (authToken) {
        const decoded = verifyToken(authToken, serverConfig.auth.jwtSecret || '')
        adminRole = decoded.role as 'admin' | 'super_admin' | 'teacher' | 'receptionist'
      } else {
        throw new Error('No auth token found')
      }
    } catch (authError) {
      return handleAuthError(authError)
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = manualEntrySchema.parse(body)

    // Validate that the user role can handle this attendance type
    if (!validateAttendanceTypeAccess(adminRole, validatedData.type)) {
      return NextResponse.json(
        { message: 'You are not authorized to record this attendance type' },
        { status: 403 }
      )
    }

    // Check if reason is required for certain attendance types
    const requiresReason = [AttendanceType.EXCUSED_ABSENCE, AttendanceType.SICK].includes(
      validatedData.type
    )
    if (requiresReason && !validatedData.reason?.trim()) {
      return NextResponse.json(
        { message: 'Reason is required for this attendance type' },
        { status: 400 }
      )
    }

    // Record the attendance using the initialized use cases
    const absence = await absenceUseCases.recordAbsence(
      validatedData.uniqueCode,
      validatedData.type,
      false // Don't force update for manual entries
    )

    // Log the manual entry for audit purposes
    console.log(`Manual attendance recorded by ${adminRole}:`, {
      studentCode: validatedData.uniqueCode,
      type: validatedData.type,
      recordedAt: validatedData.recordedAt,
      reason: validatedData.reason,
    })

    return NextResponse.json({
      message: 'Attendance recorded successfully',
      absence: {
        id: absence.id,
        type: absence.type,
        recordedAt: absence.recordedAt,
      },
    })
  } catch (error) {
    console.error('Error recording manual attendance:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: 'Invalid request data', errors: error.errors },
        { status: 400 }
      )
    }

    if (error instanceof NotFoundError) {
      return NextResponse.json({ message: error.message }, { status: 404 })
    }

    if (error instanceof ValidationError) {
      return NextResponse.json({ message: error.message }, { status: 400 })
    }

    return NextResponse.json({ message: 'Internal server error' }, { status: 500 })
  }
}
