'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Skeleton } from '@/components/ui/skeleton'
import { useToast } from '@/hooks/use-toast'
import { useAdminSession } from '@/hooks/use-admin-session'
import {
  Download,
  RefreshCw,
  Users,
  UserCheck,
  Target,
  TrendingUp,
  TrendingDown,
  Minus,
} from 'lucide-react'
import { format } from 'date-fns'

// Import chart components
import {
  Line<PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
} from 'recharts'

// Prayer Analytics Interfaces
interface PrayerKPIs {
  totalStudents: number
  presentToday: number
  zuhrCompliance: number
  asrCompliance: number
  dismissalCompliance: number
  weeklyTrend: number
  alerts: number
}

interface PrayerTrendData {
  date: string
  attendance: number
  zuhr: number
  asr: number
  dismissal: number
}

interface ClassPerformance {
  className: string
  totalStudents: number
  attendanceRate: number
  prayerCompliance: number
  riskLevel: 'low' | 'medium' | 'high'
  trend: 'up' | 'down' | 'stable'
}

interface TopStudent {
  name: string
  className: string
  attendanceRate: number
  prayerCompliance: number
  streak: number
}

interface PrayerAnalytics {
  kpis: PrayerKPIs
  trendData: PrayerTrendData[]
  classPerformance: ClassPerformance[]
  topStudents: TopStudent[]
  insights: string[]
}

export default function PrayerReportsPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { admin, loading: sessionLoading } = useAdminSession()

  // State management for analytics
  const [analytics, setAnalytics] = useState<PrayerAnalytics>({
    kpis: {
      totalStudents: 0,
      presentToday: 0,
      zuhrCompliance: 0,
      asrCompliance: 0,
      dismissalCompliance: 0,
      weeklyTrend: 0,
      alerts: 0,
    },
    trendData: [],
    classPerformance: [],
    topStudents: [],
    insights: [],
  })
  const [isLoading, setIsLoading] = useState(true)
  const [classFilter, setClassFilter] = useState('all')
  const [dateRange, setDateRange] = useState('30')
  const [isExporting, setIsExporting] = useState(false)

  // Role-based access control
  useEffect(() => {
    if (!sessionLoading && admin) {
      // Only Super Admin and Admin can access prayer reports
      if (admin.role !== 'super_admin' && admin.role !== 'admin') {
        router.push('/admin/home')
        return
      }
    }
  }, [admin, sessionLoading, router])

  // Fetch prayer analytics data
  const fetchAnalytics = async () => {
    try {
      setIsLoading(true)

      const queryParams = new URLSearchParams()
      queryParams.append('reportType', 'prayer')
      queryParams.append('dateRange', dateRange)
      if (classFilter !== 'all') {
        queryParams.append('class', classFilter.replace(/-/g, ' ').toUpperCase())
      }
      queryParams.append('_t', Date.now().toString())

      const response = await fetch(`/api/analytics/dashboard?${queryParams.toString()}`, {
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
          Expires: '0',
        },
      })

      if (!response.ok) {
        throw new Error('Failed to fetch prayer analytics')
      }

      const data = await response.json()
      setAnalytics(data)
    } catch (error) {
      console.error('Error fetching prayer analytics:', error)
      toast({
        title: 'Gagal memuat analitik shalat',
        description: 'Terjadi kesalahan saat mengambil data analitik',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Export prayer analytics to CSV
  const handleExport = async () => {
    try {
      setIsExporting(true)

      const response = await fetch('/api/analytics/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reportType: 'prayer',
          dateRange,
          classFilter: classFilter !== 'all' ? classFilter : undefined,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to export prayer analytics')
      }

      const blob = await response.blob()
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `prayer-analytics-${format(new Date(), 'yyyy-MM-dd')}.csv`
      link.click()
      URL.revokeObjectURL(url)

      toast({
        title: 'Analitik shalat berhasil diekspor',
        description: 'File CSV telah diunduh ke perangkat Anda',
      })
    } catch (error) {
      console.error('Error exporting prayer analytics:', error)
      toast({
        title: 'Gagal mengekspor analitik',
        description: 'Terjadi kesalahan saat mengekspor data',
        variant: 'destructive',
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Load data on component mount and when filters change
  useEffect(() => {
    if (admin && (admin.role === 'super_admin' || admin.role === 'admin')) {
      fetchAnalytics()
    }
  }, [admin, dateRange, classFilter])

  // Show loading state
  if (sessionLoading || !admin) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-8 w-64" />
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className="h-32" />
          ))}
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    )
  }

  // Access denied for non-authorized roles
  if (admin.role !== 'super_admin' && admin.role !== 'admin') {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <h2 className="mb-2 text-xl font-semibold text-red-600">Akses Ditolak</h2>
          <p className="text-gray-600">Anda tidak memiliki izin untuk mengakses analitik shalat.</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Analitik Shalat</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Dashboard analitik kehadiran shalat siswa
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={fetchAnalytics} disabled={isLoading} variant="outline">
            {isLoading ? (
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="mr-2 h-4 w-4" />
            )}
            Refresh
          </Button>
          <Button
            onClick={handleExport}
            disabled={isExporting}
            className="bg-green-600 hover:bg-green-700"
          >
            {isExporting ? (
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Download className="mr-2 h-4 w-4" />
            )}
            Ekspor CSV
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col gap-4 md:flex-row">
        <Select value={classFilter} onValueChange={setClassFilter}>
          <SelectTrigger className="w-full md:w-48">
            <SelectValue placeholder="Pilih Kelas" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Semua Kelas</SelectItem>
            <SelectItem value="x-tkj-1">X TKJ 1</SelectItem>
            <SelectItem value="x-tkj-2">X TKJ 2</SelectItem>
            <SelectItem value="xi-tkj-1">XI TKJ 1</SelectItem>
            <SelectItem value="xi-tkj-2">XI TKJ 2</SelectItem>
            <SelectItem value="xii-tkj-1">XII TKJ 1</SelectItem>
            <SelectItem value="xii-tkj-2">XII TKJ 2</SelectItem>
          </SelectContent>
        </Select>
        <Select value={dateRange} onValueChange={setDateRange}>
          <SelectTrigger className="w-full md:w-48">
            <SelectValue placeholder="Periode" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7">7 Hari Terakhir</SelectItem>
            <SelectItem value="30">30 Hari Terakhir</SelectItem>
            <SelectItem value="90">3 Bulan Terakhir</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-blue-600">
                  {analytics.kpis.totalStudents}
                </div>
                <div className="text-sm font-medium text-gray-600">Total Siswa</div>
                <div className="mt-1 text-xs text-gray-500">Siswa aktif</div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-green-600">
                  {analytics.kpis.presentToday}
                </div>
                <div className="text-sm font-medium text-gray-600">Hadir Hari Ini</div>
                <div className="mt-1 text-xs text-gray-500">Siswa hadir</div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <UserCheck className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-emerald-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-emerald-600">
                  {analytics.kpis.zuhrCompliance}%
                </div>
                <div className="text-sm font-medium text-gray-600">Kepatuhan Zuhur</div>
                <div className="mt-1 text-xs text-gray-500">Rata-rata kehadiran</div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-emerald-100">
                <Target className="h-6 w-6 text-emerald-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-amber-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center gap-2">
                  <div className="text-3xl font-bold text-amber-600">
                    {analytics.kpis.weeklyTrend}%
                  </div>
                  {analytics.kpis.weeklyTrend > 0 ? (
                    <TrendingUp className="h-5 w-5 text-green-600" />
                  ) : analytics.kpis.weeklyTrend < 0 ? (
                    <TrendingDown className="h-5 w-5 text-red-600" />
                  ) : (
                    <Minus className="h-5 w-5 text-gray-600" />
                  )}
                </div>
                <div className="text-sm font-medium text-gray-600">Tren Mingguan</div>
                <div className="mt-1 text-xs text-gray-500">Perubahan dari minggu lalu</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Attendance Trend Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Tren Kehadiran Shalat</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={analytics.trendData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Line
                    type="monotone"
                    dataKey="zuhr"
                    stroke="#10b981"
                    strokeWidth={2}
                    name="Zuhur"
                  />
                  <Line type="monotone" dataKey="asr" stroke="#3b82f6" strokeWidth={2} name="Asr" />
                  <Line
                    type="monotone"
                    dataKey="dismissal"
                    stroke="#f59e0b"
                    strokeWidth={2}
                    name="Pulang"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Class Performance Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Performa Kelas</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={analytics.classPerformance}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="className" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="prayerCompliance" fill="#10b981" name="Kepatuhan Shalat %" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Students and Insights */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Top Students */}
        <Card>
          <CardHeader>
            <CardTitle>Siswa Terbaik</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.topStudents.map((student, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between rounded-lg bg-gray-50 p-3"
                >
                  <div>
                    <div className="font-semibold text-gray-900">{student.name}</div>
                    <div className="text-sm text-gray-600">{student.className}</div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-green-600">{student.prayerCompliance}%</div>
                    <div className="text-xs text-gray-500">{student.streak} hari berturut</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Insights */}
        <Card>
          <CardHeader>
            <CardTitle>Wawasan & Rekomendasi</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.insights.map((insight, index) => (
                <div key={index} className="rounded border-l-4 border-blue-400 bg-blue-50 p-3">
                  <p className="text-sm text-gray-700">{insight}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
