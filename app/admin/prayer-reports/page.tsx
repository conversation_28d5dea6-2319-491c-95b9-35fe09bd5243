'use client'

import { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { AdminLayout } from '@/components/layouts/admin-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { useToast } from '@/hooks/use-toast'
import { useAdminSession } from '@/hooks/use-admin-session'
import { Calendar, Download, Search, Filter, Refresh<PERSON><PERSON>, <PERSON>rash2, <PERSON>, <PERSON><PERSON>ff } from 'lucide-react'
import { format } from 'date-fns'
import { id } from 'date-fns/locale'
import { AttendanceType } from '@/lib/domain/entities/absence'

// Prayer Report Interface
interface PrayerReport {
  uniqueCode: string
  name: string
  className: string
  summaryDate: string
  zuhr: boolean
  zuhrTime: string | null
  asr: boolean
  asrTime: string | null
  dismissal: boolean
  dismissalTime: string | null
  ijin: boolean
  ijinTime?: string | null
}

// Prayer Statistics Interface
interface PrayerStats {
  total: number
  zuhr: number
  asr: number
  dismissal: number
  ijin: number
  zuhrPercentage?: number
  asrPercentage?: number
  dismissalPercentage?: number
  ijinPercentage?: number
}

export default function PrayerReportsPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { admin, loading: sessionLoading } = useAdminSession()

  // State management
  const [reports, setReports] = useState<PrayerReport[]>([])
  const [stats, setStats] = useState<PrayerStats>({
    total: 0,
    zuhr: 0,
    asr: 0,
    dismissal: 0,
    ijin: 0,
  })
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [classFilter, setClassFilter] = useState('all')
  const [date, setDate] = useState('today')
  const [isExporting, setIsExporting] = useState(false)

  // Role-based access control
  useEffect(() => {
    if (!sessionLoading && admin) {
      // Only Super Admin and Admin can access prayer reports
      if (admin.role !== 'super_admin' && admin.role !== 'admin') {
        router.push('/admin/home')
        return
      }
    }
  }, [admin, sessionLoading, router])

  // Fetch prayer reports data
  const fetchReports = async () => {
    try {
      setIsLoading(true)

      const queryParams = new URLSearchParams()
      queryParams.append('date', date)
      queryParams.append('reportType', 'prayer') // Only prayer data
      if (classFilter !== 'all') {
        queryParams.append('class', classFilter.replace(/-/g, ' ').toUpperCase())
      }
      queryParams.append('_t', Date.now().toString())

      const response = await fetch(`/api/absence/reports?${queryParams.toString()}`, {
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
          Expires: '0',
        },
      })

      if (!response.ok) {
        throw new Error('Failed to fetch prayer reports')
      }

      const data = await response.json()
      setReports(data)

      // Calculate prayer statistics
      const total = data.length
      const zuhrCount = data.filter((r: PrayerReport) => r.zuhr).length
      const asrCount = data.filter((r: PrayerReport) => r.asr).length
      const dismissalCount = data.filter((r: PrayerReport) => r.dismissal).length
      const ijinCount = data.filter((r: PrayerReport) => r.ijin).length

      setStats({
        total,
        zuhr: zuhrCount,
        asr: asrCount,
        dismissal: dismissalCount,
        ijin: ijinCount,
        zuhrPercentage: total > 0 ? Math.round((zuhrCount / (total - ijinCount || 1)) * 100) : 0,
        asrPercentage: total > 0 ? Math.round((asrCount / (total - ijinCount || 1)) * 100) : 0,
        dismissalPercentage: total > 0 ? Math.round((dismissalCount / total) * 100) : 0,
        ijinPercentage: total > 0 ? Math.round((ijinCount / total) * 100) : 0,
      })
    } catch (error) {
      console.error('Error fetching prayer reports:', error)
      toast({
        title: 'Gagal memuat laporan shalat',
        description: 'Terjadi kesalahan saat mengambil data laporan',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Export prayer reports to CSV
  const handleExport = async () => {
    try {
      setIsExporting(true)

      // Generate prayer-specific CSV content
      const csvContent = generatePrayerCSV(reports, stats)

      // Create download
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `laporan-shalat-${format(new Date(), 'yyyy-MM-dd')}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast({
        title: 'Laporan shalat berhasil diekspor',
        description: 'File CSV telah diunduh ke perangkat Anda',
      })
    } catch (error) {
      console.error('Error exporting prayer reports:', error)
      toast({
        title: 'Gagal mengekspor laporan',
        description: 'Terjadi kesalahan saat mengekspor data',
        variant: 'destructive',
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Generate prayer-specific CSV
  const generatePrayerCSV = (reports: PrayerReport[], stats: PrayerStats): string => {
    const headers = [
      'No',
      'Kode_Siswa',
      'Nama_Lengkap',
      'Kelas',
      'Tanggal',
      'Shalat_Zuhur',
      'Waktu_Zuhur',
      'Shalat_Asr',
      'Waktu_Asr',
      'Pulang',
      'Waktu_Pulang',
      'Status_Ijin',
      'Waktu_Ijin',
      'Tingkat_Kepatuhan',
    ]

    const rows = reports.map((report, index) => {
      const compliance = report.ijin
        ? 'IJIN'
        : report.zuhr && report.asr
          ? 'BAIK'
          : report.zuhr || report.asr
            ? 'CUKUP'
            : 'KURANG'

      return [
        index + 1,
        report.uniqueCode,
        report.name,
        report.className,
        report.summaryDate,
        report.zuhr ? '✓' : '✗',
        report.zuhrTime || '-',
        report.asr ? '✓' : '✗',
        report.asrTime || '-',
        report.dismissal ? '✓' : '✗',
        report.dismissalTime || '-',
        report.ijin ? '✓' : '✗',
        report.ijinTime || '-',
        compliance,
      ]
    })

    // Create CSV content with metadata
    const metadata = [
      ['=== LAPORAN ABSENSI SHALAT ==='],
      ['SMK Negeri 3 Banjarmasin'],
      [''],
      ['STATISTIK KEHADIRAN SHALAT'],
      [`Total Siswa: ${stats.total}`],
      [`Shalat Zuhur: ${stats.zuhr} (${stats.zuhrPercentage}%)`],
      [`Shalat Asr: ${stats.asr} (${stats.asrPercentage}%)`],
      [`Pulang: ${stats.dismissal} (${stats.dismissalPercentage}%)`],
      [`Ijin: ${stats.ijin} (${stats.ijinPercentage}%)`],
      [''],
      ['=== DETAIL DATA ==='],
      [''],
    ]

    return [
      ...metadata.map(row => row.join(',')),
      headers.join(','),
      ...rows.map(row => row.join(',')),
    ].join('\n')
  }

  // Filter reports based on search
  const filteredReports = reports.filter(report => {
    if (!searchQuery) return true
    const query = searchQuery.toLowerCase()
    return (
      report.name.toLowerCase().includes(query) ||
      report.uniqueCode.toLowerCase().includes(query) ||
      report.className.toLowerCase().includes(query)
    )
  })

  // Load data on component mount and when filters change
  useEffect(() => {
    if (admin && (admin.role === 'super_admin' || admin.role === 'admin')) {
      fetchReports()
    }
  }, [admin, date, classFilter])

  // Show loading state
  if (sessionLoading || !admin) {
    return (
      <AdminLayout>
        <div className="space-y-6">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-96 w-full" />
        </div>
      </AdminLayout>
    )
  }

  // Access denied for non-authorized roles
  if (admin.role !== 'super_admin' && admin.role !== 'admin') {
    return (
      <AdminLayout>
        <Card>
          <CardContent className="p-6 text-center">
            <h2 className="mb-2 text-xl font-semibold text-red-600">Akses Ditolak</h2>
            <p className="text-gray-600">
              Anda tidak memiliki izin untuk mengakses laporan shalat.
            </p>
          </CardContent>
        </Card>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              Laporan Absensi Shalat
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Kelola dan pantau kehadiran shalat siswa
            </p>
          </div>
          <Button
            onClick={handleExport}
            disabled={isExporting || reports.length === 0}
            className="bg-green-600 hover:bg-green-700"
          >
            {isExporting ? (
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Download className="mr-2 h-4 w-4" />
            )}
            Ekspor CSV
          </Button>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-5">
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
              <div className="text-sm text-gray-600">Total Siswa</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-600">{stats.zuhr}</div>
              <div className="text-sm text-gray-600">Shalat Zuhur ({stats.zuhrPercentage}%)</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-600">{stats.asr}</div>
              <div className="text-sm text-gray-600">Shalat Asr ({stats.asrPercentage}%)</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-purple-600">{stats.dismissal}</div>
              <div className="text-sm text-gray-600">Pulang ({stats.dismissalPercentage}%)</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-orange-600">{stats.ijin}</div>
              <div className="text-sm text-gray-600">Ijin ({stats.ijinPercentage}%)</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col gap-4 md:flex-row">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                  <Input
                    placeholder="Cari nama siswa atau kode..."
                    value={searchQuery}
                    onChange={e => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={date} onValueChange={setDate}>
                <SelectTrigger className="w-full md:w-48">
                  <Calendar className="mr-2 h-4 w-4" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">Hari Ini</SelectItem>
                  <SelectItem value="yesterday">Kemarin</SelectItem>
                  <SelectItem value="week">Minggu Ini</SelectItem>
                </SelectContent>
              </Select>
              <Select value={classFilter} onValueChange={setClassFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Kelas</SelectItem>
                  <SelectItem value="x-tkj-1">X TKJ 1</SelectItem>
                  <SelectItem value="x-tkj-2">X TKJ 2</SelectItem>
                  <SelectItem value="xi-tkj-1">XI TKJ 1</SelectItem>
                  <SelectItem value="xi-tkj-2">XI TKJ 2</SelectItem>
                  <SelectItem value="xii-tkj-1">XII TKJ 1</SelectItem>
                  <SelectItem value="xii-tkj-2">XII TKJ 2</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Prayer Reports Table */}
        <Card>
          <CardHeader>
            <CardTitle>Data Absensi Shalat ({filteredReports.length} siswa)</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <Skeleton key={i} className="h-12 w-full" />
                ))}
              </div>
            ) : filteredReports.length === 0 ? (
              <div className="py-8 text-center text-gray-500">Tidak ada data laporan shalat</div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-16">No</TableHead>
                      <TableHead className="min-w-32">Kode</TableHead>
                      <TableHead className="min-w-48">Nama</TableHead>
                      <TableHead className="min-w-24">Kelas</TableHead>
                      <TableHead className="min-w-20 text-center">Zuhur</TableHead>
                      <TableHead className="min-w-20 text-center">Asr</TableHead>
                      <TableHead className="min-w-20 text-center">Pulang</TableHead>
                      <TableHead className="min-w-20 text-center">Ijin</TableHead>
                      <TableHead className="min-w-24 text-center">Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredReports.map((report, index) => {
                      const compliance = report.ijin
                        ? 'IJIN'
                        : report.zuhr && report.asr
                          ? 'BAIK'
                          : report.zuhr || report.asr
                            ? 'CUKUP'
                            : 'KURANG'

                      const complianceColor = report.ijin
                        ? 'bg-orange-100 text-orange-800'
                        : report.zuhr && report.asr
                          ? 'bg-green-100 text-green-800'
                          : report.zuhr || report.asr
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'

                      return (
                        <TableRow key={report.uniqueCode} className="hover:bg-gray-50">
                          <TableCell className="font-medium">{index + 1}</TableCell>
                          <TableCell className="font-mono text-sm">{report.uniqueCode}</TableCell>
                          <TableCell className="font-medium">{report.name}</TableCell>
                          <TableCell>{report.className}</TableCell>
                          <TableCell className="text-center">
                            {report.zuhr ? (
                              <div className="flex flex-col items-center">
                                <Badge
                                  variant="secondary"
                                  className="mb-1 bg-green-100 text-green-800"
                                >
                                  ✓
                                </Badge>
                                <span className="text-xs text-gray-500">{report.zuhrTime}</span>
                              </div>
                            ) : (
                              <Badge variant="secondary" className="bg-red-100 text-red-800">
                                ✗
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell className="text-center">
                            {report.asr ? (
                              <div className="flex flex-col items-center">
                                <Badge
                                  variant="secondary"
                                  className="mb-1 bg-green-100 text-green-800"
                                >
                                  ✓
                                </Badge>
                                <span className="text-xs text-gray-500">{report.asrTime}</span>
                              </div>
                            ) : (
                              <Badge variant="secondary" className="bg-red-100 text-red-800">
                                ✗
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell className="text-center">
                            {report.dismissal ? (
                              <div className="flex flex-col items-center">
                                <Badge
                                  variant="secondary"
                                  className="mb-1 bg-purple-100 text-purple-800"
                                >
                                  ✓
                                </Badge>
                                <span className="text-xs text-gray-500">
                                  {report.dismissalTime}
                                </span>
                              </div>
                            ) : (
                              <Badge variant="secondary" className="bg-red-100 text-red-800">
                                ✗
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell className="text-center">
                            {report.ijin ? (
                              <div className="flex flex-col items-center">
                                <Badge
                                  variant="secondary"
                                  className="mb-1 bg-orange-100 text-orange-800"
                                >
                                  ✓
                                </Badge>
                                <span className="text-xs text-gray-500">{report.ijinTime}</span>
                              </div>
                            ) : (
                              <Badge variant="secondary" className="bg-gray-100 text-gray-800">
                                ✗
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell className="text-center">
                            <Badge variant="secondary" className={complianceColor}>
                              {compliance}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
