'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useToast } from '@/hooks/use-toast'
import { useAdminSession } from '@/hooks/use-admin-session'
import {
  Download,
  RefreshCw,
  Users,
  UserCheck,
  Target,
  Search,
  Calendar,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react'
import { format } from 'date-fns'
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  Pa<PERSON>ationLink,
  Pa<PERSON>ationNex<PERSON>,
  PaginationPrevious,
} from '@/components/ui/pagination'

// Import chart components
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
} from 'recharts'

// Prayer Report Interface (Real Data Structure)
interface PrayerReport {
  uniqueCode: string
  name: string
  className: string
  summaryDate: string
  zuhr: boolean
  zuhrTime: string | null
  asr: boolean
  asrTime: string | null
  dismissal: boolean
  dismissalTime: string | null
  ijin: boolean
  ijinTime?: string | null
}

// Prayer Statistics Interface
interface PrayerStats {
  total: number
  zuhr: number
  asr: number
  dismissal: number
  ijin: number
  zuhrPercentage: number
  asrPercentage: number
  dismissalPercentage: number
  ijinPercentage: number
}

// Trend Data for Charts
interface PrayerTrendData {
  date: string
  zuhr: number
  asr: number
  dismissal: number
}

export default function PrayerReportsPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { admin, loading: sessionLoading } = useAdminSession()

  // State management for real data
  const [reports, setReports] = useState<PrayerReport[]>([])
  const [stats, setStats] = useState<PrayerStats>({
    total: 0,
    zuhr: 0,
    asr: 0,
    dismissal: 0,
    ijin: 0,
    zuhrPercentage: 0,
    asrPercentage: 0,
    dismissalPercentage: 0,
    ijinPercentage: 0,
  })
  const [trendData, setTrendData] = useState<PrayerTrendData[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [classFilter, setClassFilter] = useState('all')
  const [date, setDate] = useState('today')
  const [searchQuery, setSearchQuery] = useState('')
  const [isExporting, setIsExporting] = useState(false)

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(50) // 50 students per page for 3000 students

  // Role-based access control
  useEffect(() => {
    if (!sessionLoading && admin) {
      // Only Super Admin and Admin can access prayer reports
      if (admin.role !== 'super_admin' && admin.role !== 'admin') {
        router.push('/admin/home')
        return
      }
    }
  }, [admin, sessionLoading, router])

  // Fetch prayer reports data
  const fetchReports = async () => {
    try {
      setIsLoading(true)

      const queryParams = new URLSearchParams()
      queryParams.append('date', date)
      queryParams.append('reportType', 'prayer') // Only prayer data
      if (classFilter !== 'all') {
        queryParams.append('class', classFilter.replace(/-/g, ' ').toUpperCase())
      }
      queryParams.append('_t', Date.now().toString())

      const response = await fetch(`/api/absence/reports?${queryParams.toString()}`, {
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
          Expires: '0',
        },
      })

      if (!response.ok) {
        throw new Error('Failed to fetch prayer reports')
      }

      const data = await response.json()
      setReports(data)

      // Calculate prayer statistics
      const total = data.length
      const zuhrCount = data.filter((r: PrayerReport) => r.zuhr).length
      const asrCount = data.filter((r: PrayerReport) => r.asr).length
      const dismissalCount = data.filter((r: PrayerReport) => r.dismissal).length
      const ijinCount = data.filter((r: PrayerReport) => r.ijin).length

      setStats({
        total,
        zuhr: zuhrCount,
        asr: asrCount,
        dismissal: dismissalCount,
        ijin: ijinCount,
        zuhrPercentage: total > 0 ? Math.round((zuhrCount / (total - ijinCount || 1)) * 100) : 0,
        asrPercentage: total > 0 ? Math.round((asrCount / (total - ijinCount || 1)) * 100) : 0,
        dismissalPercentage: total > 0 ? Math.round((dismissalCount / total) * 100) : 0,
        ijinPercentage: total > 0 ? Math.round((ijinCount / total) * 100) : 0,
      })

      // Generate trend data (mock for now, can be enhanced with real historical data)
      const mockTrendData: PrayerTrendData[] = [
        { date: '2024-01-01', zuhr: zuhrCount, asr: asrCount, dismissal: dismissalCount },
        {
          date: '2024-01-02',
          zuhr: Math.max(0, zuhrCount - 5),
          asr: Math.max(0, asrCount - 3),
          dismissal: Math.max(0, dismissalCount - 2),
        },
        {
          date: '2024-01-03',
          zuhr: Math.max(0, zuhrCount + 2),
          asr: Math.max(0, asrCount + 1),
          dismissal: Math.max(0, dismissalCount + 3),
        },
        { date: '2024-01-04', zuhr: zuhrCount, asr: asrCount, dismissal: dismissalCount },
        {
          date: '2024-01-05',
          zuhr: Math.max(0, zuhrCount - 1),
          asr: Math.max(0, asrCount - 2),
          dismissal: Math.max(0, dismissalCount - 1),
        },
      ]
      setTrendData(mockTrendData)
    } catch (error) {
      console.error('Error fetching prayer reports:', error)
      toast({
        title: 'Gagal memuat laporan shalat',
        description: 'Terjadi kesalahan saat mengambil data laporan',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Export prayer reports to CSV
  const handleExport = async () => {
    try {
      setIsExporting(true)

      // Generate prayer-specific CSV content
      const csvContent = generatePrayerCSV(reports, stats)

      // Create download
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `laporan-shalat-${format(new Date(), 'yyyy-MM-dd')}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast({
        title: 'Laporan shalat berhasil diekspor',
        description: 'File CSV telah diunduh ke perangkat Anda',
      })
    } catch (error) {
      console.error('Error exporting prayer reports:', error)
      toast({
        title: 'Gagal mengekspor laporan',
        description: 'Terjadi kesalahan saat mengekspor data',
        variant: 'destructive',
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Generate prayer-specific CSV
  const generatePrayerCSV = (reports: PrayerReport[], stats: PrayerStats): string => {
    const reportDate =
      date === 'today' ? 'Hari Ini' : date === 'yesterday' ? 'Kemarin' : 'Minggu Ini'
    const className = classFilter === 'all' ? 'Semua Kelas' : classFilter

    const headers = [
      'No',
      'Kode_Siswa',
      'Nama_Lengkap',
      'Kelas',
      'Tanggal',
      'Shalat_Zuhur',
      'Waktu_Zuhur',
      'Shalat_Asr',
      'Waktu_Asr',
      'Absen_Pulang',
      'Waktu_Pulang',
      'Status_Ijin',
      'Waktu_Ijin',
      'Skor_Shalat',
    ]

    const rows = reports.map((report, index) => {
      const prayerCount = (report.zuhr ? 1 : 0) + (report.asr ? 1 : 0)
      const prayerScore = report.ijin ? 100 : Math.round((prayerCount / 2) * 100)

      return [
        index + 1,
        report.uniqueCode,
        report.name,
        report.className,
        report.summaryDate,
        report.zuhr ? 'HADIR' : 'TIDAK_HADIR',
        report.zuhrTime || '-',
        report.asr ? 'HADIR' : 'TIDAK_HADIR',
        report.asrTime || '-',
        report.dismissal ? 'SUDAH' : 'BELUM',
        report.dismissalTime || '-',
        report.ijin ? 'YA' : 'TIDAK',
        report.ijinTime || '-',
        `${prayerScore}%`,
      ]
    })

    const csvContent = [
      `=== LAPORAN SHALAT - ${reportDate} ===`,
      `Filter Kelas: ${className}`,
      `Total Siswa: ${stats.total}`,
      `Zuhur: ${stats.zuhr} (${stats.zuhrPercentage}%)`,
      `Asr: ${stats.asr} (${stats.asrPercentage}%)`,
      `Pulang: ${stats.dismissal} (${stats.dismissalPercentage}%)`,
      '',
      headers.join(','),
      ...rows.map(row => row.join(',')),
    ].join('\n')

    return csvContent
  }

  // Filter reports based on search
  const filteredReports = reports.filter(report => {
    if (!searchQuery) return true
    const query = searchQuery.toLowerCase()
    return (
      report.name.toLowerCase().includes(query) ||
      report.uniqueCode.toLowerCase().includes(query) ||
      report.className.toLowerCase().includes(query)
    )
  })

  // Pagination logic
  const totalPages = Math.ceil(filteredReports.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const paginatedReports = filteredReports.slice(startIndex, endIndex)

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [classFilter, date, searchQuery])

  // Load data on component mount and when filters change
  useEffect(() => {
    if (admin && (admin.role === 'super_admin' || admin.role === 'admin')) {
      fetchReports()
    }
  }, [admin, date, classFilter])

  // Show loading state
  if (sessionLoading || !admin) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-8 w-64" />
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className="h-32" />
          ))}
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    )
  }

  // Access denied for non-authorized roles
  if (admin.role !== 'super_admin' && admin.role !== 'admin') {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <h2 className="mb-2 text-xl font-semibold text-red-600">Akses Ditolak</h2>
          <p className="text-gray-600">Anda tidak memiliki izin untuk mengakses laporan shalat.</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Analitik Shalat</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Dashboard analitik kehadiran shalat siswa
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={fetchReports} disabled={isLoading} variant="outline">
            {isLoading ? (
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="mr-2 h-4 w-4" />
            )}
            Refresh
          </Button>
          <Button
            onClick={handleExport}
            disabled={isExporting}
            className="bg-green-600 hover:bg-green-700"
          >
            {isExporting ? (
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Download className="mr-2 h-4 w-4" />
            )}
            Ekspor CSV
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col gap-4 md:flex-row">
        <Select value={classFilter} onValueChange={setClassFilter}>
          <SelectTrigger className="w-full md:w-48">
            <SelectValue placeholder="Pilih Kelas" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Semua Kelas</SelectItem>
            <SelectItem value="x-tkj-1">X TKJ 1</SelectItem>
            <SelectItem value="x-tkj-2">X TKJ 2</SelectItem>
            <SelectItem value="xi-tkj-1">XI TKJ 1</SelectItem>
            <SelectItem value="xi-tkj-2">XI TKJ 2</SelectItem>
            <SelectItem value="xii-tkj-1">XII TKJ 1</SelectItem>
            <SelectItem value="xii-tkj-2">XII TKJ 2</SelectItem>
          </SelectContent>
        </Select>
        <Select value={date} onValueChange={setDate}>
          <SelectTrigger className="w-full md:w-48">
            <SelectValue placeholder="Periode" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="today">Hari Ini</SelectItem>
            <SelectItem value="yesterday">Kemarin</SelectItem>
            <SelectItem value="week">Minggu Ini</SelectItem>
            <SelectItem value="30days">30 Hari Terakhir</SelectItem>
            <SelectItem value="monthly">Bulanan</SelectItem>
            <SelectItem value="yearly">Tahunan</SelectItem>
          </SelectContent>
        </Select>
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
          <Input
            placeholder="Cari siswa..."
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-blue-600">{stats.total}</div>
                <div className="text-sm font-medium text-gray-600">Total Siswa</div>
                <div className="mt-1 text-xs text-gray-500">Siswa aktif</div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-green-600">{stats.zuhr}</div>
                <div className="text-sm font-medium text-gray-600">Shalat Zuhur</div>
                <div className="mt-1 text-xs text-gray-500">{stats.zuhrPercentage}% kehadiran</div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <UserCheck className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-emerald-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-emerald-600">{stats.asr}</div>
                <div className="text-sm font-medium text-gray-600">Shalat Asr</div>
                <div className="mt-1 text-xs text-gray-500">{stats.asrPercentage}% kehadiran</div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-emerald-100">
                <Target className="h-6 w-6 text-emerald-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-amber-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-amber-600">{stats.dismissal}</div>
                <div className="text-sm font-medium text-gray-600">Absen Pulang</div>
                <div className="mt-1 text-xs text-gray-500">{stats.dismissalPercentage}% siswa</div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-amber-100">
                <Calendar className="h-6 w-6 text-amber-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Prayer Trend Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Tren Kehadiran Shalat</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={trendData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="zuhr"
                  stroke="#10b981"
                  strokeWidth={2}
                  name="Zuhur"
                />
                <Line type="monotone" dataKey="asr" stroke="#3b82f6" strokeWidth={2} name="Asr" />
                <Line
                  type="monotone"
                  dataKey="dismissal"
                  stroke="#f59e0b"
                  strokeWidth={2}
                  name="Pulang"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Student Data Matrix - Moved to Bottom */}
      <Card>
        <CardHeader>
          <CardTitle>Data Siswa Shalat ({filteredReports.length} siswa)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>No</TableHead>
                  <TableHead>Kode Siswa</TableHead>
                  <TableHead>Nama</TableHead>
                  <TableHead>Kelas</TableHead>
                  <TableHead>Zuhur</TableHead>
                  <TableHead>Asr</TableHead>
                  <TableHead>Pulang</TableHead>
                  <TableHead>Ijin</TableHead>
                  <TableHead>Skor</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  [...Array(5)].map((_, i) => (
                    <TableRow key={i}>
                      <TableCell>
                        <Skeleton className="h-4 w-8" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-20" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-32" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-16" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-16" />
                      </TableCell>
                    </TableRow>
                  ))
                ) : paginatedReports.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} className="py-8 text-center text-gray-500">
                      Tidak ada data siswa ditemukan
                    </TableCell>
                  </TableRow>
                ) : (
                  paginatedReports.map((report, index) => {
                    const prayerCount = (report.zuhr ? 1 : 0) + (report.asr ? 1 : 0)
                    const prayerScore = report.ijin ? 100 : Math.round((prayerCount / 2) * 100)
                    const globalIndex = startIndex + index + 1

                    return (
                      <TableRow key={report.uniqueCode}>
                        <TableCell>{globalIndex}</TableCell>
                        <TableCell className="font-mono text-sm">{report.uniqueCode}</TableCell>
                        <TableCell className="font-medium">{report.name}</TableCell>
                        <TableCell>{report.className}</TableCell>
                        <TableCell>
                          <Badge variant={report.zuhr ? 'default' : 'secondary'}>
                            {report.zuhr ? '✓' : '✗'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={report.asr ? 'default' : 'secondary'}>
                            {report.asr ? '✓' : '✗'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={report.dismissal ? 'default' : 'secondary'}>
                            {report.dismissal ? '✓' : '✗'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={report.ijin ? 'outline' : 'secondary'}>
                            {report.ijin ? 'Ijin' : '-'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              prayerScore >= 80
                                ? 'default'
                                : prayerScore >= 50
                                  ? 'secondary'
                                  : 'destructive'
                            }
                          >
                            {prayerScore}%
                          </Badge>
                        </TableCell>
                      </TableRow>
                    )
                  })
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-4 flex items-center justify-between">
              <div className="text-sm text-gray-600">
                Menampilkan {startIndex + 1}-{Math.min(endIndex, filteredReports.length)} dari{' '}
                {filteredReports.length} siswa
              </div>
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      className={
                        currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'
                      }
                    />
                  </PaginationItem>

                  {/* Page numbers */}
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum
                    if (totalPages <= 5) {
                      pageNum = i + 1
                    } else if (currentPage <= 3) {
                      pageNum = i + 1
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i
                    } else {
                      pageNum = currentPage - 2 + i
                    }

                    return (
                      <PaginationItem key={pageNum}>
                        <PaginationLink
                          onClick={() => setCurrentPage(pageNum)}
                          isActive={currentPage === pageNum}
                          className="cursor-pointer"
                        >
                          {pageNum}
                        </PaginationLink>
                      </PaginationItem>
                    )
                  })}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      className={
                        currentPage === totalPages
                          ? 'pointer-events-none opacity-50'
                          : 'cursor-pointer'
                      }
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
