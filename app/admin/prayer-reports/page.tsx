'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { useToast } from '@/hooks/use-toast'
import { useAdminSession } from '@/hooks/use-admin-session'
import { Calendar, Download, Search, Filter, RefreshCw, Trash2, Eye, EyeOff } from 'lucide-react'
import { format } from 'date-fns'
import { id } from 'date-fns/locale'
import { AttendanceType } from '@/lib/domain/entities/absence'

// Prayer Report Interface
interface PrayerReport {
  uniqueCode: string
  name: string
  className: string
  summaryDate: string
  zuhr: boolean
  zuhrTime: string | null
  asr: boolean
  asrTime: string | null
  dismissal: boolean
  dismissalTime: string | null
  ijin: boolean
  ijinTime?: string | null
}

// Prayer Statistics Interface
interface PrayerStats {
  total: number
  zuhr: number
  asr: number
  dismissal: number
  ijin: number
  zuhrPercentage?: number
  asrPercentage?: number
  dismissalPercentage?: number
  ijinPercentage?: number
}

export default function PrayerReportsPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { admin, loading: sessionLoading } = useAdminSession()

  // State management
  const [reports, setReports] = useState<PrayerReport[]>([])
  const [stats, setStats] = useState<PrayerStats>({
    total: 0,
    zuhr: 0,
    asr: 0,
    dismissal: 0,
    ijin: 0,
  })
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [classFilter, setClassFilter] = useState('all')
  const [date, setDate] = useState('today')
  const [isExporting, setIsExporting] = useState(false)

  // Role-based access control
  useEffect(() => {
    if (!sessionLoading && admin) {
      // Only Super Admin and Admin can access prayer reports
      if (admin.role !== 'super_admin' && admin.role !== 'admin') {
        router.push('/admin/home')
        return
      }
    }
  }, [admin, sessionLoading, router])

  // Fetch prayer reports data
  const fetchReports = async () => {
    try {
      setIsLoading(true)

      const queryParams = new URLSearchParams()
      queryParams.append('date', date)
      queryParams.append('reportType', 'prayer') // Only prayer data
      if (classFilter !== 'all') {
        queryParams.append('class', classFilter.replace(/-/g, ' ').toUpperCase())
      }
      queryParams.append('_t', Date.now().toString())

      const response = await fetch(`/api/absence/reports?${queryParams.toString()}`, {
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
          Expires: '0',
        },
      })

      if (!response.ok) {
        throw new Error('Failed to fetch prayer reports')
      }

      const data = await response.json()
      setReports(data)

      // Calculate prayer statistics
      const total = data.length
      const zuhrCount = data.filter((r: PrayerReport) => r.zuhr).length
      const asrCount = data.filter((r: PrayerReport) => r.asr).length
      const dismissalCount = data.filter((r: PrayerReport) => r.dismissal).length
      const ijinCount = data.filter((r: PrayerReport) => r.ijin).length

      setStats({
        total,
        zuhr: zuhrCount,
        asr: asrCount,
        dismissal: dismissalCount,
        ijin: ijinCount,
        zuhrPercentage: total > 0 ? Math.round((zuhrCount / (total - ijinCount || 1)) * 100) : 0,
        asrPercentage: total > 0 ? Math.round((asrCount / (total - ijinCount || 1)) * 100) : 0,
        dismissalPercentage: total > 0 ? Math.round((dismissalCount / total) * 100) : 0,
        ijinPercentage: total > 0 ? Math.round((ijinCount / total) * 100) : 0,
      })
    } catch (error) {
      console.error('Error fetching prayer reports:', error)
      toast({
        title: 'Gagal memuat laporan shalat',
        description: 'Terjadi kesalahan saat mengambil data laporan',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Export prayer reports to CSV
  const handleExport = async () => {
    try {
      setIsExporting(true)

      // Generate prayer-specific CSV content
      const csvContent = generatePrayerCSV(reports, stats)

      // Create download
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `laporan-shalat-${format(new Date(), 'yyyy-MM-dd')}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast({
        title: 'Laporan shalat berhasil diekspor',
        description: 'File CSV telah diunduh ke perangkat Anda',
      })
    } catch (error) {
      console.error('Error exporting prayer reports:', error)
      toast({
        title: 'Gagal mengekspor laporan',
        description: 'Terjadi kesalahan saat mengekspor data',
        variant: 'destructive',
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Helper function to escape CSV values
  const escapeCsvValue = (value: string | number | null | undefined): string => {
    if (value === null || value === undefined) return '""'
    const stringValue = String(value)
    if (stringValue.includes('"') || stringValue.includes(',') || stringValue.includes('\n')) {
      return `"${stringValue.replace(/"/g, '""')}"`
    }
    return stringValue
  }

  // Helper function to get current WITA time
  const getCurrentWITATime = () => {
    return new Date(new Date().toLocaleString('en-US', { timeZone: 'Asia/Makassar' }))
  }

  // Generate prayer-specific CSV
  const generatePrayerCSV = (reports: PrayerReport[], stats: PrayerStats): string => {
    const reportDate =
      date === 'today' ? 'Hari Ini' : date === 'yesterday' ? 'Kemarin' : 'Minggu Ini'
    const className = classFilter === 'all' ? 'Semua Kelas' : classFilter
    const currentTime = getCurrentWITATime()
    const exportDate =
      currentTime.toLocaleDateString('id-ID', {
        day: '2-digit',
        month: 'long',
        year: 'numeric',
        timeZone: 'Asia/Makassar',
      }) +
      ' ' +
      currentTime.toLocaleTimeString('id-ID', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        timeZone: 'Asia/Makassar',
      }) +
      ' WITA'

    // Create metadata
    const metadata = [
      ['=== LAPORAN ABSENSI SHALAT ==='],
      ['SMK Negeri 3 Banjarmasin'],
      ['https://smkn3banjarmasin.sch.id/'],
      [''],
      ['INFORMASI LAPORAN'],
      [`Periode: ${reportDate}`],
      [`Filter Kelas: ${className}`],
      [`Total Siswa: ${stats.total} siswa`],
      [`Tanggal Ekspor: ${exportDate}`],
      [''],
      ['STATISTIK KEHADIRAN SHALAT'],
      [`Shalat Zuhur: ${stats.zuhr} siswa (${stats.zuhrPercentage}%)`],
      [`Shalat Asr: ${stats.asr} siswa (${stats.asrPercentage}%)`],
      [`Pulang: ${stats.dismissal} siswa (${stats.dismissalPercentage}%)`],
      [`Ijin: ${stats.ijin} siswa (${stats.ijinPercentage}%)`],
      [''],
      ['=== DETAIL DATA ABSENSI SHALAT ==='],
      [''],
    ]

    // Create headers with better information architecture
    const headers = [
      'No',
      'Kode_Siswa',
      'Nama_Lengkap',
      'Kelas',
      'Tanggal_Laporan',
      'Hari',
      'Status_Kehadiran_Shalat',
      'Shalat_Zuhur',
      'Waktu_Zuhur',
      'Shalat_Asr',
      'Waktu_Asr',
      'Skor_Ibadah_Persen',
      'Tingkat_Kepatuhan',
      'Jumlah_Shalat_Hadir',
      'Status_Ijin',
      'Waktu_Ijin',
      'Absen_Pulang',
      'Waktu_Pulang',
      'Analisis_Kehadiran',
      'Rekomendasi_Tindakan',
      'Keterangan_Lengkap',
    ]

    // Convert reports data to CSV rows
    const rows: string[][] = []

    reports.forEach((report, index) => {
      const dayName = report.summaryDate
        ? new Date(report.summaryDate).toLocaleDateString('id-ID', {
            weekday: 'long',
            timeZone: 'Asia/Makassar',
          })
        : '-'

      // Calculate prayer metrics for CSV
      const prayerCount = (report.zuhr ? 1 : 0) + (report.asr ? 1 : 0)
      const prayerScore = report.ijin ? 100 : Math.round((prayerCount / 2) * 100)

      // Determine compliance level
      const enhancedCompliance = report.ijin
        ? 'IJIN_KHUSUS'
        : prayerScore === 100
          ? 'SANGAT_BAIK'
          : prayerScore >= 50
            ? 'BAIK'
            : 'PERLU_PERHATIAN'

      // Generate attendance status
      const attendanceStatus = report.ijin
        ? 'BERHALANGAN_IJIN'
        : prayerCount === 2
          ? 'HADIR_LENGKAP'
          : prayerCount === 1
            ? 'HADIR_SEBAGIAN'
            : 'TIDAK_HADIR'

      // Generate analysis
      const analysis = report.ijin
        ? 'Siswa memiliki ijin khusus sehingga tidak wajib mengikuti shalat berjamaah'
        : prayerCount === 2
          ? 'Siswa menunjukkan kedisiplinan ibadah yang sangat baik'
          : prayerCount === 1
            ? 'Siswa hadir sebagian shalat, perlu motivasi untuk konsistensi'
            : 'Siswa tidak hadir shalat berjamaah, perlu perhatian khusus'

      // Generate recommendations
      const recommendations = []
      if (!report.ijin) {
        if (prayerCount === 0) {
          recommendations.push('Panggil siswa untuk konseling tentang pentingnya ibadah')
          recommendations.push('Koordinasi dengan wali kelas dan orang tua')
        } else if (prayerCount === 1) {
          recommendations.push('Berikan motivasi untuk konsistensi dalam beribadah')
          recommendations.push('Pantau kehadiran shalat minggu depan')
        } else {
          recommendations.push('Berikan apresiasi atas kedisiplinan ibadah')
          recommendations.push('Jadikan contoh untuk siswa lain')
        }
      } else {
        recommendations.push('Pantau kondisi siswa dan berikan dukungan yang diperlukan')
      }

      // Generate detailed notes
      const detailedNotes = []
      if (report.ijin) {
        detailedNotes.push('Status: Berhalangan hadir dengan ijin khusus')
        if (report.ijinTime) detailedNotes.push(`Waktu ijin: ${report.ijinTime}`)
      } else {
        detailedNotes.push(`Kehadiran shalat: ${prayerCount}/2 shalat wajib`)
        if (report.zuhr) detailedNotes.push(`Shalat Zuhur: Hadir (${report.zuhrTime})`)
        if (report.asr) detailedNotes.push(`Shalat Asr: Hadir (${report.asrTime})`)
        if (!report.zuhr) detailedNotes.push('Shalat Zuhur: Tidak hadir')
        if (!report.asr) detailedNotes.push('Shalat Asr: Tidak hadir')
      }

      if (report.dismissal) {
        detailedNotes.push(`Absen pulang: ${report.dismissalTime}`)
      } else {
        detailedNotes.push('Belum absen pulang')
      }

      rows.push([
        escapeCsvValue((index + 1).toString()),
        escapeCsvValue(report.uniqueCode),
        escapeCsvValue(report.name),
        escapeCsvValue(report.className),
        escapeCsvValue(report.summaryDate || '-'),
        escapeCsvValue(dayName),
        escapeCsvValue(attendanceStatus),
        escapeCsvValue(report.zuhr ? 'HADIR' : 'TIDAK_HADIR'),
        escapeCsvValue(report.zuhrTime || '-'),
        escapeCsvValue(report.asr ? 'HADIR' : 'TIDAK_HADIR'),
        escapeCsvValue(report.asrTime || '-'),
        escapeCsvValue(prayerScore.toString()),
        escapeCsvValue(enhancedCompliance),
        escapeCsvValue(`${prayerCount}/2`),
        escapeCsvValue(report.ijin ? 'YA' : 'TIDAK'),
        escapeCsvValue(report.ijinTime || '-'),
        escapeCsvValue(report.dismissal ? 'SUDAH' : 'BELUM'),
        escapeCsvValue(report.dismissalTime || '-'),
        escapeCsvValue(analysis),
        escapeCsvValue(recommendations.join('. ')),
        escapeCsvValue(detailedNotes.join('; ')),
      ])
    })

    // Create comprehensive footer
    const footer = [
      [''],
      ['=== PANDUAN INTERPRETASI DATA ==='],
      [''],
      ['1. STATUS KEHADIRAN SHALAT:'],
      ['   - HADIR_LENGKAP: Mengikuti Shalat Zuhur dan Asr berjamaah'],
      ['   - HADIR_SEBAGIAN: Mengikuti salah satu shalat (Zuhur atau Asr)'],
      ['   - TIDAK_HADIR: Tidak mengikuti shalat berjamaah'],
      ['   - BERHALANGAN_IJIN: Memiliki ijin khusus tidak mengikuti shalat'],
      [''],
      ['2. TINGKAT KEPATUHAN IBADAH:'],
      ['   - SANGAT_BAIK: Skor 100% - Hadir lengkap atau berhalangan dengan ijin'],
      ['   - BAIK: Skor 50% - Hadir salah satu shalat'],
      ['   - PERLU_PERHATIAN: Skor 0% - Tidak hadir tanpa keterangan'],
      ['   - IJIN_KHUSUS: Memiliki ijin resmi dari sekolah'],
      [''],
      ['3. SKOR IBADAH:'],
      ['   - Dihitung berdasarkan persentase kehadiran shalat wajib'],
      ['   - Siswa dengan ijin khusus mendapat skor 100%'],
      ['   - Rentang: 0% (tidak hadir) hingga 100% (hadir lengkap/ijin)'],
      [''],
      ['4. REKOMENDASI TINDAKAN:'],
      ['   - Siswa skor 0%: Konseling dan koordinasi dengan orang tua'],
      ['   - Siswa skor 50%: Motivasi untuk konsistensi ibadah'],
      ['   - Siswa skor 100%: Apresiasi dan jadikan contoh'],
      [''],
      ['5. INFORMASI TEKNIS:'],
      ['   - Waktu menggunakan zona WITA (UTC+8) - Waktu Indonesia Tengah'],
      ['   - Data diambil dari sistem absensi real-time dengan QR Code'],
      ['   - Laporan mencakup absensi shalat berjamaah di sekolah'],
      ['   - Sistem terintegrasi dengan database siswa aktif'],
      [''],
      ['=== TINDAK LANJUT YANG DISARANKAN ==='],
      [''],
      ['Untuk Siswa dengan Skor Rendah:'],
      ['1. Panggil siswa untuk konseling individual'],
      ['2. Koordinasi dengan wali kelas dan BK'],
      ['3. Komunikasi dengan orang tua/wali'],
      ['4. Buat program pembinaan khusus'],
      ['5. Pantau perkembangan mingguan'],
      [''],
      ['Untuk Siswa dengan Skor Tinggi:'],
      ['1. Berikan apresiasi dan penghargaan'],
      ['2. Jadikan mentor untuk siswa lain'],
      ['3. Libatkan dalam kegiatan keagamaan'],
      [''],
      [`Diekspor pada: ${exportDate}`],
      [`Sistem Absensi Shalat - SMK Negeri 3 Banjarmasin`],
      [`Website: https://smkn3banjarmasin.sch.id/`],
    ]

    // Combine all content
    return [
      ...metadata.map(row => row.map(cell => escapeCsvValue(cell)).join(',')),
      headers.map(header => escapeCsvValue(header)).join(','),
      ...rows.map(row => row.join(',')),
      ...footer.map(row => row.map(cell => escapeCsvValue(cell)).join(',')),
    ].join('\n')
  }

  // Filter reports based on search
  const filteredReports = reports.filter(report => {
    if (!searchQuery) return true
    const query = searchQuery.toLowerCase()
    return (
      report.name.toLowerCase().includes(query) ||
      report.uniqueCode.toLowerCase().includes(query) ||
      report.className.toLowerCase().includes(query)
    )
  })

  // Load data on component mount and when filters change
  useEffect(() => {
    if (admin && (admin.role === 'super_admin' || admin.role === 'admin')) {
      fetchReports()
    }
  }, [admin, date, classFilter])

  // Show loading state
  if (sessionLoading || !admin) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-96 w-full" />
      </div>
    )
  }

  // Access denied for non-authorized roles
  if (admin.role !== 'super_admin' && admin.role !== 'admin') {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <h2 className="mb-2 text-xl font-semibold text-red-600">Akses Ditolak</h2>
          <p className="text-gray-600">Anda tidak memiliki izin untuk mengakses laporan shalat.</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Laporan Absensi Shalat
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Kelola dan pantau kehadiran shalat siswa
          </p>
        </div>
        <Button
          onClick={handleExport}
          disabled={isExporting || reports.length === 0}
          className="bg-green-600 hover:bg-green-700"
        >
          {isExporting ? (
            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <Download className="mr-2 h-4 w-4" />
          )}
          Ekspor CSV
        </Button>
      </div>

      {/* Enhanced Statistics Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-blue-600">{stats.total}</div>
                <div className="text-sm font-medium text-gray-600">Total Siswa</div>
                <div className="mt-1 text-xs text-gray-500">Siswa aktif hari ini</div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                <span className="text-xl text-blue-600">👥</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-green-600">{stats.zuhrPercentage}%</div>
                <div className="text-sm font-medium text-gray-600">Kehadiran Zuhur</div>
                <div className="mt-1 text-xs text-gray-500">
                  {stats.zuhr} dari {stats.total} siswa
                </div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <span className="text-xl text-green-600">🕌</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-emerald-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-emerald-600">{stats.asrPercentage}%</div>
                <div className="text-sm font-medium text-gray-600">Kehadiran Asr</div>
                <div className="mt-1 text-xs text-gray-500">
                  {stats.asr} dari {stats.total} siswa
                </div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-emerald-100">
                <span className="text-xl text-emerald-600">🕌</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-amber-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-amber-600">
                  {Math.round(((stats.zuhr + stats.asr) / (stats.total * 2)) * 100)}%
                </div>
                <div className="text-sm font-medium text-gray-600">Skor Ibadah</div>
                <div className="mt-1 text-xs text-gray-500">Rata-rata kehadiran shalat</div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-amber-100">
                <span className="text-xl text-amber-600">⭐</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Additional Insights */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <CardContent className="p-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-700">{stats.ijin}</div>
              <div className="text-sm font-medium text-blue-600">Siswa Berhalangan</div>
              <div className="mt-1 text-xs text-blue-500">
                Memiliki ijin khusus ({stats.ijinPercentage}%)
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-green-200 bg-gradient-to-r from-green-50 to-emerald-50">
          <CardContent className="p-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-700">
                {reports.filter(r => !r.ijin && r.zuhr && r.asr).length}
              </div>
              <div className="text-sm font-medium text-green-600">Shalat Lengkap</div>
              <div className="mt-1 text-xs text-green-500">Hadir Zuhur & Asr</div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-red-200 bg-gradient-to-r from-red-50 to-rose-50">
          <CardContent className="p-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-700">
                {reports.filter(r => !r.ijin && !r.zuhr && !r.asr).length}
              </div>
              <div className="text-sm font-medium text-red-600">Perlu Perhatian</div>
              <div className="mt-1 text-xs text-red-500">Tidak hadir shalat</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col gap-4 md:flex-row">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                <Input
                  placeholder="Cari nama siswa atau kode..."
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={date} onValueChange={setDate}>
              <SelectTrigger className="w-full md:w-48">
                <Calendar className="mr-2 h-4 w-4" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="today">Hari Ini</SelectItem>
                <SelectItem value="yesterday">Kemarin</SelectItem>
                <SelectItem value="last7days">7 Hari Terakhir</SelectItem>
                <SelectItem value="last30days">30 Hari Terakhir</SelectItem>
                <SelectItem value="thismonth">Bulan Ini</SelectItem>
                <SelectItem value="lastmonth">Bulan Lalu</SelectItem>
              </SelectContent>
            </Select>
            <Select value={classFilter} onValueChange={setClassFilter}>
              <SelectTrigger className="w-full md:w-48">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Kelas</SelectItem>
                <SelectItem value="x-tkj-1">X TKJ 1</SelectItem>
                <SelectItem value="x-tkj-2">X TKJ 2</SelectItem>
                <SelectItem value="xi-tkj-1">XI TKJ 1</SelectItem>
                <SelectItem value="xi-tkj-2">XI TKJ 2</SelectItem>
                <SelectItem value="xii-tkj-1">XII TKJ 1</SelectItem>
                <SelectItem value="xii-tkj-2">XII TKJ 2</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Prayer Reports Table */}
      <Card>
        <CardHeader>
          <CardTitle>Data Absensi Shalat ({filteredReports.length} siswa)</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          ) : filteredReports.length === 0 ? (
            <div className="py-8 text-center text-gray-500">Tidak ada data laporan shalat</div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="w-12 text-center font-semibold">No</TableHead>
                    <TableHead className="min-w-40 font-semibold">Identitas Siswa</TableHead>
                    <TableHead className="min-w-32 text-center font-semibold">
                      Kehadiran Shalat
                    </TableHead>
                    <TableHead className="min-w-28 text-center font-semibold">
                      Skor Ibadah
                    </TableHead>
                    <TableHead className="min-w-24 text-center font-semibold">Status</TableHead>
                    <TableHead className="min-w-32 text-center font-semibold">Keterangan</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredReports.map((report, index) => {
                    // Calculate prayer completion score (0-100%)
                    const prayerCount = (report.zuhr ? 1 : 0) + (report.asr ? 1 : 0)
                    const prayerScore = report.ijin ? 100 : Math.round((prayerCount / 2) * 100)

                    // Determine status and color

                    const statusColor = report.ijin
                      ? 'bg-blue-100 text-blue-800 border-blue-200'
                      : prayerScore === 100
                        ? 'bg-green-100 text-green-800 border-green-200'
                        : prayerScore >= 50
                          ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
                          : 'bg-red-100 text-red-800 border-red-200'

                    const statusText = report.ijin
                      ? 'Ijin'
                      : prayerScore === 100
                        ? 'Sangat Baik'
                        : prayerScore >= 50
                          ? 'Baik'
                          : 'Perlu Perhatian'

                    // Generate attendance summary
                    const attendanceSummary = []
                    if (report.zuhr) attendanceSummary.push('Zuhur ✓')
                    if (report.asr) attendanceSummary.push('Asr ✓')
                    if (!report.zuhr) attendanceSummary.push('Zuhur ✗')
                    if (!report.asr) attendanceSummary.push('Asr ✗')

                    // Generate notes
                    const notes = []
                    if (report.ijin) {
                      notes.push('Memiliki ijin khusus')
                    } else {
                      if (!report.zuhr && !report.asr) {
                        notes.push('Tidak hadir shalat berjamaah')
                      } else if (!report.zuhr) {
                        notes.push('Tidak hadir Shalat Zuhur')
                      } else if (!report.asr) {
                        notes.push('Tidak hadir Shalat Asr')
                      } else {
                        notes.push('Hadir lengkap shalat berjamaah')
                      }
                    }

                    return (
                      <TableRow
                        key={report.uniqueCode}
                        className="transition-colors hover:bg-blue-50/50"
                      >
                        <TableCell className="text-center font-medium text-gray-600">
                          {index + 1}
                        </TableCell>

                        {/* Student Identity */}
                        <TableCell className="py-4">
                          <div className="space-y-1">
                            <div className="font-semibold text-gray-900">{report.name}</div>
                            <div className="text-sm text-gray-500">
                              {report.uniqueCode} • {report.className}
                            </div>
                          </div>
                        </TableCell>

                        {/* Prayer Attendance */}
                        <TableCell className="py-4 text-center">
                          <div className="space-y-2">
                            <div className="flex justify-center gap-1">
                              {report.zuhr ? (
                                <Badge className="border-green-200 bg-green-100 text-xs text-green-700">
                                  Zuhur ✓
                                </Badge>
                              ) : (
                                <Badge className="border-red-200 bg-red-100 text-xs text-red-700">
                                  Zuhur ✗
                                </Badge>
                              )}
                              {report.asr ? (
                                <Badge className="border-green-200 bg-green-100 text-xs text-green-700">
                                  Asr ✓
                                </Badge>
                              ) : (
                                <Badge className="border-red-200 bg-red-100 text-xs text-red-700">
                                  Asr ✗
                                </Badge>
                              )}
                            </div>
                            {(report.zuhrTime || report.asrTime) && (
                              <div className="text-xs text-gray-500">
                                {report.zuhrTime && `Z: ${report.zuhrTime}`}
                                {report.zuhrTime && report.asrTime && ' • '}
                                {report.asrTime && `A: ${report.asrTime}`}
                              </div>
                            )}
                          </div>
                        </TableCell>

                        {/* Prayer Score */}
                        <TableCell className="py-4 text-center">
                          <div className="space-y-1">
                            <div
                              className={`text-lg font-bold ${
                                prayerScore === 100
                                  ? 'text-green-600'
                                  : prayerScore >= 50
                                    ? 'text-yellow-600'
                                    : 'text-red-600'
                              }`}
                            >
                              {prayerScore}%
                            </div>
                            <div className="text-xs text-gray-500">{prayerCount}/2 shalat</div>
                          </div>
                        </TableCell>

                        {/* Status */}
                        <TableCell className="py-4 text-center">
                          <Badge className={`${statusColor} font-medium`}>{statusText}</Badge>
                        </TableCell>

                        {/* Notes */}
                        <TableCell className="py-4">
                          <div className="max-w-32 text-sm text-gray-600">{notes.join(', ')}</div>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
