'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useToast } from '@/hooks/use-toast'
import { useAdminSession } from '@/hooks/use-admin-session'
import {
  Download,
  RefreshCw,
  Users,
  UserCheck,
  Clock,
  AlertTriangle,
  Search,
  Calendar,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react'
import { format } from 'date-fns'
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  <PERSON><PERSON><PERSON>Link,
  Pa<PERSON>ation<PERSON><PERSON><PERSON>,
  PaginationPrevious,
} from '@/components/ui/pagination'

// Import chart components
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
} from 'recharts'

// School Report Interface (Real Data Structure)
interface SchoolReport {
  uniqueCode: string
  name: string
  className: string
  summaryDate: string
  entry: boolean
  entryTime: string | null
  lateEntry: boolean
  lateEntryTime: string | null
  excusedAbsence: boolean
  excusedAbsenceTime?: string | null
  temporaryLeave: boolean
  temporaryLeaveTime?: string | null
  returnFromLeave: boolean
  returnFromLeaveTime?: string | null
  sick: boolean
  sickTime?: string | null
}

// School Statistics Interface
interface SchoolStats {
  total: number
  entry: number
  lateEntry: number
  excusedAbsence: number
  temporaryLeave: number
  returnFromLeave: number
  sick: number
  entryPercentage: number
  lateEntryPercentage: number
  excusedAbsencePercentage: number
  temporaryLeavePercentage: number
  returnFromLeavePercentage: number
  sickPercentage: number
}

// Trend Data for Charts
interface SchoolTrendData {
  date: string
  morning: number
  afternoon: number
  late: number
}

// Class Performance for Charts
interface ClassPerformance {
  className: string
  attendanceRate: number
  punctualityRate: number
}

export default function SchoolReportsPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { admin, loading: sessionLoading } = useAdminSession()

  // State management for real data
  const [reports, setReports] = useState<SchoolReport[]>([])
  const [stats, setStats] = useState<SchoolStats>({
    total: 0,
    entry: 0,
    lateEntry: 0,
    excusedAbsence: 0,
    temporaryLeave: 0,
    returnFromLeave: 0,
    sick: 0,
    entryPercentage: 0,
    lateEntryPercentage: 0,
    excusedAbsencePercentage: 0,
    temporaryLeavePercentage: 0,
    returnFromLeavePercentage: 0,
    sickPercentage: 0,
  })
  const [trendData, setTrendData] = useState<SchoolTrendData[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [classFilter, setClassFilter] = useState('all')
  const [date, setDate] = useState('today')
  const [searchQuery, setSearchQuery] = useState('')
  const [isExporting, setIsExporting] = useState(false)

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(50) // 50 students per page for 3000 students

  // Role-based access control
  useEffect(() => {
    if (!sessionLoading && admin) {
      // Only Super Admin, Teacher, and Receptionist can access school reports
      if (!['super_admin', 'teacher', 'receptionist'].includes(admin.role || '')) {
        router.push('/admin/home')
        return
      }
    }
  }, [admin, sessionLoading, router])

  // Fetch school reports data
  const fetchReports = async () => {
    try {
      setIsLoading(true)

      const queryParams = new URLSearchParams()
      queryParams.append('date', date)
      queryParams.append('reportType', 'school') // Only school attendance data
      if (classFilter !== 'all') {
        queryParams.append('class', classFilter.replace(/-/g, ' ').toUpperCase())
      }
      queryParams.append('_t', Date.now().toString())

      const response = await fetch(`/api/absence/reports?${queryParams.toString()}`, {
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
          Expires: '0',
        },
      })

      if (!response.ok) {
        throw new Error('Failed to fetch school reports')
      }

      const data = await response.json()
      setReports(data)

      // Calculate school statistics
      const total = data.length
      const morningCount = data.filter((r: SchoolReport) => r.morning).length
      const afternoonCount = data.filter((r: SchoolReport) => r.afternoon).length
      const lateCount = data.filter((r: SchoolReport) => r.late).length
      const excusedCount = data.filter((r: SchoolReport) => r.excused).length
      const sickCount = data.filter((r: SchoolReport) => r.sick).length

      setStats({
        total,
        morning: morningCount,
        afternoon: afternoonCount,
        late: lateCount,
        excused: excusedCount,
        sick: sickCount,
        morningPercentage: total > 0 ? Math.round((morningCount / total) * 100) : 0,
        afternoonPercentage: total > 0 ? Math.round((afternoonCount / total) * 100) : 0,
        latePercentage: total > 0 ? Math.round((lateCount / total) * 100) : 0,
        excusedPercentage: total > 0 ? Math.round((excusedCount / total) * 100) : 0,
        sickPercentage: total > 0 ? Math.round((sickCount / total) * 100) : 0,
      })

      // Generate trend data (mock for now, can be enhanced with real historical data)
      const mockTrendData: SchoolTrendData[] = [
        { date: '2024-01-01', morning: morningCount, afternoon: afternoonCount, late: lateCount },
        {
          date: '2024-01-02',
          morning: Math.max(0, morningCount - 3),
          afternoon: Math.max(0, afternoonCount - 2),
          late: Math.max(0, lateCount + 1),
        },
        {
          date: '2024-01-03',
          morning: Math.max(0, morningCount + 1),
          afternoon: Math.max(0, afternoonCount + 2),
          late: Math.max(0, lateCount - 1),
        },
        { date: '2024-01-04', morning: morningCount, afternoon: afternoonCount, late: lateCount },
        {
          date: '2024-01-05',
          morning: Math.max(0, morningCount - 1),
          afternoon: Math.max(0, afternoonCount - 1),
          late: Math.max(0, lateCount + 2),
        },
      ]
      setTrendData(mockTrendData)

      // Generate class performance data
      const classGroups = data.reduce((acc: any, report: SchoolReport) => {
        if (!acc[report.className]) {
          acc[report.className] = { total: 0, onTime: 0, present: 0 }
        }
        acc[report.className].total++
        if (report.morning || report.afternoon) {
          acc[report.className].present++
        }
        if ((report.morning || report.afternoon) && !report.late) {
          acc[report.className].onTime++
        }
        return acc
      }, {})

      const classPerf = Object.entries(classGroups).map(([className, data]: [string, any]) => ({
        className,
        attendanceRate: Math.round((data.present / data.total) * 100),
        punctualityRate: Math.round((data.onTime / data.total) * 100),
      }))
      setClassPerformance(classPerf)
    } catch (error) {
      console.error('Error fetching school reports:', error)
      toast({
        title: 'Gagal memuat laporan sekolah',
        description: 'Terjadi kesalahan saat mengambil data laporan',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Export school reports to CSV
  const handleExport = async () => {
    try {
      setIsExporting(true)

      // Generate school-specific CSV content
      const csvContent = generateSchoolCSV(reports, stats)

      // Create download
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `laporan-sekolah-${format(new Date(), 'yyyy-MM-dd')}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast({
        title: 'Laporan sekolah berhasil diekspor',
        description: 'File CSV telah diunduh ke perangkat Anda',
      })
    } catch (error) {
      console.error('Error exporting school reports:', error)
      toast({
        title: 'Gagal mengekspor laporan',
        description: 'Terjadi kesalahan saat mengekspor data',
        variant: 'destructive',
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Generate school-specific CSV
  const generateSchoolCSV = (reports: SchoolReport[], stats: SchoolStats): string => {
    const reportDate =
      date === 'today' ? 'Hari Ini' : date === 'yesterday' ? 'Kemarin' : 'Minggu Ini'
    const className = classFilter === 'all' ? 'Semua Kelas' : classFilter

    const headers = [
      'No',
      'Kode_Siswa',
      'Nama_Lengkap',
      'Kelas',
      'Tanggal',
      'Absen_Pagi',
      'Waktu_Pagi',
      'Absen_Siang',
      'Waktu_Siang',
      'Status_Terlambat',
      'Waktu_Terlambat',
      'Status_Ijin',
      'Waktu_Ijin',
      'Status_Sakit',
      'Waktu_Sakit',
      'Skor_Kehadiran',
    ]

    const rows = reports.map((report, index) => {
      const attendanceCount = (report.morning ? 1 : 0) + (report.afternoon ? 1 : 0)
      const attendanceScore =
        report.excused || report.sick ? 100 : Math.round((attendanceCount / 2) * 100)

      return [
        index + 1,
        report.uniqueCode,
        report.name,
        report.className,
        report.summaryDate,
        report.morning ? 'HADIR' : 'TIDAK_HADIR',
        report.morningTime || '-',
        report.afternoon ? 'HADIR' : 'TIDAK_HADIR',
        report.afternoonTime || '-',
        report.late ? 'YA' : 'TIDAK',
        report.lateTime || '-',
        report.excused ? 'YA' : 'TIDAK',
        report.excusedTime || '-',
        report.sick ? 'YA' : 'TIDAK',
        report.sickTime || '-',
        `${attendanceScore}%`,
      ]
    })

    const csvContent = [
      `=== LAPORAN SEKOLAH - ${reportDate} ===`,
      `Filter Kelas: ${className}`,
      `Total Siswa: ${stats.total}`,
      `Pagi: ${stats.morning} (${stats.morningPercentage}%)`,
      `Siang: ${stats.afternoon} (${stats.afternoonPercentage}%)`,
      `Terlambat: ${stats.late} (${stats.latePercentage}%)`,
      `Ijin: ${stats.excused} (${stats.excusedPercentage}%)`,
      `Sakit: ${stats.sick} (${stats.sickPercentage}%)`,
      '',
      headers.join(','),
      ...rows.map(row => row.join(',')),
    ].join('\n')

    return csvContent
  }

  // Filter reports based on search
  const filteredReports = reports.filter(report => {
    if (!searchQuery) return true
    const query = searchQuery.toLowerCase()
    return (
      report.name.toLowerCase().includes(query) ||
      report.uniqueCode.toLowerCase().includes(query) ||
      report.className.toLowerCase().includes(query)
    )
  })

  // Load data on component mount and when filters change
  useEffect(() => {
    if (admin && ['super_admin', 'teacher', 'receptionist'].includes(admin.role || '')) {
      fetchReports()
    }
  }, [admin, date, classFilter])

  // Show loading state
  if (sessionLoading || !admin) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-8 w-64" />
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-5">
          {[...Array(5)].map((_, i) => (
            <Skeleton key={i} className="h-32" />
          ))}
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    )
  }

  // Access denied for non-authorized roles
  if (!['super_admin', 'teacher', 'receptionist'].includes(admin.role || '')) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <h2 className="mb-2 text-xl font-semibold text-red-600">Akses Ditolak</h2>
          <p className="text-gray-600">Anda tidak memiliki izin untuk mengakses laporan sekolah.</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Analitik Sekolah</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Dashboard analitik kehadiran dan kedisiplinan siswa
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={fetchReports} disabled={isLoading} variant="outline">
            {isLoading ? (
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="mr-2 h-4 w-4" />
            )}
            Refresh
          </Button>
          <Button
            onClick={handleExport}
            disabled={isExporting}
            className="bg-green-600 hover:bg-green-700"
          >
            {isExporting ? (
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Download className="mr-2 h-4 w-4" />
            )}
            Ekspor CSV
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col gap-4 md:flex-row">
        <Select value={classFilter} onValueChange={setClassFilter}>
          <SelectTrigger className="w-full md:w-48">
            <SelectValue placeholder="Pilih Kelas" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Semua Kelas</SelectItem>
            <SelectItem value="x-tkj-1">X TKJ 1</SelectItem>
            <SelectItem value="x-tkj-2">X TKJ 2</SelectItem>
            <SelectItem value="xi-tkj-1">XI TKJ 1</SelectItem>
            <SelectItem value="xi-tkj-2">XI TKJ 2</SelectItem>
            <SelectItem value="xii-tkj-1">XII TKJ 1</SelectItem>
            <SelectItem value="xii-tkj-2">XII TKJ 2</SelectItem>
          </SelectContent>
        </Select>
        <Select value={date} onValueChange={setDate}>
          <SelectTrigger className="w-full md:w-48">
            <SelectValue placeholder="Periode" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="today">Hari Ini</SelectItem>
            <SelectItem value="yesterday">Kemarin</SelectItem>
            <SelectItem value="week">Minggu Ini</SelectItem>
          </SelectContent>
        </Select>
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
          <Input
            placeholder="Cari siswa..."
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-5">
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-blue-600">{stats.total}</div>
                <div className="text-sm font-medium text-gray-600">Total Siswa</div>
                <div className="mt-1 text-xs text-gray-500">Siswa aktif</div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-green-600">{stats.morning}</div>
                <div className="text-sm font-medium text-gray-600">Absen Pagi</div>
                <div className="mt-1 text-xs text-gray-500">
                  {stats.morningPercentage}% kehadiran
                </div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <UserCheck className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-emerald-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-emerald-600">{stats.afternoon}</div>
                <div className="text-sm font-medium text-gray-600">Absen Siang</div>
                <div className="mt-1 text-xs text-gray-500">
                  {stats.afternoonPercentage}% kehadiran
                </div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-emerald-100">
                <Clock className="h-6 w-6 text-emerald-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-amber-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-amber-600">{stats.late}</div>
                <div className="text-sm font-medium text-gray-600">Terlambat</div>
                <div className="mt-1 text-xs text-gray-500">{stats.latePercentage}% siswa</div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-amber-100">
                <AlertTriangle className="h-6 w-6 text-amber-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-red-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-red-600">{stats.excused + stats.sick}</div>
                <div className="text-sm font-medium text-gray-600">Ijin & Sakit</div>
                <div className="mt-1 text-xs text-gray-500">
                  {stats.excusedPercentage + stats.sickPercentage}% siswa
                </div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
                <Calendar className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Student Data Matrix */}
      <Card>
        <CardHeader>
          <CardTitle>Data Siswa Sekolah</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>No</TableHead>
                  <TableHead>Kode Siswa</TableHead>
                  <TableHead>Nama</TableHead>
                  <TableHead>Kelas</TableHead>
                  <TableHead>Pagi</TableHead>
                  <TableHead>Siang</TableHead>
                  <TableHead>Terlambat</TableHead>
                  <TableHead>Ijin</TableHead>
                  <TableHead>Sakit</TableHead>
                  <TableHead>Skor</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  [...Array(5)].map((_, i) => (
                    <TableRow key={i}>
                      <TableCell>
                        <Skeleton className="h-4 w-8" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-20" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-32" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-16" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-16" />
                      </TableCell>
                    </TableRow>
                  ))
                ) : filteredReports.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={10} className="py-8 text-center text-gray-500">
                      Tidak ada data siswa ditemukan
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredReports.map((report, index) => {
                    const attendanceCount = (report.morning ? 1 : 0) + (report.afternoon ? 1 : 0)
                    const attendanceScore =
                      report.excused || report.sick ? 100 : Math.round((attendanceCount / 2) * 100)

                    return (
                      <TableRow key={report.uniqueCode}>
                        <TableCell>{index + 1}</TableCell>
                        <TableCell className="font-mono text-sm">{report.uniqueCode}</TableCell>
                        <TableCell className="font-medium">{report.name}</TableCell>
                        <TableCell>{report.className}</TableCell>
                        <TableCell>
                          <Badge variant={report.morning ? 'default' : 'secondary'}>
                            {report.morning ? '✓' : '✗'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={report.afternoon ? 'default' : 'secondary'}>
                            {report.afternoon ? '✓' : '✗'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={report.late ? 'destructive' : 'secondary'}>
                            {report.late ? 'Terlambat' : '-'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={report.excused ? 'outline' : 'secondary'}>
                            {report.excused ? 'Ijin' : '-'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={report.sick ? 'outline' : 'secondary'}>
                            {report.sick ? 'Sakit' : '-'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              attendanceScore >= 80
                                ? 'default'
                                : attendanceScore >= 50
                                  ? 'secondary'
                                  : 'destructive'
                            }
                          >
                            {attendanceScore}%
                          </Badge>
                        </TableCell>
                      </TableRow>
                    )
                  })
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Charts Section */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Attendance Trend Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Tren Kehadiran Sekolah</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={trendData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Line
                    type="monotone"
                    dataKey="morning"
                    stroke="#10b981"
                    strokeWidth={2}
                    name="Pagi"
                  />
                  <Line
                    type="monotone"
                    dataKey="afternoon"
                    stroke="#3b82f6"
                    strokeWidth={2}
                    name="Siang"
                  />
                  <Line
                    type="monotone"
                    dataKey="late"
                    stroke="#ef4444"
                    strokeWidth={2}
                    name="Terlambat"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Class Performance Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Performa Kelas</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={classPerformance}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="className" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="attendanceRate" fill="#10b981" name="Tingkat Kehadiran %" />
                  <Bar dataKey="punctualityRate" fill="#3b82f6" name="Tingkat Kedisiplinan %" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
