'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Skeleton } from '@/components/ui/skeleton'
import { useToast } from '@/hooks/use-toast'
import { useAdminSession } from '@/hooks/use-admin-session'
import {
  Download,
  RefreshCw,
  Users,
  UserCheck,
  Clock,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Minus,
} from 'lucide-react'
import { format } from 'date-fns'

// Import chart components
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
} from 'recharts'

// School Analytics Interfaces
interface SchoolKPIs {
  totalStudents: number
  presentToday: number
  onTimeAttendance: number
  lateAttendance: number
  absentToday: number
  weeklyTrend: number
  alerts: number
}

interface SchoolTrendData {
  date: string
  attendance: number
  onTime: number
  late: number
  absent: number
}

interface ClassPerformance {
  className: string
  totalStudents: number
  attendanceRate: number
  punctualityRate: number
  riskLevel: 'low' | 'medium' | 'high'
  trend: 'up' | 'down' | 'stable'
}

interface TopStudent {
  name: string
  className: string
  attendanceRate: number
  punctualityRate: number
  streak: number
}

interface SchoolAnalytics {
  kpis: SchoolKPIs
  trendData: SchoolTrendData[]
  classPerformance: ClassPerformance[]
  topStudents: TopStudent[]
  insights: string[]
}

export default function SchoolReportsPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { admin, loading: sessionLoading } = useAdminSession()

  // State management for analytics
  const [analytics, setAnalytics] = useState<SchoolAnalytics>({
    kpis: {
      totalStudents: 0,
      presentToday: 0,
      onTimeAttendance: 0,
      lateAttendance: 0,
      absentToday: 0,
      weeklyTrend: 0,
      alerts: 0,
    },
    trendData: [],
    classPerformance: [],
    topStudents: [],
    insights: [],
  })
  const [isLoading, setIsLoading] = useState(true)
  const [classFilter, setClassFilter] = useState('all')
  const [dateRange, setDateRange] = useState('30')
  const [isExporting, setIsExporting] = useState(false)

  // Role-based access control
  useEffect(() => {
    if (!sessionLoading && admin) {
      // Only Super Admin, Teacher, and Receptionist can access school reports
      if (!['super_admin', 'teacher', 'receptionist'].includes(admin.role || '')) {
        router.push('/admin/home')
        return
      }
    }
  }, [admin, sessionLoading, router])

  // Fetch school analytics data
  const fetchAnalytics = async () => {
    try {
      setIsLoading(true)

      const queryParams = new URLSearchParams()
      queryParams.append('reportType', 'school')
      queryParams.append('dateRange', dateRange)
      if (classFilter !== 'all') {
        queryParams.append('class', classFilter.replace(/-/g, ' ').toUpperCase())
      }
      queryParams.append('_t', Date.now().toString())

      const response = await fetch(`/api/analytics/dashboard?${queryParams.toString()}`, {
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
          Expires: '0',
        },
      })

      if (!response.ok) {
        throw new Error('Failed to fetch school analytics')
      }

      const data = await response.json()
      setAnalytics(data)
    } catch (error) {
      console.error('Error fetching school analytics:', error)
      toast({
        title: 'Gagal memuat analitik sekolah',
        description: 'Terjadi kesalahan saat mengambil data analitik',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Export school analytics to CSV
  const handleExport = async () => {
    try {
      setIsExporting(true)

      const response = await fetch('/api/analytics/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reportType: 'school',
          dateRange,
          classFilter: classFilter !== 'all' ? classFilter : undefined,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to export school analytics')
      }

      const blob = await response.blob()
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `school-analytics-${format(new Date(), 'yyyy-MM-dd')}.csv`
      link.click()
      URL.revokeObjectURL(url)

      toast({
        title: 'Analitik sekolah berhasil diekspor',
        description: 'File CSV telah diunduh ke perangkat Anda',
      })
    } catch (error) {
      console.error('Error exporting school analytics:', error)
      toast({
        title: 'Gagal mengekspor analitik',
        description: 'Terjadi kesalahan saat mengekspor data',
        variant: 'destructive',
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Load data on component mount and when filters change
  useEffect(() => {
    if (admin && ['super_admin', 'teacher', 'receptionist'].includes(admin.role || '')) {
      fetchAnalytics()
    }
  }, [admin, dateRange, classFilter])

  // Show loading state
  if (sessionLoading || !admin) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-8 w-64" />
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className="h-32" />
          ))}
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    )
  }

  // Access denied for non-authorized roles
  if (!['super_admin', 'teacher', 'receptionist'].includes(admin.role || '')) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <h2 className="mb-2 text-xl font-semibold text-red-600">Akses Ditolak</h2>
          <p className="text-gray-600">
            Anda tidak memiliki izin untuk mengakses analitik sekolah.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Analitik Sekolah</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Dashboard analitik kehadiran dan kedisiplinan siswa
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={fetchAnalytics} disabled={isLoading} variant="outline">
            {isLoading ? (
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="mr-2 h-4 w-4" />
            )}
            Refresh
          </Button>
          <Button
            onClick={handleExport}
            disabled={isExporting}
            className="bg-green-600 hover:bg-green-700"
          >
            {isExporting ? (
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Download className="mr-2 h-4 w-4" />
            )}
            Ekspor CSV
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col gap-4 md:flex-row">
        <Select value={classFilter} onValueChange={setClassFilter}>
          <SelectTrigger className="w-full md:w-48">
            <SelectValue placeholder="Pilih Kelas" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Semua Kelas</SelectItem>
            <SelectItem value="x-tkj-1">X TKJ 1</SelectItem>
            <SelectItem value="x-tkj-2">X TKJ 2</SelectItem>
            <SelectItem value="xi-tkj-1">XI TKJ 1</SelectItem>
            <SelectItem value="xi-tkj-2">XI TKJ 2</SelectItem>
            <SelectItem value="xii-tkj-1">XII TKJ 1</SelectItem>
            <SelectItem value="xii-tkj-2">XII TKJ 2</SelectItem>
          </SelectContent>
        </Select>
        <Select value={dateRange} onValueChange={setDateRange}>
          <SelectTrigger className="w-full md:w-48">
            <SelectValue placeholder="Periode" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7">7 Hari Terakhir</SelectItem>
            <SelectItem value="30">30 Hari Terakhir</SelectItem>
            <SelectItem value="90">3 Bulan Terakhir</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-5">
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-blue-600">
                  {analytics.kpis.totalStudents}
                </div>
                <div className="text-sm font-medium text-gray-600">Total Siswa</div>
                <div className="mt-1 text-xs text-gray-500">Siswa aktif</div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-green-600">
                  {analytics.kpis.presentToday}
                </div>
                <div className="text-sm font-medium text-gray-600">Hadir Hari Ini</div>
                <div className="mt-1 text-xs text-gray-500">Siswa hadir</div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <UserCheck className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-emerald-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-emerald-600">
                  {analytics.kpis.onTimeAttendance}
                </div>
                <div className="text-sm font-medium text-gray-600">Tepat Waktu</div>
                <div className="mt-1 text-xs text-gray-500">Siswa disiplin</div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-emerald-100">
                <Clock className="h-6 w-6 text-emerald-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-amber-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-amber-600">
                  {analytics.kpis.lateAttendance}
                </div>
                <div className="text-sm font-medium text-gray-600">Terlambat</div>
                <div className="mt-1 text-xs text-gray-500">Perlu perhatian</div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-amber-100">
                <AlertTriangle className="h-6 w-6 text-amber-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-red-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center gap-2">
                  <div className="text-3xl font-bold text-red-600">
                    {analytics.kpis.weeklyTrend}%
                  </div>
                  {analytics.kpis.weeklyTrend > 0 ? (
                    <TrendingUp className="h-5 w-5 text-green-600" />
                  ) : analytics.kpis.weeklyTrend < 0 ? (
                    <TrendingDown className="h-5 w-5 text-red-600" />
                  ) : (
                    <Minus className="h-5 w-5 text-gray-600" />
                  )}
                </div>
                <div className="text-sm font-medium text-gray-600">Tren Mingguan</div>
                <div className="mt-1 text-xs text-gray-500">Perubahan dari minggu lalu</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
