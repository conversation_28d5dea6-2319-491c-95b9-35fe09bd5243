'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { AdminLayout } from '@/components/layouts/admin-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useToast } from '@/hooks/use-toast'
import { useAdminSession } from '@/hooks/use-admin-session'
import { Calendar, Download, Search, Filter, RefreshCw } from 'lucide-react'
import { format } from 'date-fns'

// School Report Interface
interface SchoolReport {
  uniqueCode: string
  name: string
  className: string
  summaryDate: string
  entry: boolean
  entryTime: string | null
  lateEntry: boolean
  lateEntryTime: string | null
  excusedAbsence: boolean
  excusedAbsenceTime: string | null
  temporaryLeave: boolean
  temporaryLeaveTime: string | null
  returnFromLeave: boolean
  returnFromLeaveTime: string | null
  sick: boolean
  sickTime: string | null
  dismissal: boolean
  dismissalTime: string | null
}

// School Statistics Interface
interface SchoolStats {
  total: number
  entry: number
  lateEntry: number
  excusedAbsence: number
  temporaryLeave: number
  returnFromLeave: number
  sick: number
  dismissal: number
  entryPercentage?: number
  lateEntryPercentage?: number
  excusedAbsencePercentage?: number
  temporaryLeavePercentage?: number
  returnFromLeavePercentage?: number
  sickPercentage?: number
  dismissalPercentage?: number
}

export default function SchoolReportsPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { admin, loading: sessionLoading } = useAdminSession()

  // State management
  const [reports, setReports] = useState<SchoolReport[]>([])
  const [stats, setStats] = useState<SchoolStats>({
    total: 0,
    entry: 0,
    lateEntry: 0,
    excusedAbsence: 0,
    temporaryLeave: 0,
    returnFromLeave: 0,
    sick: 0,
    dismissal: 0,
  })
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [classFilter, setClassFilter] = useState('all')
  const [date, setDate] = useState('today')
  const [isExporting, setIsExporting] = useState(false)

  // Role-based access control
  useEffect(() => {
    if (!sessionLoading && admin) {
      // Only Super Admin, Teacher, and Receptionist can access school reports
      if (!['super_admin', 'teacher', 'receptionist'].includes(admin.role || '')) {
        router.push('/admin/home')
        return
      }
    }
  }, [admin, sessionLoading, router])

  // Fetch school reports data
  const fetchReports = async () => {
    try {
      setIsLoading(true)

      const queryParams = new URLSearchParams()
      queryParams.append('date', date)
      queryParams.append('reportType', 'school') // Only school data
      if (classFilter !== 'all') {
        queryParams.append('class', classFilter.replace(/-/g, ' ').toUpperCase())
      }
      queryParams.append('_t', Date.now().toString())

      const response = await fetch(`/api/absence/reports?${queryParams.toString()}`, {
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
          Expires: '0',
        },
      })

      if (!response.ok) {
        throw new Error('Failed to fetch school reports')
      }

      const data = await response.json()
      setReports(data)

      // Calculate school statistics
      const total = data.length
      const entryCount = data.filter((r: SchoolReport) => r.entry).length
      const lateEntryCount = data.filter((r: SchoolReport) => r.lateEntry).length
      const excusedAbsenceCount = data.filter((r: SchoolReport) => r.excusedAbsence).length
      const temporaryLeaveCount = data.filter((r: SchoolReport) => r.temporaryLeave).length
      const returnFromLeaveCount = data.filter((r: SchoolReport) => r.returnFromLeave).length
      const sickCount = data.filter((r: SchoolReport) => r.sick).length
      const dismissalCount = data.filter((r: SchoolReport) => r.dismissal).length

      setStats({
        total,
        entry: entryCount,
        lateEntry: lateEntryCount,
        excusedAbsence: excusedAbsenceCount,
        temporaryLeave: temporaryLeaveCount,
        returnFromLeave: returnFromLeaveCount,
        sick: sickCount,
        dismissal: dismissalCount,
        entryPercentage: total > 0 ? Math.round((entryCount / total) * 100) : 0,
        lateEntryPercentage: total > 0 ? Math.round((lateEntryCount / total) * 100) : 0,
        excusedAbsencePercentage: total > 0 ? Math.round((excusedAbsenceCount / total) * 100) : 0,
        temporaryLeavePercentage: total > 0 ? Math.round((temporaryLeaveCount / total) * 100) : 0,
        returnFromLeavePercentage: total > 0 ? Math.round((returnFromLeaveCount / total) * 100) : 0,
        sickPercentage: total > 0 ? Math.round((sickCount / total) * 100) : 0,
        dismissalPercentage: total > 0 ? Math.round((dismissalCount / total) * 100) : 0,
      })
    } catch (error) {
      console.error('Error fetching school reports:', error)
      toast({
        title: 'Gagal memuat laporan sekolah',
        description: 'Terjadi kesalahan saat mengambil data laporan',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Export school reports to CSV
  const handleExport = async () => {
    try {
      setIsExporting(true)

      // Generate school-specific CSV content
      const csvContent = generateSchoolCSV(reports, stats)

      // Create download
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `laporan-sekolah-${format(new Date(), 'yyyy-MM-dd')}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast({
        title: 'Laporan sekolah berhasil diekspor',
        description: 'File CSV telah diunduh ke perangkat Anda',
      })
    } catch (error) {
      console.error('Error exporting school reports:', error)
      toast({
        title: 'Gagal mengekspor laporan',
        description: 'Terjadi kesalahan saat mengekspor data',
        variant: 'destructive',
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Generate school-specific CSV
  const generateSchoolCSV = (reports: SchoolReport[], stats: SchoolStats): string => {
    const headers = [
      'No',
      'Kode_Siswa',
      'Nama_Lengkap',
      'Kelas',
      'Tanggal',
      'Masuk_Sekolah',
      'Waktu_Masuk',
      'Terlambat',
      'Waktu_Terlambat',
      'Sakit',
      'Waktu_Sakit',
      'Ijin_Sementara',
      'Waktu_Ijin_Sementara',
      'Kembali_Ijin',
      'Waktu_Kembali_Ijin',
      'Pulang',
      'Waktu_Pulang',
      'Status_Kehadiran',
    ]

    const rows = reports.map((report, index) => {
      let attendanceStatus = 'TIDAK_HADIR'
      if (report.sick) attendanceStatus = 'SAKIT'
      else if (report.temporaryLeave) attendanceStatus = 'IJIN_SEMENTARA'
      else if (report.excusedAbsence) attendanceStatus = 'IJIN'
      else if (report.lateEntry) attendanceStatus = 'TERLAMBAT'
      else if (report.entry) attendanceStatus = 'HADIR'

      return [
        index + 1,
        report.uniqueCode,
        report.name,
        report.className,
        report.summaryDate,
        report.entry ? '✓' : '✗',
        report.entryTime || '-',
        report.lateEntry ? '✓' : '✗',
        report.lateEntryTime || '-',
        report.sick ? '✓' : '✗',
        report.sickTime || '-',
        report.temporaryLeave ? '✓' : '✗',
        report.temporaryLeaveTime || '-',
        report.returnFromLeave ? '✓' : '✗',
        report.returnFromLeaveTime || '-',
        report.dismissal ? '✓' : '✗',
        report.dismissalTime || '-',
        attendanceStatus,
      ]
    })

    // Create CSV content with metadata
    const metadata = [
      ['=== LAPORAN ABSENSI SEKOLAH ==='],
      ['SMK Negeri 3 Banjarmasin'],
      [''],
      ['STATISTIK KEHADIRAN SEKOLAH'],
      [`Total Siswa: ${stats.total}`],
      [`Masuk Sekolah: ${stats.entry} (${stats.entryPercentage}%)`],
      [`Terlambat: ${stats.lateEntry} (${stats.lateEntryPercentage}%)`],
      [`Sakit: ${stats.sick} (${stats.sickPercentage}%)`],
      [`Ijin Sementara: ${stats.temporaryLeave} (${stats.temporaryLeavePercentage}%)`],
      [`Kembali Ijin: ${stats.returnFromLeave} (${stats.returnFromLeavePercentage}%)`],
      [`Pulang: ${stats.dismissal} (${stats.dismissalPercentage}%)`],
      [''],
      ['=== DETAIL DATA ==='],
      [''],
    ]

    return [
      ...metadata.map(row => row.join(',')),
      headers.join(','),
      ...rows.map(row => row.join(',')),
    ].join('\n')
  }

  // Filter reports based on search
  const filteredReports = reports.filter(report => {
    if (!searchQuery) return true
    const query = searchQuery.toLowerCase()
    return (
      report.name.toLowerCase().includes(query) ||
      report.uniqueCode.toLowerCase().includes(query) ||
      report.className.toLowerCase().includes(query)
    )
  })

  // Load data on component mount and when filters change
  useEffect(() => {
    if (admin && ['super_admin', 'teacher', 'receptionist'].includes(admin.role || '')) {
      fetchReports()
    }
  }, [admin, date, classFilter])

  // Show loading state
  if (sessionLoading || !admin) {
    return (
      <AdminLayout>
        <div className="space-y-6">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-96 w-full" />
        </div>
      </AdminLayout>
    )
  }

  // Access denied for non-authorized roles
  if (!['super_admin', 'teacher', 'receptionist'].includes(admin.role || '')) {
    return (
      <AdminLayout>
        <Card>
          <CardContent className="p-6 text-center">
            <h2 className="mb-2 text-xl font-semibold text-red-600">Akses Ditolak</h2>
            <p className="text-gray-600">
              Anda tidak memiliki izin untuk mengakses laporan sekolah.
            </p>
          </CardContent>
        </Card>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              Laporan Absensi Sekolah
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Kelola dan pantau kehadiran sekolah siswa
            </p>
          </div>
          <Button
            onClick={handleExport}
            disabled={isExporting || reports.length === 0}
            className="bg-green-600 hover:bg-green-700"
          >
            {isExporting ? (
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Download className="mr-2 h-4 w-4" />
            )}
            Ekspor CSV
          </Button>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
              <div className="text-sm text-gray-600">Total Siswa</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-600">{stats.entry}</div>
              <div className="text-sm text-gray-600">Masuk ({stats.entryPercentage}%)</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-yellow-600">{stats.lateEntry}</div>
              <div className="text-sm text-gray-600">Terlambat ({stats.lateEntryPercentage}%)</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-red-600">{stats.sick}</div>
              <div className="text-sm text-gray-600">Sakit ({stats.sickPercentage}%)</div>
            </CardContent>
          </Card>
        </div>

        {/* Additional Statistics */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-orange-600">{stats.temporaryLeave}</div>
              <div className="text-sm text-gray-600">
                Ijin Sementara ({stats.temporaryLeavePercentage}%)
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-purple-600">{stats.returnFromLeave}</div>
              <div className="text-sm text-gray-600">
                Kembali Ijin ({stats.returnFromLeavePercentage}%)
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-indigo-600">{stats.dismissal}</div>
              <div className="text-sm text-gray-600">Pulang ({stats.dismissalPercentage}%)</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col gap-4 md:flex-row">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                  <Input
                    placeholder="Cari nama siswa atau kode..."
                    value={searchQuery}
                    onChange={e => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={date} onValueChange={setDate}>
                <SelectTrigger className="w-full md:w-48">
                  <Calendar className="mr-2 h-4 w-4" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">Hari Ini</SelectItem>
                  <SelectItem value="yesterday">Kemarin</SelectItem>
                  <SelectItem value="week">Minggu Ini</SelectItem>
                </SelectContent>
              </Select>
              <Select value={classFilter} onValueChange={setClassFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Kelas</SelectItem>
                  <SelectItem value="x-tkj-1">X TKJ 1</SelectItem>
                  <SelectItem value="x-tkj-2">X TKJ 2</SelectItem>
                  <SelectItem value="xi-tkj-1">XI TKJ 1</SelectItem>
                  <SelectItem value="xi-tkj-2">XI TKJ 2</SelectItem>
                  <SelectItem value="xii-tkj-1">XII TKJ 1</SelectItem>
                  <SelectItem value="xii-tkj-2">XII TKJ 2</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* School Reports Table */}
        <Card>
          <CardHeader>
            <CardTitle>Data Absensi Sekolah ({filteredReports.length} siswa)</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <Skeleton key={i} className="h-12 w-full" />
                ))}
              </div>
            ) : filteredReports.length === 0 ? (
              <div className="py-8 text-center text-gray-500">Tidak ada data laporan sekolah</div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-16">No</TableHead>
                      <TableHead className="min-w-32">Kode</TableHead>
                      <TableHead className="min-w-48">Nama</TableHead>
                      <TableHead className="min-w-24">Kelas</TableHead>
                      <TableHead className="min-w-20 text-center">Masuk</TableHead>
                      <TableHead className="min-w-20 text-center">Terlambat</TableHead>
                      <TableHead className="min-w-20 text-center">Sakit</TableHead>
                      <TableHead className="min-w-20 text-center">Ijin Sementara</TableHead>
                      <TableHead className="min-w-20 text-center">Kembali Ijin</TableHead>
                      <TableHead className="min-w-20 text-center">Pulang</TableHead>
                      <TableHead className="min-w-24 text-center">Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredReports.map((report, index) => {
                      let attendanceStatus = 'TIDAK_HADIR'
                      let statusColor = 'bg-red-100 text-red-800'

                      if (report.sick) {
                        attendanceStatus = 'SAKIT'
                        statusColor = 'bg-red-100 text-red-800'
                      } else if (report.temporaryLeave) {
                        attendanceStatus = 'IJIN_SEMENTARA'
                        statusColor = 'bg-orange-100 text-orange-800'
                      } else if (report.excusedAbsence) {
                        attendanceStatus = 'IJIN'
                        statusColor = 'bg-yellow-100 text-yellow-800'
                      } else if (report.lateEntry) {
                        attendanceStatus = 'TERLAMBAT'
                        statusColor = 'bg-yellow-100 text-yellow-800'
                      } else if (report.entry) {
                        attendanceStatus = 'HADIR'
                        statusColor = 'bg-green-100 text-green-800'
                      }

                      return (
                        <TableRow key={report.uniqueCode} className="hover:bg-gray-50">
                          <TableCell className="font-medium">{index + 1}</TableCell>
                          <TableCell className="font-mono text-sm">{report.uniqueCode}</TableCell>
                          <TableCell className="font-medium">{report.name}</TableCell>
                          <TableCell>{report.className}</TableCell>
                          <TableCell className="text-center">
                            {report.entry ? (
                              <div className="flex flex-col items-center">
                                <Badge
                                  variant="secondary"
                                  className="mb-1 bg-green-100 text-green-800"
                                >
                                  ✓
                                </Badge>
                                <span className="text-xs text-gray-500">{report.entryTime}</span>
                              </div>
                            ) : (
                              <Badge variant="secondary" className="bg-red-100 text-red-800">
                                ✗
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell className="text-center">
                            {report.lateEntry ? (
                              <div className="flex flex-col items-center">
                                <Badge
                                  variant="secondary"
                                  className="mb-1 bg-yellow-100 text-yellow-800"
                                >
                                  ✓
                                </Badge>
                                <span className="text-xs text-gray-500">
                                  {report.lateEntryTime}
                                </span>
                              </div>
                            ) : (
                              <Badge variant="secondary" className="bg-red-100 text-red-800">
                                ✗
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell className="text-center">
                            {report.sick ? (
                              <div className="flex flex-col items-center">
                                <Badge variant="secondary" className="mb-1 bg-red-100 text-red-800">
                                  ✓
                                </Badge>
                                <span className="text-xs text-gray-500">{report.sickTime}</span>
                              </div>
                            ) : (
                              <Badge variant="secondary" className="bg-gray-100 text-gray-800">
                                ✗
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell className="text-center">
                            {report.temporaryLeave ? (
                              <div className="flex flex-col items-center">
                                <Badge
                                  variant="secondary"
                                  className="mb-1 bg-orange-100 text-orange-800"
                                >
                                  ✓
                                </Badge>
                                <span className="text-xs text-gray-500">
                                  {report.temporaryLeaveTime}
                                </span>
                              </div>
                            ) : (
                              <Badge variant="secondary" className="bg-gray-100 text-gray-800">
                                ✗
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell className="text-center">
                            {report.returnFromLeave ? (
                              <div className="flex flex-col items-center">
                                <Badge
                                  variant="secondary"
                                  className="mb-1 bg-purple-100 text-purple-800"
                                >
                                  ✓
                                </Badge>
                                <span className="text-xs text-gray-500">
                                  {report.returnFromLeaveTime}
                                </span>
                              </div>
                            ) : (
                              <Badge variant="secondary" className="bg-gray-100 text-gray-800">
                                ✗
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell className="text-center">
                            {report.dismissal ? (
                              <div className="flex flex-col items-center">
                                <Badge
                                  variant="secondary"
                                  className="mb-1 bg-purple-100 text-purple-800"
                                >
                                  ✓
                                </Badge>
                                <span className="text-xs text-gray-500">
                                  {report.dismissalTime}
                                </span>
                              </div>
                            ) : (
                              <Badge variant="secondary" className="bg-red-100 text-red-800">
                                ✗
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell className="text-center">
                            <Badge variant="secondary" className={statusColor}>
                              {attendanceStatus}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
