'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useToast } from '@/hooks/use-toast'
import { useAdminSession } from '@/hooks/use-admin-session'
import { Calendar, Download, Search, Filter, RefreshCw } from 'lucide-react'
import { format } from 'date-fns'

// School Report Interface
interface SchoolReport {
  uniqueCode: string
  name: string
  className: string
  summaryDate: string
  entry: boolean
  entryTime: string | null
  lateEntry: boolean
  lateEntryTime: string | null
  excusedAbsence: boolean
  excusedAbsenceTime: string | null
  temporaryLeave: boolean
  temporaryLeaveTime: string | null
  returnFromLeave: boolean
  returnFromLeaveTime: string | null
  sick: boolean
  sickTime: string | null
  dismissal: boolean
  dismissalTime: string | null
}

// School Statistics Interface
interface SchoolStats {
  total: number
  entry: number
  lateEntry: number
  excusedAbsence: number
  temporaryLeave: number
  returnFromLeave: number
  sick: number
  dismissal: number
  entryPercentage?: number
  lateEntryPercentage?: number
  excusedAbsencePercentage?: number
  temporaryLeavePercentage?: number
  returnFromLeavePercentage?: number
  sickPercentage?: number
  dismissalPercentage?: number
}

export default function SchoolReportsPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { admin, loading: sessionLoading } = useAdminSession()

  // State management
  const [reports, setReports] = useState<SchoolReport[]>([])
  const [stats, setStats] = useState<SchoolStats>({
    total: 0,
    entry: 0,
    lateEntry: 0,
    excusedAbsence: 0,
    temporaryLeave: 0,
    returnFromLeave: 0,
    sick: 0,
    dismissal: 0,
  })
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [classFilter, setClassFilter] = useState('all')
  const [date, setDate] = useState('today')
  const [isExporting, setIsExporting] = useState(false)

  // Role-based access control
  useEffect(() => {
    if (!sessionLoading && admin) {
      // Only Super Admin, Teacher, and Receptionist can access school reports
      if (!['super_admin', 'teacher', 'receptionist'].includes(admin.role || '')) {
        router.push('/admin/home')
        return
      }
    }
  }, [admin, sessionLoading, router])

  // Fetch school reports data
  const fetchReports = async () => {
    try {
      setIsLoading(true)

      const queryParams = new URLSearchParams()
      queryParams.append('date', date)
      queryParams.append('reportType', 'school') // Only school data
      if (classFilter !== 'all') {
        queryParams.append('class', classFilter.replace(/-/g, ' ').toUpperCase())
      }
      queryParams.append('_t', Date.now().toString())

      const response = await fetch(`/api/absence/reports?${queryParams.toString()}`, {
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
          Expires: '0',
        },
      })

      if (!response.ok) {
        throw new Error('Failed to fetch school reports')
      }

      const data = await response.json()
      setReports(data)

      // Calculate school statistics
      const total = data.length
      const entryCount = data.filter((r: SchoolReport) => r.entry).length
      const lateEntryCount = data.filter((r: SchoolReport) => r.lateEntry).length
      const excusedAbsenceCount = data.filter((r: SchoolReport) => r.excusedAbsence).length
      const temporaryLeaveCount = data.filter((r: SchoolReport) => r.temporaryLeave).length
      const returnFromLeaveCount = data.filter((r: SchoolReport) => r.returnFromLeave).length
      const sickCount = data.filter((r: SchoolReport) => r.sick).length
      const dismissalCount = data.filter((r: SchoolReport) => r.dismissal).length

      setStats({
        total,
        entry: entryCount,
        lateEntry: lateEntryCount,
        excusedAbsence: excusedAbsenceCount,
        temporaryLeave: temporaryLeaveCount,
        returnFromLeave: returnFromLeaveCount,
        sick: sickCount,
        dismissal: dismissalCount,
        entryPercentage: total > 0 ? Math.round((entryCount / total) * 100) : 0,
        lateEntryPercentage: total > 0 ? Math.round((lateEntryCount / total) * 100) : 0,
        excusedAbsencePercentage: total > 0 ? Math.round((excusedAbsenceCount / total) * 100) : 0,
        temporaryLeavePercentage: total > 0 ? Math.round((temporaryLeaveCount / total) * 100) : 0,
        returnFromLeavePercentage: total > 0 ? Math.round((returnFromLeaveCount / total) * 100) : 0,
        sickPercentage: total > 0 ? Math.round((sickCount / total) * 100) : 0,
        dismissalPercentage: total > 0 ? Math.round((dismissalCount / total) * 100) : 0,
      })
    } catch (error) {
      console.error('Error fetching school reports:', error)
      toast({
        title: 'Gagal memuat laporan sekolah',
        description: 'Terjadi kesalahan saat mengambil data laporan',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Export school reports to CSV
  const handleExport = async () => {
    try {
      setIsExporting(true)

      // Generate school-specific CSV content
      const csvContent = generateSchoolCSV(reports, stats)

      // Create download
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `laporan-sekolah-${format(new Date(), 'yyyy-MM-dd')}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast({
        title: 'Laporan sekolah berhasil diekspor',
        description: 'File CSV telah diunduh ke perangkat Anda',
      })
    } catch (error) {
      console.error('Error exporting school reports:', error)
      toast({
        title: 'Gagal mengekspor laporan',
        description: 'Terjadi kesalahan saat mengekspor data',
        variant: 'destructive',
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Helper function to escape CSV values
  const escapeCsvValue = (value: string | number | null | undefined): string => {
    if (value === null || value === undefined) return '""'
    const stringValue = String(value)
    if (stringValue.includes('"') || stringValue.includes(',') || stringValue.includes('\n')) {
      return `"${stringValue.replace(/"/g, '""')}"`
    }
    return stringValue
  }

  // Helper function to get current WITA time
  const getCurrentWITATime = () => {
    return new Date(new Date().toLocaleString('en-US', { timeZone: 'Asia/Makassar' }))
  }

  // Generate school-specific CSV
  const generateSchoolCSV = (reports: SchoolReport[], stats: SchoolStats): string => {
    const reportDate =
      date === 'today' ? 'Hari Ini' : date === 'yesterday' ? 'Kemarin' : 'Minggu Ini'
    const className = classFilter === 'all' ? 'Semua Kelas' : classFilter
    const currentTime = getCurrentWITATime()
    const exportDate =
      currentTime.toLocaleDateString('id-ID', {
        day: '2-digit',
        month: 'long',
        year: 'numeric',
        timeZone: 'Asia/Makassar',
      }) +
      ' ' +
      currentTime.toLocaleTimeString('id-ID', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        timeZone: 'Asia/Makassar',
      }) +
      ' WITA'

    // Create metadata
    const metadata = [
      ['=== LAPORAN ABSENSI SEKOLAH ==='],
      ['SMK Negeri 3 Banjarmasin'],
      ['https://smkn3banjarmasin.sch.id/'],
      [''],
      ['INFORMASI LAPORAN'],
      [`Periode: ${reportDate}`],
      [`Filter Kelas: ${className}`],
      [`Total Siswa: ${stats.total} siswa`],
      [`Tanggal Ekspor: ${exportDate}`],
      [''],
      ['STATISTIK KEHADIRAN SEKOLAH'],
      [`Masuk Sekolah: ${stats.entry} siswa (${stats.entryPercentage}%)`],
      [`Terlambat: ${stats.lateEntry} siswa (${stats.lateEntryPercentage}%)`],
      [`Sakit: ${stats.sick} siswa (${stats.sickPercentage}%)`],
      [`Ijin Sementara: ${stats.temporaryLeave} siswa (${stats.temporaryLeavePercentage}%)`],
      [`Kembali Ijin: ${stats.returnFromLeave} siswa (${stats.returnFromLeavePercentage}%)`],
      [`Pulang: ${stats.dismissal} siswa (${stats.dismissalPercentage}%)`],
      [''],
      ['=== DETAIL DATA ABSENSI SEKOLAH ==='],
      [''],
    ]

    // Create headers with better information architecture
    const headers = [
      'No',
      'Kode_Siswa',
      'Nama_Lengkap',
      'Kelas',
      'Tanggal_Laporan',
      'Hari',
      'Status_Kehadiran_Utama',
      'Skor_Kedisiplinan_Persen',
      'Tingkat_Kedisiplinan',
      'Masuk_Sekolah',
      'Waktu_Masuk',
      'Status_Keterlambatan',
      'Waktu_Terlambat',
      'Status_Kesehatan',
      'Waktu_Sakit',
      'Ijin_Keluar_Sementara',
      'Waktu_Ijin_Keluar',
      'Kembali_dari_Ijin',
      'Waktu_Kembali',
      'Absen_Pulang',
      'Waktu_Pulang',
      'Timeline_Aktivitas',
      'Analisis_Kehadiran',
      'Rekomendasi_Tindakan',
      'Keterangan_Lengkap',
    ]

    // Convert reports data to CSV rows
    const rows: string[][] = []

    reports.forEach((report, index) => {
      // Calculate discipline score and status
      let disciplineScore = 100
      let attendanceStatus = 'HADIR'

      if (report.sick) {
        attendanceStatus = 'SAKIT'
        disciplineScore = 80 // Excused absence
      } else if (report.excusedAbsence) {
        attendanceStatus = 'IJIN'
        disciplineScore = 85 // Excused absence
      } else if (report.temporaryLeave) {
        attendanceStatus = 'IJIN_SEMENTARA'
        disciplineScore = 90 // Temporary leave
      } else if (report.lateEntry) {
        attendanceStatus = 'TERLAMBAT'
        disciplineScore = 70 // Late entry penalty
      } else if (!report.entry) {
        attendanceStatus = 'TIDAK_HADIR'
        disciplineScore = 0 // Unexcused absence
      }

      // Determine discipline level
      const disciplineLevel =
        disciplineScore >= 90
          ? 'SANGAT_BAIK'
          : disciplineScore >= 70
            ? 'BAIK'
            : disciplineScore >= 50
              ? 'CUKUP'
              : 'PERLU_PERBAIKAN'

      // Generate activity timeline
      const activities = []
      if (report.entry) activities.push(`Masuk: ${report.entryTime}`)
      if (report.lateEntry) activities.push(`Terlambat: ${report.lateEntryTime}`)
      if (report.temporaryLeave) activities.push(`Ijin Keluar: ${report.temporaryLeaveTime}`)
      if (report.returnFromLeave) activities.push(`Kembali: ${report.returnFromLeaveTime}`)
      if (report.dismissal) activities.push(`Pulang: ${report.dismissalTime}`)
      if (report.sick) activities.push(`Sakit: ${report.sickTime}`)

      // Generate analysis
      const analysis = []
      if (report.sick) {
        analysis.push('Siswa dalam kondisi sakit dan tidak dapat hadir ke sekolah')
      } else if (report.excusedAbsence) {
        analysis.push('Siswa memiliki ijin resmi untuk tidak hadir ke sekolah')
      } else if (report.temporaryLeave) {
        analysis.push('Siswa hadir namun keluar sekolah sementara dengan ijin')
        if (report.returnFromLeave) analysis.push('sudah kembali ke sekolah')
      } else if (report.lateEntry) {
        analysis.push('Siswa hadir namun datang terlambat, perlu perbaikan kedisiplinan')
      } else if (report.entry) {
        analysis.push('Siswa menunjukkan kedisiplinan yang baik dengan hadir tepat waktu')
      } else {
        analysis.push('Siswa tidak hadir tanpa keterangan, perlu tindak lanjut segera')
      }

      // Generate recommendations
      const recommendations = []
      if (disciplineScore === 0) {
        recommendations.push('Hubungi orang tua siswa segera')
        recommendations.push('Lakukan konseling dengan siswa')
        recommendations.push('Buat surat peringatan')
        recommendations.push('Pantau kehadiran minggu depan')
      } else if (disciplineScore < 70) {
        recommendations.push('Berikan teguran dan motivasi')
        recommendations.push('Koordinasi dengan wali kelas')
        recommendations.push('Pantau perkembangan kedisiplinan')
      } else if (disciplineScore < 90) {
        recommendations.push('Berikan dukungan dan motivasi')
        recommendations.push('Pantau kondisi kesehatan/keluarga')
      } else {
        recommendations.push('Berikan apresiasi atas kedisiplinan')
        recommendations.push('Jadikan contoh untuk siswa lain')
      }

      // Generate detailed notes
      const detailedNotes = []
      detailedNotes.push(`Status kehadiran: ${attendanceStatus}`)
      detailedNotes.push(`Skor kedisiplinan: ${disciplineScore}%`)

      if (report.entry) {
        detailedNotes.push(`Masuk sekolah: ${report.entryTime}`)
        if (!report.lateEntry) detailedNotes.push('Tepat waktu')
      }

      if (report.lateEntry) {
        detailedNotes.push(`Terlambat: ${report.lateEntryTime}`)
      }

      if (report.sick) {
        detailedNotes.push(`Kondisi sakit: ${report.sickTime}`)
      }

      if (report.temporaryLeave) {
        detailedNotes.push(`Ijin keluar: ${report.temporaryLeaveTime}`)
        if (report.returnFromLeave) {
          detailedNotes.push(`Kembali: ${report.returnFromLeaveTime}`)
        }
      }

      if (report.dismissal) {
        detailedNotes.push(`Pulang: ${report.dismissalTime}`)
      } else {
        detailedNotes.push('Belum absen pulang')
      }

      const dayName = report.summaryDate
        ? new Date(report.summaryDate).toLocaleDateString('id-ID', {
            weekday: 'long',
            timeZone: 'Asia/Makassar',
          })
        : '-'

      rows.push([
        escapeCsvValue((index + 1).toString()),
        escapeCsvValue(report.uniqueCode),
        escapeCsvValue(report.name),
        escapeCsvValue(report.className),
        escapeCsvValue(report.summaryDate || '-'),
        escapeCsvValue(dayName),
        escapeCsvValue(attendanceStatus),
        escapeCsvValue(disciplineScore.toString()),
        escapeCsvValue(disciplineLevel),
        escapeCsvValue(report.entry ? 'HADIR' : 'TIDAK_HADIR'),
        escapeCsvValue(report.entryTime || '-'),
        escapeCsvValue(report.lateEntry ? 'YA' : 'TIDAK'),
        escapeCsvValue(report.lateEntryTime || '-'),
        escapeCsvValue(report.sick ? 'YA' : 'TIDAK'),
        escapeCsvValue(report.sickTime || '-'),
        escapeCsvValue(report.temporaryLeave ? 'YA' : 'TIDAK'),
        escapeCsvValue(report.temporaryLeaveTime || '-'),
        escapeCsvValue(report.returnFromLeave ? 'YA' : 'TIDAK'),
        escapeCsvValue(report.returnFromLeaveTime || '-'),
        escapeCsvValue(report.dismissal ? 'SUDAH' : 'BELUM'),
        escapeCsvValue(report.dismissalTime || '-'),
        escapeCsvValue(activities.join('; ') || 'Tidak ada aktivitas'),
        escapeCsvValue(analysis.join('. ')),
        escapeCsvValue(recommendations.join('. ')),
        escapeCsvValue(detailedNotes.join('; ')),
      ])
    })

    // Create comprehensive footer
    const footer = [
      [''],
      ['=== PANDUAN INTERPRETASI DATA ==='],
      [''],
      ['1. STATUS KEHADIRAN UTAMA:'],
      ['   - HADIR: Masuk sekolah tepat waktu tanpa keterlambatan'],
      ['   - TERLAMBAT: Masuk sekolah namun datang terlambat'],
      ['   - SAKIT: Tidak hadir karena kondisi kesehatan'],
      ['   - IJIN: Tidak hadir dengan ijin resmi dari sekolah'],
      ['   - IJIN_SEMENTARA: Hadir namun keluar sekolah sementara dengan ijin'],
      ['   - TIDAK_HADIR: Tidak hadir tanpa keterangan yang jelas'],
      [''],
      ['2. SKOR KEDISIPLINAN:'],
      ['   - 100%: Hadir tepat waktu (kedisiplinan sempurna)'],
      ['   - 90%: Ijin sementara (masih dalam kategori baik)'],
      ['   - 85%: Ijin resmi (dapat diterima dengan alasan valid)'],
      ['   - 80%: Sakit (kondisi yang dapat dimaklumi)'],
      ['   - 70%: Terlambat (perlu perbaikan kedisiplinan)'],
      ['   - 0%: Tidak hadir tanpa keterangan (sangat perlu perhatian)'],
      [''],
      ['3. TINGKAT KEDISIPLINAN:'],
      ['   - SANGAT_BAIK: Skor 90-100% - Siswa menunjukkan kedisiplinan tinggi'],
      ['   - BAIK: Skor 70-89% - Siswa cukup disiplin namun perlu motivasi'],
      ['   - CUKUP: Skor 50-69% - Siswa perlu bimbingan kedisiplinan'],
      ['   - PERLU_PERBAIKAN: Skor 0-49% - Siswa memerlukan perhatian khusus'],
      [''],
      ['4. TIMELINE AKTIVITAS:'],
      ['   - Mencatat semua aktivitas siswa selama di sekolah'],
      ['   - Termasuk waktu masuk, keluar sementara, kembali, dan pulang'],
      ['   - Membantu memahami pola kehadiran dan aktivitas siswa'],
      [''],
      ['5. ANALISIS KEHADIRAN:'],
      ['   - Evaluasi kondisi dan alasan kehadiran siswa'],
      ['   - Memberikan konteks untuk memahami situasi siswa'],
      ['   - Membantu dalam pengambilan keputusan tindak lanjut'],
      [''],
      ['6. REKOMENDASI TINDAKAN:'],
      ['   - Saran konkret untuk menangani setiap kondisi siswa'],
      ['   - Disesuaikan dengan tingkat kedisiplinan dan kondisi'],
      ['   - Membantu guru dan staf dalam memberikan bimbingan'],
      [''],
      ['=== TINDAK LANJUT BERDASARKAN SKOR KEDISIPLINAN ==='],
      [''],
      ['Skor 0% (Tidak Hadir Tanpa Keterangan):'],
      ['1. Hubungi orang tua/wali siswa dalam 24 jam'],
      ['2. Lakukan kunjungan rumah jika diperlukan'],
      ['3. Konseling intensif dengan siswa dan keluarga'],
      ['4. Buat surat peringatan resmi'],
      ['5. Pantau kehadiran harian minggu berikutnya'],
      ['6. Koordinasi dengan BK dan wali kelas'],
      [''],
      ['Skor 70% (Terlambat):'],
      ['1. Berikan teguran dan motivasi kepada siswa'],
      ['2. Cari tahu penyebab keterlambatan'],
      ['3. Koordinasi dengan wali kelas untuk pemantauan'],
      ['4. Berikan bimbingan manajemen waktu'],
      ['5. Pantau perkembangan kedisiplinan mingguan'],
      [''],
      ['Skor 80-85% (Sakit/Ijin):'],
      ['1. Pastikan alasan valid dengan bukti yang memadai'],
      ['2. Berikan dukungan dan motivasi'],
      ['3. Pantau kondisi kesehatan/keluarga siswa'],
      ['4. Koordinasi dengan orang tua untuk pemantauan'],
      [''],
      ['Skor 90-100% (Sangat Baik):'],
      ['1. Berikan apresiasi dan penghargaan'],
      ['2. Jadikan contoh positif untuk siswa lain'],
      ['3. Libatkan dalam kegiatan kepemimpinan'],
      ['4. Pertahankan motivasi dan dukungan'],
      [''],
      ['=== INFORMASI TEKNIS ==='],
      [''],
      ['- Waktu menggunakan zona WITA (UTC+8) - Waktu Indonesia Tengah'],
      ['- Data diambil dari sistem absensi real-time dengan QR Code'],
      ['- Laporan mencakup semua aktivitas kehadiran di sekolah'],
      ['- Sistem terintegrasi dengan database siswa dan jadwal sekolah'],
      ['- Pembaruan data dilakukan secara otomatis setiap scan QR'],
      [''],
      [`Diekspor pada: ${exportDate}`],
      [`Sistem Absensi Sekolah - SMK Negeri 3 Banjarmasin`],
      [`Website: https://smkn3banjarmasin.sch.id/`],
    ]

    // Combine all content
    return [
      ...metadata.map(row => row.map(cell => escapeCsvValue(cell)).join(',')),
      headers.map(header => escapeCsvValue(header)).join(','),
      ...rows.map(row => row.join(',')),
      ...footer.map(row => row.map(cell => escapeCsvValue(cell)).join(',')),
    ].join('\n')
  }

  // Filter reports based on search
  const filteredReports = reports.filter(report => {
    if (!searchQuery) return true
    const query = searchQuery.toLowerCase()
    return (
      report.name.toLowerCase().includes(query) ||
      report.uniqueCode.toLowerCase().includes(query) ||
      report.className.toLowerCase().includes(query)
    )
  })

  // Load data on component mount and when filters change
  useEffect(() => {
    if (admin && ['super_admin', 'teacher', 'receptionist'].includes(admin.role || '')) {
      fetchReports()
    }
  }, [admin, date, classFilter])

  // Show loading state
  if (sessionLoading || !admin) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-96 w-full" />
      </div>
    )
  }

  // Access denied for non-authorized roles
  if (!['super_admin', 'teacher', 'receptionist'].includes(admin.role || '')) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <h2 className="mb-2 text-xl font-semibold text-red-600">Akses Ditolak</h2>
          <p className="text-gray-600">Anda tidak memiliki izin untuk mengakses laporan sekolah.</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Laporan Absensi Sekolah
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Kelola dan pantau kehadiran sekolah siswa
          </p>
        </div>
        <Button
          onClick={handleExport}
          disabled={isExporting || reports.length === 0}
          className="bg-green-600 hover:bg-green-700"
        >
          {isExporting ? (
            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <Download className="mr-2 h-4 w-4" />
          )}
          Ekspor CSV
        </Button>
      </div>

      {/* Enhanced Statistics Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-blue-600">{stats.total}</div>
                <div className="text-sm font-medium text-gray-600">Total Siswa</div>
                <div className="mt-1 text-xs text-gray-500">Siswa aktif hari ini</div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                <span className="text-xl text-blue-600">👥</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-green-600">{stats.entryPercentage}%</div>
                <div className="text-sm font-medium text-gray-600">Tingkat Kehadiran</div>
                <div className="mt-1 text-xs text-gray-500">
                  {stats.entry} dari {stats.total} siswa hadir
                </div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <span className="text-xl text-green-600">✅</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-yellow-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-yellow-600">
                  {stats.lateEntryPercentage}%
                </div>
                <div className="text-sm font-medium text-gray-600">Keterlambatan</div>
                <div className="mt-1 text-xs text-gray-500">
                  {stats.lateEntry} siswa datang terlambat
                </div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100">
                <span className="text-xl text-yellow-600">⏰</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-amber-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-amber-600">
                  {Math.round(((stats.entry - stats.lateEntry) / stats.total) * 100)}%
                </div>
                <div className="text-sm font-medium text-gray-600">Kedisiplinan</div>
                <div className="mt-1 text-xs text-gray-500">Hadir tepat waktu</div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-amber-100">
                <span className="text-xl text-amber-600">⭐</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Additional Insights */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
        <Card className="border-red-200 bg-gradient-to-r from-red-50 to-rose-50">
          <CardContent className="p-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-700">{stats.sick}</div>
              <div className="text-sm font-medium text-red-600">Siswa Sakit</div>
              <div className="mt-1 text-xs text-red-500">
                Perlu perhatian kesehatan ({stats.sickPercentage}%)
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-orange-200 bg-gradient-to-r from-orange-50 to-amber-50">
          <CardContent className="p-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-700">{stats.temporaryLeave}</div>
              <div className="text-sm font-medium text-orange-600">Ijin Sementara</div>
              <div className="mt-1 text-xs text-orange-500">
                Keluar sekolah sementara ({stats.temporaryLeavePercentage}%)
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-purple-200 bg-gradient-to-r from-purple-50 to-violet-50">
          <CardContent className="p-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-700">{stats.returnFromLeave}</div>
              <div className="text-sm font-medium text-purple-600">Kembali dari Ijin</div>
              <div className="mt-1 text-xs text-purple-500">
                Sudah kembali ke sekolah ({stats.returnFromLeavePercentage}%)
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-indigo-200 bg-gradient-to-r from-indigo-50 to-blue-50">
          <CardContent className="p-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-indigo-700">{stats.dismissal}</div>
              <div className="text-sm font-medium text-indigo-600">Sudah Pulang</div>
              <div className="mt-1 text-xs text-indigo-500">
                Absen pulang sekolah ({stats.dismissalPercentage}%)
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col gap-4 md:flex-row">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                <Input
                  placeholder="Cari nama siswa atau kode..."
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={date} onValueChange={setDate}>
              <SelectTrigger className="w-full md:w-48">
                <Calendar className="mr-2 h-4 w-4" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="today">Hari Ini</SelectItem>
                <SelectItem value="yesterday">Kemarin</SelectItem>
                <SelectItem value="last7days">7 Hari Terakhir</SelectItem>
                <SelectItem value="last30days">30 Hari Terakhir</SelectItem>
                <SelectItem value="thismonth">Bulan Ini</SelectItem>
                <SelectItem value="lastmonth">Bulan Lalu</SelectItem>
              </SelectContent>
            </Select>
            <Select value={classFilter} onValueChange={setClassFilter}>
              <SelectTrigger className="w-full md:w-48">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Kelas</SelectItem>
                <SelectItem value="x-tkj-1">X TKJ 1</SelectItem>
                <SelectItem value="x-tkj-2">X TKJ 2</SelectItem>
                <SelectItem value="xi-tkj-1">XI TKJ 1</SelectItem>
                <SelectItem value="xi-tkj-2">XI TKJ 2</SelectItem>
                <SelectItem value="xii-tkj-1">XII TKJ 1</SelectItem>
                <SelectItem value="xii-tkj-2">XII TKJ 2</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* School Reports Table */}
      <Card>
        <CardHeader>
          <CardTitle>Data Absensi Sekolah ({filteredReports.length} siswa)</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          ) : filteredReports.length === 0 ? (
            <div className="py-8 text-center text-gray-500">Tidak ada data laporan sekolah</div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="w-12 text-center font-semibold">No</TableHead>
                    <TableHead className="min-w-40 font-semibold">Identitas Siswa</TableHead>
                    <TableHead className="min-w-32 text-center font-semibold">
                      Status Kehadiran
                    </TableHead>
                    <TableHead className="min-w-28 text-center font-semibold">
                      Waktu & Aktivitas
                    </TableHead>
                    <TableHead className="min-w-24 text-center font-semibold">
                      Tingkat Kedisiplinan
                    </TableHead>
                    <TableHead className="min-w-32 text-center font-semibold">Keterangan</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredReports.map((report, index) => {
                    // Calculate discipline score and status
                    let disciplineScore = 100
                    let attendanceStatus = 'HADIR'
                    let statusColor = 'bg-green-100 text-green-800 border-green-200'

                    if (report.sick) {
                      attendanceStatus = 'SAKIT'
                      statusColor = 'bg-red-100 text-red-800 border-red-200'
                      disciplineScore = 80 // Excused absence
                    } else if (report.excusedAbsence) {
                      attendanceStatus = 'IJIN'
                      statusColor = 'bg-blue-100 text-blue-800 border-blue-200'
                      disciplineScore = 85 // Excused absence
                    } else if (report.temporaryLeave) {
                      attendanceStatus = 'IJIN_SEMENTARA'
                      statusColor = 'bg-orange-100 text-orange-800 border-orange-200'
                      disciplineScore = 90 // Temporary leave
                    } else if (report.lateEntry) {
                      attendanceStatus = 'TERLAMBAT'
                      statusColor = 'bg-yellow-100 text-yellow-800 border-yellow-200'
                      disciplineScore = 70 // Late entry penalty
                    } else if (!report.entry) {
                      attendanceStatus = 'TIDAK_HADIR'
                      statusColor = 'bg-red-100 text-red-800 border-red-200'
                      disciplineScore = 0 // Unexcused absence
                    }

                    // Generate activity timeline
                    const activities = []
                    if (report.entry) activities.push(`Masuk: ${report.entryTime}`)
                    if (report.lateEntry) activities.push(`Terlambat: ${report.lateEntryTime}`)
                    if (report.temporaryLeave)
                      activities.push(`Ijin Keluar: ${report.temporaryLeaveTime}`)
                    if (report.returnFromLeave)
                      activities.push(`Kembali: ${report.returnFromLeaveTime}`)
                    if (report.dismissal) activities.push(`Pulang: ${report.dismissalTime}`)
                    if (report.sick) activities.push(`Sakit: ${report.sickTime}`)

                    // Generate status text
                    const statusText = {
                      HADIR: 'Hadir',
                      TERLAMBAT: 'Terlambat',
                      SAKIT: 'Sakit',
                      IJIN: 'Ijin',
                      IJIN_SEMENTARA: 'Ijin Sementara',
                      TIDAK_HADIR: 'Tidak Hadir',
                    }[attendanceStatus]

                    // Generate notes
                    const notes = []
                    if (report.sick) {
                      notes.push('Siswa dalam kondisi sakit')
                    } else if (report.excusedAbsence) {
                      notes.push('Memiliki ijin tidak hadir')
                    } else if (report.temporaryLeave) {
                      notes.push('Ijin keluar sementara')
                      if (report.returnFromLeave) notes.push('sudah kembali')
                    } else if (report.lateEntry) {
                      notes.push('Datang terlambat ke sekolah')
                    } else if (report.entry) {
                      notes.push('Hadir tepat waktu')
                    } else {
                      notes.push('Tidak hadir tanpa keterangan')
                    }

                    return (
                      <TableRow
                        key={report.uniqueCode}
                        className="transition-colors hover:bg-blue-50/50"
                      >
                        <TableCell className="text-center font-medium text-gray-600">
                          {index + 1}
                        </TableCell>

                        {/* Student Identity */}
                        <TableCell className="py-4">
                          <div className="space-y-1">
                            <div className="font-semibold text-gray-900">{report.name}</div>
                            <div className="text-sm text-gray-500">
                              {report.uniqueCode} • {report.className}
                            </div>
                          </div>
                        </TableCell>

                        {/* Attendance Status */}
                        <TableCell className="py-4 text-center">
                          <div className="space-y-2">
                            <Badge className={`${statusColor} font-medium`}>{statusText}</Badge>
                            {report.entry && !report.lateEntry && (
                              <div className="text-xs font-medium text-green-600">Tepat Waktu</div>
                            )}
                          </div>
                        </TableCell>

                        {/* Time & Activities */}
                        <TableCell className="py-4">
                          <div className="space-y-1 text-xs">
                            {activities.length > 0 ? (
                              activities.slice(0, 3).map((activity, idx) => (
                                <div key={idx} className="text-gray-600">
                                  {activity}
                                </div>
                              ))
                            ) : (
                              <div className="text-gray-400">Tidak ada aktivitas</div>
                            )}
                            {activities.length > 3 && (
                              <div className="text-gray-400">+{activities.length - 3} lainnya</div>
                            )}
                          </div>
                        </TableCell>

                        {/* Discipline Score */}
                        <TableCell className="py-4 text-center">
                          <div className="space-y-1">
                            <div
                              className={`text-lg font-bold ${
                                disciplineScore >= 90
                                  ? 'text-green-600'
                                  : disciplineScore >= 70
                                    ? 'text-yellow-600'
                                    : 'text-red-600'
                              }`}
                            >
                              {disciplineScore}%
                            </div>
                            <div className="text-xs text-gray-500">
                              {disciplineScore >= 90
                                ? 'Sangat Baik'
                                : disciplineScore >= 70
                                  ? 'Baik'
                                  : 'Perlu Perbaikan'}
                            </div>
                          </div>
                        </TableCell>

                        {/* Notes */}
                        <TableCell className="py-4">
                          <div className="max-w-32 text-sm text-gray-600">{notes.join(', ')}</div>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
