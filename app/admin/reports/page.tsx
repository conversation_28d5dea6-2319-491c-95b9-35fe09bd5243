'use client'

import { useState, useEffect, useCallback } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { ThemeToggle } from '@/components/theme-toggle'
import { AdminBottomNav } from '@/components/admin-bottom-nav'
import { useToast } from '@/components/ui/use-toast'
import {
  Download,
  Search,
  Trash2,
  Loader2,
  RefreshCw,
  Users,
  X,
  Calendar,
  CalendarDays,
  CalendarRange,
} from 'lucide-react'
import { mockReports } from '@/lib/mock-data'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { AttendanceType } from '@/lib/domain/entities/absence'
import { ColoredProgress } from '@/components/ui/colored-progress'
import { useAdminSession } from '@/hooks/use-admin-session'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  format,
  subMonths,
  subYears,
  startOfMonth,
  endOfMonth,
  startOfYear,
  endOfYear,
} from 'date-fns'
import { id } from 'date-fns/locale'
import { formatTimeWITA, getCurrentWITATime } from '@/lib/utils/date'
import { clientConfig } from '@/lib/config'

// Define the interface for attendance report data
interface AttendanceReport {
  uniqueCode: string
  name: string
  className: string
  zuhr: boolean
  zuhrTime: string | null
  asr: boolean
  asrTime: string | null
  dismissal: boolean
  dismissalTime: string | null
  ijin: boolean
  ijinTime?: string | null
  summaryDate: string
  // Add new fields for weekly view
  weeklyRecords?: {
    date: string
    zuhr: boolean
    zuhrTime: string | null
    asr: boolean
    asrTime: string | null
    dismissal: boolean
    dismissalTime: string | null
    ijin: boolean
    ijinTime?: string | null
    rawDate?: Date // Add this property to store the actual date object
    isEmpty?: boolean // Add isEmpty flag to mark days with no attendance data
  }[]
}

// Define interface for attendance statistics
interface AttendanceStats {
  total: number
  zuhr: number
  asr: number
  dismissal: number
  ijin: number
  // Tambahkan properti baru untuk tampilan mingguan
  totalPossibleDays?: number
  effectiveStudentDays?: number
  zuhrPercentage?: number
  asrPercentage?: number
  dismissalPercentage?: number
  ijinPercentage?: number
}

// Add interface for the day data in allDaysInWeek
interface WeekDay {
  dayKey: string
  formattedDate: string
  date: Date
}

// Tambahkan interface untuk weekly record (sekitar baris 35-55)
interface WeeklyRecord {
  date: string
  zuhr: boolean
  zuhrTime: string | null
  asr: boolean
  asrTime: string | null
  dismissal: boolean
  dismissalTime: string | null
  ijin: boolean
  ijinTime?: string | null
  rawDate: Date
  isEmpty?: boolean
}

// Tambahkan interface untuk record mingguan student
interface StudentWeeklyRecord {
  uniqueCode: string
  name: string
  className: string
  zuhr: boolean
  zuhrTime: string | null
  asr: boolean
  asrTime: string | null
  dismissal: boolean
  dismissalTime: string | null
  ijin: boolean
  ijinTime: string | null
  summaryDate: string
  weeklyRecords: Record<string, WeeklyRecord>
}

// Tambahkan interface DayStats untuk memperbaiki error tipe data
interface DayStats {
  zuhr: number
  asr: number
  dismissal: number
  ijin: number
  total: number
  date: string
}

export default function AdminReports() {
  const { toast } = useToast()
  const [date, setDate] = useState('today')
  const [classFilter, setClassFilter] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('')
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [deleteLoading, setDeleteLoading] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [reports, setReports] = useState<AttendanceReport[]>([])
  const [classes, setClasses] = useState<{ id: number; name: string }[]>([])
  const [totalStudents, setTotalStudents] = useState(0)
  const [lastActiveFilter, setLastActiveFilter] = useState({ date: 'today', classFilter: 'all' })

  // New state for report type
  const [reportType, setReportType] = useState<'prayer' | 'school'>('prayer')
  const [stats, setStats] = useState<AttendanceStats>({
    total: 0,
    zuhr: 0,
    asr: 0,
    dismissal: 0,
    ijin: 0,
  })
  const [selectedAttendance, setSelectedAttendance] = useState<{
    uniqueCode: string
    name: string
    type: AttendanceType
    date: string
  } | null>(null)
  const [isExporting, setIsExporting] = useState(false)
  const [showExportConfirmDialog, setShowExportConfirmDialog] = useState(false)
  const [showExportOptions, setShowExportOptions] = useState(false)
  const [calendarView, setCalendarView] = useState<'day' | 'month' | 'year'>('day')
  const [selectedMonth, setSelectedMonth] = useState<Date>(new Date())
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear())
  const [isExportingMonth, setIsExportingMonth] = useState(false)
  const [isExportingYear, setIsExportingYear] = useState(false)
  const { admin } = useAdminSession()

  // Role-based access control for report types
  const canAccessPrayerReports = admin?.role === 'super_admin' || admin?.role === 'admin'
  const canAccessSchoolReports =
    admin?.role === 'super_admin' || admin?.role === 'teacher' || admin?.role === 'receptionist'

  // Set default report type based on role
  useEffect(() => {
    if (!canAccessPrayerReports && canAccessSchoolReports) {
      setReportType('school')
    } else if (canAccessPrayerReports && !canAccessSchoolReports) {
      setReportType('prayer')
    }
  }, [admin?.role, canAccessPrayerReports, canAccessSchoolReports])

  // Implement search debouncing
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery)
    }, 300) // 300ms delay

    return () => clearTimeout(timer)
  }, [searchQuery])

  // Clear search query
  const handleClearSearch = useCallback(() => {
    setSearchQuery('')
  }, [])

  // Fetch attendance reports from the API - wrapped in useCallback to prevent dependency issues
  const fetchReports = useCallback(
    async (forceFresh = false) => {
      try {
        setIsLoading(true)

        // Convert date selection to API parameter
        let dateParam = ''
        if (date === 'today') {
          // For "today", we'll send a special parameter that tells the backend to use the current date
          dateParam = 'today'
        } else if (date === 'yesterday') {
          // For "yesterday", create an actual date object for yesterday using WITA time
          const yesterday = getCurrentWITATime()
          yesterday.setDate(yesterday.getDate() - 1)
          // Format as YYYY-MM-DD for the API
          dateParam = yesterday.toISOString().split('T')[0]
        } else if (date === 'week') {
          // For "week", we'll send a special parameter
          dateParam = 'week'
        }

        // Convert class filter to API parameter
        let classParam = ''
        if (classFilter !== 'all') {
          classParam = classFilter.replace(/-/g, ' ').toUpperCase()
        }

        // Build the query string
        const queryParams = new URLSearchParams()
        if (dateParam) queryParams.append('date', dateParam)
        if (classParam) queryParams.append('class', classParam)

        // Add report type parameter
        queryParams.append('reportType', reportType)

        // Add a cache-busting parameter to prevent browser caching
        queryParams.append('_t', Date.now().toString())

        // Add force fresh parameter to invalidate server-side cache
        if (forceFresh) {
          queryParams.append('force_fresh', 'true')
        }

        const response = await fetch(`/api/absence/reports?${queryParams.toString()}`, {
          // Add cache control headers to prevent browser caching
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            Pragma: 'no-cache',
            Expires: '0',
            'X-Timestamp': Date.now().toString(), // Add unique timestamp
          },
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to fetch reports')
        }

        const data = await response.json()

        // Print the first few records for debugging
        if (data.length > 0) {
          console.log('Sample data:', JSON.stringify(data.slice(0, 2), null, 2))

          // For weekly view, analyze date distribution to verify we're getting all days
          if (date === 'week') {
            console.log('========== ANALYZING WEEKLY DATE DISTRIBUTION ==========')
            // Create a map to count records per day
            const dateCountMap = new Map()

            // Get today in WITA timezone and calculate the week range - use consistent date creation
            const today = getCurrentWITATime()
            // Create a clean date with just year, month, day (no time)
            const cleanToday = new Date(today.getFullYear(), today.getMonth(), today.getDate())
            cleanToday.setHours(0, 0, 0, 0)
            console.log(`Today's date for analysis (cleaned): ${cleanToday.toISOString()}`)

            // Check last 7 days (including today)
            const weekDates = []
            for (let i = 0; i < 7; i++) {
              const checkDate = new Date(cleanToday)
              checkDate.setDate(checkDate.getDate() - i)
              const dateKey = checkDate.toISOString().split('T')[0]
              weekDates.push(dateKey)
              dateCountMap.set(dateKey, 0)
            }
            console.log('Expected dates in week range:', weekDates)

            // Count records per day
            let totalRecordsAnalyzed = 0
            data.forEach((item: any) => {
              totalRecordsAnalyzed++
              const recordDate = new Date(item.summaryDate)
              const dateKey = recordDate.toISOString().split('T')[0]
              console.log(`Record date: ${item.summaryDate} -> mapped to ${dateKey}`)

              if (dateCountMap.has(dateKey)) {
                dateCountMap.set(dateKey, dateCountMap.get(dateKey) + 1)
              } else {
                // If we see a date outside our expected range, log it
                console.log(`!! Unexpected date found: ${dateKey} (outside weekly range)`)
              }
            })

            console.log(`Analyzed ${totalRecordsAnalyzed} records for weekly distribution`)

            // Convert to array for easier logging
            const distributionArray = Array.from(dateCountMap.entries())
              .sort()
              .map(([date, count]) => ({ date, count }))

            console.log('Weekly date distribution:')
            console.log(JSON.stringify(distributionArray, null, 2))
            console.log('===========================================')
          }
        }

        // Format the data for the UI
        let formattedReports: AttendanceReport[] = []

        if (date === 'week') {
          // For weekly view, always use the backend-transformed format
          // The backend now provides data in the correct format with weeklyRecords
          console.log('Processing weekly data from backend:', {
            dataLength: data.length,
            firstItem: data[0],
            hasWeeklyRecords: data[0]?.weeklyRecords ? Array.isArray(data[0].weeklyRecords) : false,
          })

          formattedReports = data.map((student: any) => ({
            uniqueCode: student.uniqueCode,
            name: student.name,
            className: student.className,
            zuhr: student.zuhr,
            zuhrTime: student.zuhr ? '✓' : null,
            asr: student.asr,
            asrTime: student.asr ? '✓' : null,
            dismissal: student.dismissal,
            dismissalTime: student.dismissal ? '✓' : null,
            ijin: student.ijin || false,
            ijinTime: student.ijin ? '✓' : null,
            summaryDate: student.summaryDate,
            weeklyRecords: student.weeklyRecords || [],
          }))

          console.log('Formatted weekly reports:', {
            count: formattedReports.length,
            sampleRecord: formattedReports[0],
            weeklyRecordsCount: formattedReports[0]?.weeklyRecords?.length || 0,
          })
        } else {
          // For daily views, format as before
          formattedReports = data.map((item: any) => {
            // Format date for display
            const summaryDate = new Date(item.summaryDate)

            // Format date using WITA timezone for consistency
            const formattedDate = summaryDate.toLocaleDateString('id-ID', {
              day: '2-digit',
              month: 'short',
              year: 'numeric',
              timeZone: 'Asia/Makassar', // WITA timezone
            })

            // Debug the date formatting

            // Format times for display (if available)
            let zuhrTime = null
            let asrTime = null
            let dismissalTime = null
            let ijinTime = null

            if (item.updatedAt) {
              const updatedAt = new Date(item.updatedAt)

              // Use formatTimeWITA for consistent WITA timezone handling
              const timeStr = formatTimeWITA(updatedAt) + ' WITA'

              // Assign times based on attendance type
              if (item.zuhr) {
                zuhrTime = `${formattedDate}, ${timeStr}`
              }
              if (item.asr) {
                asrTime = `${formattedDate}, ${timeStr}`
              }
              if (item.dismissal) {
                dismissalTime = `${formattedDate}, ${timeStr}`
              }
              if (item.ijin) {
                ijinTime = `${formattedDate}, ${timeStr}`
              }
            }

            return {
              uniqueCode: item.uniqueCode,
              name: item.name,
              className: item.className,
              zuhr: item.zuhr,
              zuhrTime,
              asr: item.asr,
              asrTime,
              dismissal: item.dismissal,
              dismissalTime,
              ijin: item.ijin || false,
              ijinTime,
              summaryDate: formattedDate,
            }
          })
        }

        setReports(formattedReports)
      } catch (error) {
        console.error('Error fetching reports:', error)

        // Handle error message
        let errorMessage = 'Gagal mengambil data laporan'
        if (error instanceof Error) {
          errorMessage = error.message || errorMessage
        }

        toast({
          title: 'Error',
          description: errorMessage,
          variant: 'destructive',
        })

        // Use mock data as fallback
        setReports(
          mockReports.map(report => ({
            uniqueCode: report.uniqueCode,
            name: report.name,
            className: report.class,
            zuhr: report.zuhur,
            zuhrTime: report.zuhurTime,
            asr: report.asr,
            asrTime: report.asrTime,
            dismissal: report.dismissal,
            dismissalTime: report.dismissalTime,
            ijin: false, // Default mock ijin value
            ijinTime: null,
            summaryDate: '29 Apr 2025',
          }))
        )
      } finally {
        setIsLoading(false)
      }
    },
    [date, classFilter, reportType, toast]
  ) // Add dependencies: date, classFilter, reportType, and toast

  // Fetch classes from the API
  const fetchClasses = useCallback(async () => {
    try {
      const response = await fetch('/api/classes')

      if (!response.ok) {
        throw new Error('Failed to fetch classes')
      }

      const data = await response.json()
      setClasses(data)
    } catch (error) {
      console.error('Error fetching classes:', error)
      // Don't show a toast for this error to avoid overwhelming the user
    }
  }, [])

  // Fetch total students count based on filter
  const fetchTotalStudents = useCallback(async () => {
    try {
      // Build query params based on current class filter
      const queryParams = new URLSearchParams()
      if (classFilter !== 'all') {
        queryParams.append('class', classFilter.replace(/-/g, ' ').toUpperCase())
      }

      // Add force=true parameter to bypass cache and get fresh count
      queryParams.append('force', 'true')

      const response = await fetch(`/api/students/count?${queryParams.toString()}`, {
        // Add credentials to ensure auth cookies are sent
        credentials: 'include',
        // Add headers to prevent caching
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
        },
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error(`Student count API error (${response.status}):`, errorData)

        // Use reports length as fallback for the count
        const fallbackCount = reports.length
        setTotalStudents(fallbackCount)

        return
      }

      const data = await response.json()
      setTotalStudents(data.count)
    } catch (error) {
      console.error('Error fetching student count:', error)

      // Use reports length or a default value (3 based on your data) as fallback
      const fallbackCount = reports.length > 0 ? reports.length : 3
      setTotalStudents(fallbackCount)
    }
  }, [classFilter, reports.length])

  // Fetch classes on component mount
  useEffect(() => {
    fetchClasses()
  }, [fetchClasses])

  // Fetch total students when class filter changes
  useEffect(() => {
    fetchTotalStudents()
  }, [fetchTotalStudents])

  // Fetch reports when filters change
  useEffect(() => {
    // Update lastActiveFilter whenever date or classFilter changes
    setLastActiveFilter({ date, classFilter })

    // Then fetch reports with fresh data
    fetchReports(true) // Force fresh data on filter changes
  }, [date, classFilter, fetchReports])

  // Refresh data when the page becomes visible again
  useEffect(() => {
    // Function to handle visibility change
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // Check if the current state matches the last active filter
        if (date !== lastActiveFilter.date) {
          setDate(lastActiveFilter.date)
        }

        if (classFilter !== lastActiveFilter.classFilter) {
          setClassFilter(lastActiveFilter.classFilter)
        }

        // Wait a moment for state updates to propagate before fetching
        setTimeout(() => {
          fetchReports(true)
        }, 100)
      }
    }

    // Function to handle focus change
    const handleFocus = () => {
      // Check if the current state matches the last active filter
      if (date !== lastActiveFilter.date) {
        setDate(lastActiveFilter.date)
      }

      if (classFilter !== lastActiveFilter.classFilter) {
        setClassFilter(lastActiveFilter.classFilter)
      }

      // Wait a moment for state updates to propagate before fetching
      setTimeout(() => {
        fetchReports(true)
      }, 100)
    }

    // Add event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('focus', handleFocus)

    // Clean up event listeners on component unmount
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('focus', handleFocus)
    }
  }, [date, classFilter, lastActiveFilter, fetchReports])

  // Calculate attendance statistics whenever reports change
  useEffect(() => {
    if (reports.length > 0) {
      // Count the number of students who have attended each prayer
      let zuhrCount = 0
      let asrCount = 0
      let dismissalCount = 0
      let ijinCount = 0

      // Hitung jumlah siswa dengan izin khusus
      const studentsWithIjin = new Set()

      // First pass - identify students with Ijin
      for (const report of reports) {
        if (report.ijin) {
          studentsWithIjin.add(report.uniqueCode)
          ijinCount++
        }
      }

      // Second pass - count attendance considering Ijin status
      for (const report of reports) {
        if (report.zuhr) zuhrCount++
        if (report.asr) asrCount++
        if (report.dismissal) dismissalCount++
      }

      // For weekly reports, we don't have a clear way to count totals
      if (date === 'week') {
        // For the week, get the total from all unique students
        const enrolledStudentsCount =
          totalStudents > 0 ? totalStudents : new Set(reports.map(r => r.uniqueCode)).size

        // We'll track stats by day to calculate averages and trends (though trend display is removed)
        // const dayStats: Record<string, DayStats> = {}

        // Track total attendance events by type
        let uniqueZuhrEvents = 0
        let uniqueAsrEvents = 0
        let uniqueDismissalEvents = 0
        let uniqueIjinEvents = 0 // Total ijin events

        // Count days with any data - to get accurate denominators for percentages
        // const daysWithData = new Set() // No longer used for main denominator

        reports.forEach(report => {
          if (report.weeklyRecords && report.weeklyRecords.length > 0) {
            report.weeklyRecords.forEach(dailyRecord => {
              if (!dailyRecord.isEmpty && dailyRecord.rawDate) {
                // const dayKey = dailyRecord.rawDate.toISOString().split('T')[0]
                // daysWithData.add(dayKey) // No longer used for main denominator

                // Count attendance events
                if (dailyRecord.zuhr) uniqueZuhrEvents++
                if (dailyRecord.asr) uniqueAsrEvents++
                if (dailyRecord.dismissal) uniqueDismissalEvents++
                if (dailyRecord.ijin) {
                  uniqueIjinEvents++
                }
              }
            })
          } else {
            // Fallback for reports without daily records (should ideally not happen for weekly view)
            if (report.zuhr) uniqueZuhrEvents++
            if (report.asr) uniqueAsrEvents++
            if (report.dismissal) uniqueDismissalEvents++
            if (report.ijin) {
              uniqueIjinEvents++
            }
          }
        })

        const numDaysInFilterPeriod = 7
        const calcTotalPossibleStudentDays = enrolledStudentsCount * numDaysInFilterPeriod
        const calcEffectivePrayerStudentDays = calcTotalPossibleStudentDays - uniqueIjinEvents

        // Calculate overall weekly stats based on new denominators
        const zuhrPercentage =
          calcEffectivePrayerStudentDays > 0
            ? (uniqueZuhrEvents / calcEffectivePrayerStudentDays) * 100
            : 0
        const asrPercentage =
          calcEffectivePrayerStudentDays > 0
            ? (uniqueAsrEvents / calcEffectivePrayerStudentDays) * 100
            : 0
        const dismissalPercentage =
          calcTotalPossibleStudentDays > 0
            ? (uniqueDismissalEvents / calcTotalPossibleStudentDays) * 100
            : 0
        const ijinPercentage =
          calcTotalPossibleStudentDays > 0
            ? (uniqueIjinEvents / calcTotalPossibleStudentDays) * 100
            : 0

        console.log('Weekly statistics (revised denominators):', {
          enrolledStudentsCount,
          numDaysInFilterPeriod,
          calcTotalPossibleStudentDays,
          uniqueZuhrEvents,
          uniqueAsrEvents,
          uniqueDismissalEvents,
          uniqueIjinEvents, // Now total ijin events
          calcEffectivePrayerStudentDays,
          zuhrPercentage,
          asrPercentage,
          dismissalPercentage,
          ijinPercentage,
        })

        // Set the main stats
        setStats({
          total: enrolledStudentsCount, // Total unique students in filter
          zuhr: uniqueZuhrEvents,
          asr: uniqueAsrEvents,
          dismissal: uniqueDismissalEvents,
          ijin: uniqueIjinEvents, // Now using total ijin events
          totalPossibleDays: calcTotalPossibleStudentDays, // Base denominator for non-prayer
          effectiveStudentDays: calcEffectivePrayerStudentDays, // Base denominator for prayer
          zuhrPercentage,
          asrPercentage,
          dismissalPercentage,
          ijinPercentage,
        })
      } else {
        // For daily reports, get the total number of students
        // The actual denominator for Zuhr and Asr attendance percentage will be (total - ijin) at the UI level
        // This ensures students with Ijin are not counted as "not attending" for prayer activities
        const effectiveTotal = totalStudents > 0 ? totalStudents : reports.length

        setStats({
          total: effectiveTotal,
          zuhr: zuhrCount,
          asr: asrCount,
          dismissal: dismissalCount,
          ijin: ijinCount,
        })
      }
    } else {
      // When there are no reports, still use the totalStudents value
      // The totalStudents represents all students with role='student'
      setStats({
        total: totalStudents,
        zuhr: 0,
        asr: 0,
        dismissal: 0,
        ijin: 0,
      })
    }
  }, [reports, totalStudents, date])

  // Filter reports based on search query - enhanced to include class name
  const filteredReports = reports.filter(report => {
    if (!debouncedSearchQuery) return true

    const query = debouncedSearchQuery.toLowerCase()
    return (
      report.name.toLowerCase().includes(query) ||
      report.uniqueCode.toLowerCase().includes(query) ||
      report.className.toLowerCase().includes(query)
    )
  })

  // Handle initial export button click
  const handleExportClick = () => {
    setShowExportOptions(!showExportOptions)
  }

  // Add function to handle monthly export
  const handleExportMonth = async () => {
    try {
      setIsExportingMonth(true)

      // Calculate the first and last day of the selected month
      const firstDayOfMonth = startOfMonth(selectedMonth)
      const lastDayOfMonth = endOfMonth(selectedMonth)

      // Format month name for the file name and display
      const monthName = format(selectedMonth, 'MMMM-yyyy', { locale: id })

      // Fetch data for the entire month
      const monthlyData = await fetchReportsForDateRange(firstDayOfMonth, lastDayOfMonth)

      // Generate CSV content with monthly report data
      const csvContent = generateCsvForReports(
        monthlyData,
        `Laporan Bulanan: ${format(selectedMonth, 'MMMM yyyy', { locale: id })}`
      )

      // Create download link
      downloadCsv(csvContent, `laporan-absensi-${monthName}.csv`)

      toast({
        title: 'Laporan Bulanan Diekspor',
        description: `Laporan untuk bulan ${format(selectedMonth, 'MMMM yyyy', { locale: id })} telah berhasil diunduh`,
      })
    } catch (error) {
      console.error('Error exporting monthly CSV:', error)
      toast({
        title: 'Gagal mengekspor laporan bulanan',
        description: 'Terjadi kesalahan saat mengekspor data',
        variant: 'destructive',
      })
    } finally {
      setIsExportingMonth(false)
    }
  }

  // Add function to handle yearly export
  const handleExportYear = async () => {
    try {
      setIsExportingYear(true)

      // Calculate the first and last day of the selected year
      const firstDayOfYear = startOfYear(new Date(selectedYear, 0, 1))
      const lastDayOfYear = endOfYear(new Date(selectedYear, 0, 1))

      // Fetch data for the entire year
      const yearlyData = await fetchReportsForDateRange(firstDayOfYear, lastDayOfYear)

      // Generate CSV content with yearly report data
      const csvContent = generateCsvForReports(yearlyData, `Laporan Tahunan: ${selectedYear}`)

      // Create download link
      downloadCsv(csvContent, `laporan-absensi-${selectedYear}.csv`)

      toast({
        title: 'Laporan Tahunan Diekspor',
        description: `Laporan untuk tahun ${selectedYear} telah berhasil diunduh`,
      })
    } catch (error) {
      console.error('Error exporting yearly CSV:', error)
      toast({
        title: 'Gagal mengekspor laporan tahunan',
        description: 'Terjadi kesalahan saat mengekspor data',
        variant: 'destructive',
      })
    } finally {
      setIsExportingYear(false)
    }
  }

  // Helper function to fetch reports for a specific date range
  const fetchReportsForDateRange = async (startDate: Date, endDate: Date) => {
    try {
      // Format dates to ISO strings
      const startDateStr = startDate.toISOString().split('T')[0]
      const endDateStr = endDate.toISOString().split('T')[0]

      // Build query params
      const queryParams = new URLSearchParams()
      queryParams.append('startDate', startDateStr)
      queryParams.append('endDate', endDateStr)
      if (classFilter !== 'all') {
        queryParams.append('class', classFilter.replace(/-/g, ' ').toUpperCase())
      }

      // Add cache busting
      queryParams.append('_t', Date.now().toString())

      const response = await fetch(`/api/absence/reports/range?${queryParams.toString()}`, {
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
          Expires: '0',
          'X-Timestamp': Date.now().toString(),
        },
      })

      if (!response.ok) {
        throw new Error('Failed to fetch date range reports')
      }

      return await response.json()
    } catch (error) {
      console.error('Error fetching date range reports:', error)
      throw error
    }
  }

  // Helper function to generate enhanced CSV for reports based on report type
  const generateCsvForReports = (reports: AttendanceReport[], title: string) => {
    // Add report metadata with WITA timezone
    const reportDate = title
    const className = classFilter === 'all' ? 'Semua Kelas' : classFilter
    const currentTime = getCurrentWITATime()
    const exportDate =
      currentTime.toLocaleString('id-ID', {
        timeZone: 'Asia/Makassar',
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      }) + ' WITA'

    // Get unique student count
    const uniqueStudents = new Set(reports.map(r => r.uniqueCode)).size

    // Generate report based on type
    if (reportType === 'prayer') {
      return generatePrayerCSV(reports, reportDate, className, uniqueStudents, exportDate)
    } else {
      return generateSchoolCSV(reports, reportDate, className, uniqueStudents, exportDate)
    }
  }

  // Generate Prayer Attendance CSV
  const generatePrayerCSV = (
    reports: AttendanceReport[],
    reportDate: string,
    className: string,
    uniqueStudents: number,
    exportDate: string
  ) => {
    // Count prayer statistics
    const zuhrCount = reports.filter(r => r.zuhr).length
    const asrCount = reports.filter(r => r.asr).length
    const dismissalCount = reports.filter(r => r.dismissal).length
    const ijinCount = reports.filter(r => r.ijin).length

    // Create enhanced metadata rows
    const metadata = [
      ['=== LAPORAN ABSENSI SHALAT ==='],
      [clientConfig.school.name],
      [clientConfig.school.address || 'Banjarmasin, Kalimantan Selatan'],
      [''],
      ['INFORMASI LAPORAN'],
      ['Jenis Laporan:', 'Absensi Shalat (Zuhur, Asr, Ijin, Pulang)'],
      ['Periode:', reportDate],
      ['Filter Kelas:', className],
      ['Total Siswa:', `${uniqueStudents} siswa`],
      ['Diekspor pada:', exportDate],
      ['Timezone:', 'WITA (Waktu Indonesia Tengah, UTC+8)'],
      [''],
      ['RINGKASAN STATISTIK KEHADIRAN'],
      [
        'Shalat Zuhur:',
        `${zuhrCount} dari ${uniqueStudents - ijinCount} siswa wajib (${uniqueStudents > 0 ? Math.round((zuhrCount / (uniqueStudents - ijinCount || 1)) * 100) : 0}%)`,
      ],
      [
        'Shalat Asr:',
        `${asrCount} dari ${uniqueStudents - ijinCount} siswa wajib (${uniqueStudents > 0 ? Math.round((asrCount / (uniqueStudents - ijinCount || 1)) * 100) : 0}%)`,
      ],
      [
        'Absen Pulang:',
        `${dismissalCount} dari ${uniqueStudents} siswa (${uniqueStudents > 0 ? Math.round((dismissalCount / uniqueStudents) * 100) : 0}%)`,
      ],
      [
        'Status Ijin:',
        `${ijinCount} siswa (${uniqueStudents > 0 ? Math.round((ijinCount / uniqueStudents) * 100) : 0}%)`,
      ],
      [''],
      ['KETERANGAN'],
      ['✓ = Hadir/Ya', '✗ = Tidak Hadir/Tidak', '- = Tidak Ada Data'],
      ['Siswa dengan status ijin dianggap hadir untuk shalat'],
      ['Waktu yang ditampilkan menggunakan timezone WITA'],
      [''],
      ['=== DETAIL DATA ABSENSI SHALAT ==='],
      [''],
    ]

    // Create prayer CSV header row
    const headers = [
      'No',
      'Kode_Siswa',
      'NIS',
      'Nama_Lengkap',
      'Kelas',
      'Tanggal',
      'Hari',
      'Shalat_Zuhur',
      'Waktu_Zuhur',
      'Shalat_Asr',
      'Waktu_Asr',
      'Pulang',
      'Waktu_Pulang',
      'Ijin_Shalat',
      'Waktu_Ijin',
      'Tingkat_Kepatuhan',
      'Keterangan',
    ]

    // Convert reports data to prayer CSV rows
    const rows: string[][] = []

    reports.forEach((report, index) => {
      // Calculate prayer compliance level
      const prayerCompliance = report.ijin
        ? 'IJIN'
        : report.zuhr && report.asr
          ? 'BAIK'
          : report.zuhr || report.asr
            ? 'CUKUP'
            : 'KURANG'

      // Generate prayer-specific notes
      let keterangan = []
      if (report.ijin) {
        keterangan.push('Siswa dalam status ijin')
      }
      if (!report.zuhr && !report.ijin) {
        keterangan.push('Belum absen Zuhur')
      }
      if (!report.asr && !report.ijin) {
        keterangan.push('Belum absen Asr')
      }
      if (!report.dismissal) {
        keterangan.push('Belum absen pulang')
      }
      if (report.zuhr && report.asr && report.dismissal && !report.ijin) {
        keterangan.push('Absen shalat lengkap')
      }

      // Format date and day
      const reportDate = new Date(report.summaryDate)
      const dayName = reportDate.toLocaleDateString('id-ID', {
        weekday: 'long',
        timeZone: 'Asia/Makassar',
      })

      rows.push([
        escapeCsvValue((index + 1).toString()),
        escapeCsvValue(report.uniqueCode),
        escapeCsvValue((report as any).nis || '-'),
        escapeCsvValue(report.name),
        escapeCsvValue(report.className),
        escapeCsvValue(report.summaryDate),
        escapeCsvValue(dayName),
        escapeCsvValue(report.zuhr ? '✓' : '✗'),
        escapeCsvValue(report.zuhrTime || '-'),
        escapeCsvValue(report.asr ? '✓' : '✗'),
        escapeCsvValue(report.asrTime || '-'),
        escapeCsvValue(report.dismissal ? '✓' : '✗'),
        escapeCsvValue(report.dismissalTime || '-'),
        escapeCsvValue(report.ijin ? '✓' : '✗'),
        escapeCsvValue(report.ijinTime || '-'),
        escapeCsvValue(prayerCompliance),
        escapeCsvValue(keterangan.join('; ') || 'Normal'),
      ])
    })

    // Create footer
    const footer = [
      [''],
      ['=== INFORMASI TAMBAHAN ==='],
      [''],
      ['Tingkat Kepatuhan Shalat:'],
      ['BAIK', 'Hadir Zuhur dan Asr'],
      ['CUKUP', 'Hadir salah satu (Zuhur atau Asr)'],
      ['KURANG', 'Tidak hadir Zuhur dan Asr'],
      ['IJIN', 'Dalam status ijin'],
      [''],
      ['Catatan Penting:'],
      ['1. Data shalat diambil dari sistem absensi digital'],
      ['2. Waktu absen menggunakan timezone WITA (UTC+8)'],
      ['3. Siswa dengan status ijin dianggap hadir untuk shalat'],
      ['4. Absen pulang wajib dilakukan setelah shalat'],
      [''],
      [`Dicetak pada: ${exportDate}`],
      [`Sistem Absensi Shalat - ${clientConfig.school.name}`],
    ]

    // Combine all content
    return [
      ...metadata.map(row => row.map(cell => escapeCsvValue(cell)).join(',')),
      headers.map(header => escapeCsvValue(header)).join(','),
      ...rows.map(row => row.join(',')),
      ...footer.map(row => row.map(cell => escapeCsvValue(cell)).join(',')),
    ].join('\n')
  }

  // Generate School Attendance CSV
  const generateSchoolCSV = (
    reports: AttendanceReport[],
    reportDate: string,
    className: string,
    uniqueStudents: number,
    exportDate: string
  ) => {
    // Count school attendance statistics
    const entryCount = reports.filter(r => (r as any).entry).length
    const lateEntryCount = reports.filter(r => (r as any).lateEntry).length
    const sickCount = reports.filter(r => (r as any).sick).length
    const temporaryLeaveCount = reports.filter(r => (r as any).temporaryLeave).length
    const returnFromLeaveCount = reports.filter(r => (r as any).returnFromLeave).length
    const dismissalCount = reports.filter(r => r.dismissal).length

    // Create enhanced metadata rows
    const metadata = [
      ['=== LAPORAN ABSENSI SEKOLAH ==='],
      [clientConfig.school.name],
      [clientConfig.school.address || 'Banjarmasin, Kalimantan Selatan'],
      [''],
      ['INFORMASI LAPORAN'],
      ['Jenis Laporan:', 'Absensi Sekolah (Masuk, Terlambat, Sakit, Ijin, Pulang)'],
      ['Periode:', reportDate],
      ['Filter Kelas:', className],
      ['Total Siswa:', `${uniqueStudents} siswa`],
      ['Diekspor pada:', exportDate],
      ['Timezone:', 'WITA (Waktu Indonesia Tengah, UTC+8)'],
      [''],
      ['RINGKASAN STATISTIK KEHADIRAN'],
      [
        'Masuk Sekolah:',
        `${entryCount} dari ${uniqueStudents} siswa (${uniqueStudents > 0 ? Math.round((entryCount / uniqueStudents) * 100) : 0}%)`,
      ],
      [
        'Masuk Terlambat:',
        `${lateEntryCount} dari ${uniqueStudents} siswa (${uniqueStudents > 0 ? Math.round((lateEntryCount / uniqueStudents) * 100) : 0}%)`,
      ],
      [
        'Sakit:',
        `${sickCount} dari ${uniqueStudents} siswa (${uniqueStudents > 0 ? Math.round((sickCount / uniqueStudents) * 100) : 0}%)`,
      ],
      [
        'Ijin Sementara:',
        `${temporaryLeaveCount} dari ${uniqueStudents} siswa (${uniqueStudents > 0 ? Math.round((temporaryLeaveCount / uniqueStudents) * 100) : 0}%)`,
      ],
      [
        'Kembali dari Ijin:',
        `${returnFromLeaveCount} dari ${uniqueStudents} siswa (${uniqueStudents > 0 ? Math.round((returnFromLeaveCount / uniqueStudents) * 100) : 0}%)`,
      ],
      [
        'Pulang:',
        `${dismissalCount} dari ${uniqueStudents} siswa (${uniqueStudents > 0 ? Math.round((dismissalCount / uniqueStudents) * 100) : 0}%)`,
      ],
      [''],
      ['KETERANGAN'],
      ['✓ = Ya/Hadir', '✗ = Tidak/Tidak Hadir', '- = Tidak Ada Data'],
      ['Waktu yang ditampilkan menggunakan timezone WITA'],
      [''],
      ['=== DETAIL DATA ABSENSI SEKOLAH ==='],
      [''],
    ]

    // Create school CSV header row
    const headers = [
      'No',
      'Kode_Siswa',
      'NIS',
      'Nama_Lengkap',
      'Kelas',
      'Tanggal',
      'Hari',
      'Masuk_Sekolah',
      'Waktu_Masuk',
      'Terlambat',
      'Waktu_Terlambat',
      'Sakit',
      'Waktu_Sakit',
      'Ijin_Sementara',
      'Waktu_Ijin_Sementara',
      'Kembali_Ijin',
      'Waktu_Kembali_Ijin',
      'Pulang',
      'Waktu_Pulang',
      'Status_Kehadiran',
      'Keterangan',
    ]

    // Convert reports data to school CSV rows
    const rows: string[][] = []

    reports.forEach((report, index) => {
      // Calculate school attendance status
      const attendanceStatus = (report as any).sick
        ? 'SAKIT'
        : (report as any).excusedAbsence
          ? 'IJIN'
          : (report as any).entry && !(report as any).lateEntry
            ? 'HADIR_LENGKAP'
            : (report as any).lateEntry
              ? 'TERLAMBAT'
              : 'TIDAK_HADIR'

      // Generate school-specific notes
      let keterangan = []
      if ((report as any).sick) keterangan.push('Sakit')
      if ((report as any).excusedAbsence) keterangan.push('Ijin resmi')
      if ((report as any).lateEntry) keterangan.push('Terlambat masuk')
      if ((report as any).temporaryLeave) keterangan.push('Ijin sementara')
      if ((report as any).returnFromLeave) keterangan.push('Kembali dari ijin')
      if ((report as any).entry && !(report as any).lateEntry && report.dismissal)
        keterangan.push('Absen lengkap')

      // Format date and day
      const reportDate = new Date(report.summaryDate)
      const dayName = reportDate.toLocaleDateString('id-ID', {
        weekday: 'long',
        timeZone: 'Asia/Makassar',
      })

      rows.push([
        escapeCsvValue((index + 1).toString()),
        escapeCsvValue(report.uniqueCode),
        escapeCsvValue((report as any).nis || '-'),
        escapeCsvValue(report.name),
        escapeCsvValue(report.className),
        escapeCsvValue(report.summaryDate),
        escapeCsvValue(dayName),
        escapeCsvValue((report as any).entry ? '✓' : '✗'),
        escapeCsvValue((report as any).entryTime || '-'),
        escapeCsvValue((report as any).lateEntry ? '✓' : '✗'),
        escapeCsvValue((report as any).lateEntryTime || '-'),
        escapeCsvValue((report as any).sick ? '✓' : '✗'),
        escapeCsvValue((report as any).sickTime || '-'),
        escapeCsvValue((report as any).temporaryLeave ? '✓' : '✗'),
        escapeCsvValue((report as any).temporaryLeaveTime || '-'),
        escapeCsvValue((report as any).returnFromLeave ? '✓' : '✗'),
        escapeCsvValue((report as any).returnFromLeaveTime || '-'),
        escapeCsvValue(report.dismissal ? '✓' : '✗'),
        escapeCsvValue(report.dismissalTime || '-'),
        escapeCsvValue(attendanceStatus),
        escapeCsvValue(keterangan.join('; ') || 'Normal'),
      ])
    })

    // Create footer
    const footer = [
      [''],
      ['=== INFORMASI TAMBAHAN ==='],
      [''],
      ['Status Kehadiran:'],
      ['HADIR_LENGKAP', 'Masuk tepat waktu dan pulang normal'],
      ['TERLAMBAT', 'Masuk terlambat'],
      ['SAKIT', 'Tidak masuk karena sakit'],
      ['IJIN', 'Tidak masuk dengan ijin resmi'],
      ['TIDAK_HADIR', 'Tidak masuk tanpa keterangan'],
      [''],
      ['Catatan Penting:'],
      ['1. Data kehadiran diambil dari sistem absensi digital'],
      ['2. Waktu absen menggunakan timezone WITA (UTC+8)'],
      ['3. Siswa wajib absen masuk dan pulang setiap hari'],
      ['4. Keterlambatan dicatat secara otomatis'],
      [''],
      [`Dicetak pada: ${exportDate}`],
      [`Sistem Absensi Sekolah - ${clientConfig.school.name}`],
    ]

    // Combine all content
    return [
      ...metadata.map(row => row.map(cell => escapeCsvValue(cell)).join(',')),
      headers.map(header => escapeCsvValue(header)).join(','),
      ...rows.map(row => row.join(',')),
      ...footer.map(row => row.map(cell => escapeCsvValue(cell)).join(',')),
    ].join('\n')
  }

  // Helper function to download CSV
  const downloadCsv = (csvContent: string, filename: string) => {
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  const handleDeleteClick = (
    uniqueCode: string,
    name: string,
    type: AttendanceType,
    recordDate?: string | null
  ) => {
    // Get the date from the record if available, otherwise use the selected filter
    let dateStr = ''

    if (recordDate && typeof recordDate === 'string') {
      try {
        // When in weekly view, the recordDate might be in format like "13 Mei 2025"
        // We need to extract the correct ISO date
        console.log(`Processing recordDate for deletion: ${recordDate}`)

        // Try to normalize the date to YYYY-MM-DD format
        dateStr = normalizeDate(recordDate)
      } catch (error) {
        console.error(`Error parsing date: ${recordDate}`, error)
        // Fallback to current date in WITA
        dateStr = getCurrentWITATime().toISOString().split('T')[0]
      }
    } else if (date === 'today') {
      dateStr = getCurrentWITATime().toISOString().split('T')[0] // YYYY-MM-DD
    } else if (date === 'yesterday') {
      const yesterday = getCurrentWITATime()
      yesterday.setDate(yesterday.getDate() - 1)
      dateStr = yesterday.toISOString().split('T')[0]
    } else if (date === 'week') {
      // For week filter, we need to know which specific day the attendance record is for
      // Default to today's date if we don't have the actual date
      dateStr = getCurrentWITATime().toISOString().split('T')[0]
    } else {
      // Default to today if we can't determine the date
      dateStr = getCurrentWITATime().toISOString().split('T')[0]
    }

    setSelectedAttendance({
      uniqueCode,
      name,
      type,
      date: dateStr,
    })
    setShowDeleteDialog(true)
  }

  const handleDeleteConfirm = async () => {
    if (!selectedAttendance) return

    try {
      setDeleteLoading(true)

      const response = await fetch('/api/absence/delete', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          uniqueCode: selectedAttendance.uniqueCode,
          type: selectedAttendance.type,
          date: selectedAttendance.date,
        }),
      })

      // Check for error responses
      if (!response.ok) {
        const errorData = await response.json()

        // Handle specific error cases
        if (response.status === 404) {
          // For 404 errors (record not found), close the dialog and refresh data
          setShowDeleteDialog(false)

          // Notify user with a less alarming message
          toast({
            title: 'Data tidak ditemukan',
            description:
              'Data absen yang ingin dihapus tidak ditemukan di database. Menyegarkan tampilan data.',
          })

          // Refresh the data to update UI
          await fetchReports(true)
          return
        } else if (response.status === 401) {
          throw new Error('Anda tidak memiliki izin untuk menghapus data absen.')
        } else {
          throw new Error(errorData.error || 'Gagal menghapus data absen')
        }
      }

      // Close the dialog
      setShowDeleteDialog(false)

      // Show success message
      toast({
        title: 'Data absen dihapus',
        description: `Data absen ${getAttendanceTypeName(selectedAttendance.type)} untuk ${selectedAttendance.name} telah dihapus.`,
      })

      // Force a refresh of the data after successful deletion
      try {
        // Add a small delay to ensure the backend has time to process the deletion
        await new Promise(resolve => setTimeout(resolve, 500))

        // Clear the local state while waiting for fresh data
        setReports(prevReports =>
          prevReports.filter(report => {
            // For Zuhr attendance
            if (selectedAttendance.type === AttendanceType.ZUHR) {
              if (report.uniqueCode === selectedAttendance.uniqueCode) {
                return {
                  ...report,
                  zuhr: false,
                  zuhrTime: null,
                }
              }
            }
            // For Asr attendance
            else if (selectedAttendance.type === AttendanceType.ASR) {
              if (report.uniqueCode === selectedAttendance.uniqueCode) {
                return {
                  ...report,
                  asr: false,
                  asrTime: null,
                }
              }
            }
            // For Dismissal attendance
            else if (selectedAttendance.type === AttendanceType.DISMISSAL) {
              if (report.uniqueCode === selectedAttendance.uniqueCode) {
                return {
                  ...report,
                  dismissal: false,
                  dismissalTime: null,
                }
              }
            }
            return report
          })
        )

        // Force a refresh of the data with cache-busting
        await fetchReports(true)
      } catch (refreshError) {
        console.error('Error refreshing data:', refreshError)
      }
    } catch (error) {
      console.error('Error deleting attendance:', error)

      // Handle error message
      let errorMessage = 'Gagal menghapus data absen'
      if (error instanceof Error) {
        errorMessage = error.message || errorMessage
      }

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      })
    } finally {
      setDeleteLoading(false)
    }
  }

  const getAttendanceTypeName = (type: AttendanceType) => {
    switch (type) {
      case AttendanceType.ZUHR:
        return 'Shalat Zuhur'
      case AttendanceType.ASR:
        return 'Shalat Asr'
      case AttendanceType.DISMISSAL:
        return 'Pulang'
      default:
        return type
    }
  }

  // Helper function to convert month abbreviation to month number
  const getMonthNumber = (monthAbbr: string): string => {
    const months: Record<string, string> = {
      Jan: '01',
      Feb: '02',
      Mar: '03',
      Apr: '04',
      Mei: '05',
      Jun: '06',
      Jul: '07',
      Agu: '08',
      Sep: '09',
      Okt: '10',
      Nov: '11',
      Des: '12',
      // English month abbreviations for fallback
      May: '05',
      Aug: '08',
      Oct: '10',
      Dec: '12',
    }

    return months[monthAbbr] || '01' // Default to January if not found
  }

  // Helper function to escape CSV values
  const escapeCsvValue = (value: string | number | null | undefined): string => {
    if (value === null || value === undefined) return '""'
    const stringValue = String(value)
    // If the value contains quotes or commas, wrap in quotes and escape internal quotes
    if (stringValue.includes('"') || stringValue.includes(',') || stringValue.includes('\n')) {
      return `"${stringValue.replace(/"/g, '""')}"`
    }
    return stringValue
  }

  // Get readable date string for the current filter
  const getReadableDateString = (): string => {
    if (date === 'today') {
      return getCurrentWITATime().toLocaleDateString('id-ID', {
        day: '2-digit',
        month: 'long',
        year: 'numeric',
        timeZone: 'Asia/Makassar', // WITA timezone
      })
    } else if (date === 'yesterday') {
      const yesterday = getCurrentWITATime()
      yesterday.setDate(yesterday.getDate() - 1)
      return yesterday.toLocaleDateString('id-ID', {
        day: '2-digit',
        month: 'long',
        year: 'numeric',
        timeZone: 'Asia/Makassar', // WITA timezone
      })
    } else if (date === 'week') {
      // Calculate the date range for the past week (6 days plus today)
      const today = getCurrentWITATime()

      const endOfWeek = new Date(today)

      const startOfWeek = new Date(today)
      startOfWeek.setDate(startOfWeek.getDate() - 6)

      return `${startOfWeek.toLocaleDateString('id-ID', {
        day: '2-digit',
        month: 'long',
        timeZone: 'Asia/Makassar', // WITA timezone
      })} - ${endOfWeek.toLocaleDateString('id-ID', {
        day: '2-digit',
        month: 'long',
        year: 'numeric',
        timeZone: 'Asia/Makassar', // WITA timezone
      })}`
    }
    return ''
  }

  // Normalize date string to YYYY-MM-DD format
  const normalizeDate = (dateStr: string): string => {
    try {
      // If it's already in YYYY-MM-DD format, return it
      if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
        return dateStr
      }

      // For weekly view, the date might include a comma and time
      // Example: "13 Mei 2025, 08:30" - we need to extract just the date part
      if (dateStr.includes(',')) {
        dateStr = dateStr.split(',')[0].trim()
      }

      // Handle date formats like "13 Mei 2025"
      const parts = dateStr.split(' ')
      if (parts.length === 3) {
        const day = parts[0].padStart(2, '0')
        const month = getMonthNumber(parts[1])
        const year = parts[2]
        return `${year}-${month}-${day}`
      }

      // Default to creating a new date and formatting it
      const date = new Date(dateStr)
      if (!isNaN(date.getTime())) {
        return date.toISOString().split('T')[0]
      }

      // If all else fails, return today's date in WITA
      console.error(`Could not parse date: ${dateStr}, using today's date as fallback`)
      return getCurrentWITATime().toISOString().split('T')[0]
    } catch (error) {
      console.error('Error normalizing date:', error, 'for input:', dateStr)
      return getCurrentWITATime().toISOString().split('T')[0]
    }
  }

  // Compare today's data with weekly data for debugging
  const compareData = () => {
    if (date !== 'week' || !reports.length) return

    console.log('========== WEEKLY DATA COMPARISON ==========')
    console.log(`Total students in weekly view: ${reports.length}`)

    // Analyze distribution of dates in the weekly records
    const dateDistribution = new Map()

    reports.forEach(report => {
      if (report.weeklyRecords) {
        report.weeklyRecords.forEach((record: any) => {
          if (!record.rawDate) return
          const recordDate =
            record.rawDate instanceof Date ? record.rawDate : new Date(record.rawDate)
          const dateKey = recordDate.toISOString().split('T')[0]

          if (!dateDistribution.has(dateKey)) {
            dateDistribution.set(dateKey, 0)
          }
          dateDistribution.set(dateKey, dateDistribution.get(dateKey) + 1)
        })
      }
    })

    console.log('Date distribution in weekly view:')
    const sortedDates = Array.from(dateDistribution.entries())
      .sort((a, b) => b[0].localeCompare(a[0])) // Sort by date descending
      .map(([date, count]) => ({ date, count }))
    console.log(JSON.stringify(sortedDates, null, 2))

    // Create a simple map of students and their attendance status
    const studentSummary = reports.map(report => {
      const todayRecords = report.weeklyRecords?.filter(record => {
        // Check if this record is for today by checking if the date includes today's date
        // Use a more flexible date checking because of timezone issues
        const today = getCurrentWITATime()
        const todayStr = today.getDate().toString().padStart(2, '0')
        const monthStr = today.toLocaleString('id-ID', {
          month: 'short',
          timeZone: 'Asia/Makassar', // WITA timezone
        })
        const yearStr = today.getFullYear().toString()

        // Check if date string contains today's components
        return (
          record.date.includes(todayStr) &&
          record.date.includes(monthStr) &&
          record.date.includes(yearStr)
        )
      })

      return {
        uniqueCode: report.uniqueCode,
        name: report.name,
        className: report.className,
        hasTodayRecord: todayRecords && todayRecords.length > 0,
        todayStatus:
          todayRecords && todayRecords.length > 0
            ? {
                zuhr: todayRecords[0].zuhr,
                asr: todayRecords[0].asr,
                dismissal: todayRecords[0].dismissal,
                date: todayRecords[0].date,
              }
            : 'No data for today',
        weeklySummary: {
          zuhr: report.zuhr,
          asr: report.asr,
          dismissal: report.dismissal,
          recordCount: report.weeklyRecords?.length || 0,
        },
      }
    })

    console.table(studentSummary)
  }

  // Run comparison after reports are loaded
  useEffect(() => {
    if (reports.length > 0 && date === 'week') {
      compareData()
    }
  }, [reports, date])

  // Handle actual export function
  const handleExport = () => {
    try {
      setIsExporting(true)

      // Generate report for current day view
      const reportDate = getReadableDateString()
      const csvContent = generateCsvForReports(filteredReports, `Laporan Harian: ${reportDate}`)

      // Create filename based on date filter and class filter
      let dateStr = ''
      if (date === 'today') {
        const today = getCurrentWITATime()
        dateStr = today.toISOString().split('T')[0]
      } else if (date === 'yesterday') {
        const yesterday = getCurrentWITATime()
        yesterday.setDate(yesterday.getDate() - 1)
        dateStr = yesterday.toISOString().split('T')[0]
      } else if (date === 'week') {
        const today = getCurrentWITATime()
        dateStr = 'week-' + today.toISOString().split('T')[0]
      }

      const classStr =
        classFilter === 'all' ? 'semua-kelas' : classFilter.replace(/\s+/g, '-').toLowerCase()

      // Download the CSV
      downloadCsv(csvContent, `laporan-absensi-${dateStr}-${classStr}.csv`)

      toast({
        title: 'Laporan diekspor',
        description: 'File CSV telah berhasil diunduh',
      })
    } catch (error) {
      console.error('Error exporting CSV:', error)
      toast({
        title: 'Gagal mengekspor',
        description: 'Terjadi kesalahan saat mengekspor data',
        variant: 'destructive',
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Tambahkan state untuk menyimpan statistik mingguan (di bawah deklarasi state)
  const [weeklyStats, setWeeklyStats] = useState<{
    dayCount: number
    dayStats: Record<string, DayStats>
    sortedDayStats?: DayStats[] // Tambahkan array yang sudah diurutkan
    avgZuhrPercent: number
    avgAsrPercent: number
    avgDismissalPercent: number
    avgIjinPercent: number
  }>({
    dayCount: 0,
    dayStats: {},
    sortedDayStats: [],
    avgZuhrPercent: 0,
    avgAsrPercent: 0,
    avgDismissalPercent: 0,
    avgIjinPercent: 0,
  })

  // Tambahkan fungsi untuk mendapatkan nama hari singkat (di bawah fungsi getReadableDateString)
  function getShortDayName(day: string, month: string, year: string): string {
    try {
      // Konversi bulan dari nama singkat ke angka
      const monthMap: Record<string, number> = {
        Jan: 0,
        Feb: 1,
        Mar: 2,
        Apr: 3,
        Mei: 4,
        Jun: 5,
        Jul: 6,
        Agu: 7,
        Sep: 8,
        Okt: 9,
        Nov: 10,
        Des: 11,
      }

      const monthNum = monthMap[month] || 0

      // Pastikan parameter adalah valid
      if (!day || !year || isNaN(parseInt(day)) || isNaN(parseInt(year))) {
        return ''
      }

      const dateObj = new Date(parseInt(year), monthNum, parseInt(day))

      // Validasi date object yang dihasilkan
      if (isNaN(dateObj.getTime())) {
        return ''
      }

      // Dapatkan nama hari dalam bahasa Indonesia
      const dayNames = ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab']
      return dayNames[dateObj.getDay()]
    } catch (error) {
      console.error('Error getting day name:', error)
      return ''
    }
  }

  return (
    <div className="min-h-screen bg-slate-50 pb-16 dark:bg-slate-900">
      <header className="sticky top-0 z-10 border-b bg-white/80 p-4 backdrop-blur-md dark:border-slate-700 dark:bg-slate-900/80">
        <div className="container mx-auto flex items-center justify-between">
          <h1 className="text-xl font-bold text-slate-800 dark:text-slate-100">Laporan Absensi</h1>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="icon"
              onClick={() => fetchReports(true)}
              disabled={isLoading}
              title="Refresh Data"
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
            <ThemeToggle />
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-6">
        {/* Report Type Tabs - Role-based visibility */}
        {(canAccessPrayerReports || canAccessSchoolReports) && (
          <Tabs
            value={reportType}
            onValueChange={v => setReportType(v as 'prayer' | 'school')}
            className="mb-6"
          >
            <TabsList className="mx-auto grid w-full max-w-md grid-cols-2">
              {canAccessPrayerReports && (
                <TabsTrigger value="prayer" className="flex items-center">
                  <span className="mr-2">🕌</span>
                  <span>Laporan Shalat</span>
                </TabsTrigger>
              )}
              {canAccessSchoolReports && (
                <TabsTrigger value="school" className="flex items-center">
                  <span className="mr-2">🏫</span>
                  <span>Absensi Sekolah</span>
                </TabsTrigger>
              )}
            </TabsList>
          </Tabs>
        )}

        {/* Tabs for Calendar View Selection */}
        <Tabs
          value={calendarView}
          onValueChange={v => setCalendarView(v as 'day' | 'month' | 'year')}
          className="mb-6"
        >
          <TabsList className="mx-auto grid w-full max-w-md grid-cols-3">
            <TabsTrigger value="day" className="flex items-center">
              <Calendar className="mr-2 h-4 w-4" />
              <span>Harian</span>
            </TabsTrigger>
            <TabsTrigger value="month" className="flex items-center">
              <CalendarDays className="mr-2 h-4 w-4" />
              <span>Bulanan</span>
            </TabsTrigger>
            <TabsTrigger value="year" className="flex items-center">
              <CalendarRange className="mr-2 h-4 w-4" />
              <span>Tahunan</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="day" className="mt-4">
            {/* Attendance Statistics Summary */}
            <div className="mb-6">
              <h2 className="mb-3 text-lg font-medium text-slate-800 dark:text-slate-200">
                Ringkasan Kehadiran {date === 'week' ? '7 Hari Terakhir' : ''}
              </h2>
              {date === 'week' ? (
                <p className="mb-3 text-xs text-slate-500 dark:text-slate-400">
                  Menampilkan statistik kehadiran agregat selama 7 hari terakhir ({stats.total}{' '}
                  siswa terdaftar dalam filter). Persentase kehadiran Shalat (Zuhur & Asr) dihitung
                  sebagai: (Total kejadian hadir Shalat / ((Total Siswa x 7 hari) - Total kejadian
                  ijin)) * 100%. Persentase Pulang dihitung sebagai: (Total kejadian Pulang / (Total
                  Siswa x 7 hari)) * 100%. Persentase Ijin dihitung sebagai: (Total kejadian Ijin /
                  (Total Siswa x 7 hari)) * 100%.
                </p>
              ) : (
                <p className="mb-3 text-xs text-slate-500 dark:text-slate-400">
                  Persentase kehadiran Shalat Zuhur dan Asr dihitung berdasarkan jumlah siswa yang
                  hadir dibagi dengan jumlah siswa yang wajib hadir (total siswa dikurangi siswa
                  dengan status Ijin).
                </p>
              )}
              {date === 'week' && (
                <p className="mb-3 text-xs text-slate-500 dark:text-slate-400">
                  <strong>Ijin:</strong> Menampilkan jumlah siswa dengan status ijin selama periode
                  ini. Siswa dengan status ijin dianggap hadir untuk Zuhur dan Asr, namun tetap
                  diwajibkan untuk melakukan absen pulang.
                </p>
              )}
              <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-4">
                {/* Zuhr Statistics */}
                <Card className="overflow-hidden border-indigo-200 shadow-sm transition-shadow hover:shadow-md dark:border-slate-700">
                  <CardHeader className="bg-indigo-50 pb-2 dark:bg-indigo-900/20">
                    <CardTitle className="text-sm font-medium text-indigo-700 dark:text-indigo-300">
                      Shalat Zuhur
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="mr-2 rounded-full bg-indigo-100 p-2 dark:bg-indigo-900/30">
                          <Users className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                        </div>
                        <div>
                          <p className="text-2xl font-bold text-slate-800 dark:text-slate-200">
                            {stats.zuhr}
                            <span className="ml-1 text-sm font-medium text-slate-500">
                              {date === 'week'
                                ? `/ ${stats.effectiveStudentDays || 0}`
                                : `/ ${stats.total - stats.ijin}`}
                            </span>
                          </p>
                          {date === 'week' && (
                            <>
                              <p className="text-xs text-slate-500">
                                Total Hadir / Potensi (setelah ijin)
                              </p>
                            </>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-xl font-bold text-indigo-600 dark:text-indigo-400">
                          {date === 'week'
                            ? Math.round(stats.zuhrPercentage || 0) // Menggunakan stats.zuhrPercentage
                            : stats.total > 0
                              ? Math.round((stats.zuhr / (stats.total - stats.ijin || 1)) * 100)
                              : 0}
                          %
                        </p>
                        {date === 'week' && (
                          <p className="text-xs text-slate-500">Persentase Mingguan</p>
                        )}
                      </div>
                    </div>
                    <ColoredProgress
                      value={
                        date === 'week'
                          ? stats.zuhrPercentage || 0
                          : stats.total > 0
                            ? (stats.zuhr / (stats.total - stats.ijin || 1)) * 100
                            : 0
                      }
                      className="mt-2 h-2 bg-slate-200 dark:bg-slate-700"
                      indicatorColor="bg-indigo-500 dark:bg-indigo-400"
                    />
                  </CardContent>
                </Card>

                {/* Asr Statistics */}
                <Card className="overflow-hidden border-emerald-200 shadow-sm transition-shadow hover:shadow-md dark:border-slate-700">
                  <CardHeader className="bg-emerald-50 pb-2 dark:bg-emerald-900/20">
                    <CardTitle className="text-sm font-medium text-emerald-700 dark:text-emerald-300">
                      Shalat Asr
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="mr-2 rounded-full bg-emerald-100 p-2 dark:bg-emerald-900/30">
                          <Users className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
                        </div>
                        <div>
                          <p className="text-2xl font-bold text-slate-800 dark:text-slate-200">
                            {stats.asr}
                            <span className="ml-1 text-sm font-medium text-slate-500">
                              {date === 'week'
                                ? `/ ${stats.effectiveStudentDays || 0}`
                                : `/ ${stats.total - stats.ijin}`}
                            </span>
                          </p>
                          {date === 'week' && (
                            <p className="text-xs text-slate-500">
                              Total Hadir / Potensi (setelah ijin)
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-xl font-bold text-emerald-600 dark:text-emerald-400">
                          {date === 'week'
                            ? Math.round(stats.asrPercentage || 0) // Menggunakan stats.asrPercentage
                            : stats.total > 0
                              ? Math.round((stats.asr / (stats.total - stats.ijin || 1)) * 100)
                              : 0}
                          %
                        </p>
                        {date === 'week' && (
                          <p className="text-xs text-slate-500">Persentase Mingguan</p>
                        )}
                      </div>
                    </div>
                    <ColoredProgress
                      value={
                        date === 'week'
                          ? stats.asrPercentage || 0
                          : stats.total > 0
                            ? (stats.asr / (stats.total - stats.ijin || 1)) * 100
                            : 0
                      }
                      className="mt-2 h-2 bg-slate-200 dark:bg-slate-700"
                      indicatorColor="bg-emerald-500 dark:bg-emerald-400"
                    />
                  </CardContent>
                </Card>

                {/* Dismissal Statistics */}
                <Card className="overflow-hidden border-amber-200 shadow-sm transition-shadow hover:shadow-md dark:border-slate-700">
                  <CardHeader className="bg-amber-50 pb-2 dark:bg-amber-900/20">
                    <CardTitle className="text-sm font-medium text-amber-700 dark:text-amber-300">
                      Absen Pulang
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="mr-2 rounded-full bg-amber-100 p-2 dark:bg-amber-900/30">
                          <Users className="h-5 w-5 text-amber-600 dark:text-amber-400" />
                        </div>
                        <div>
                          <p className="text-2xl font-bold text-slate-800 dark:text-slate-200">
                            {stats.dismissal}
                            <span className="ml-1 text-sm font-medium text-slate-500">
                              {date === 'week'
                                ? `/ ${stats.totalPossibleDays || 0}`
                                : `/ ${stats.total}`}
                            </span>
                          </p>
                          {date === 'week' && (
                            <p className="text-xs text-slate-500">Total Pulang / Potensi Hadir</p>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-xl font-bold text-amber-600 dark:text-amber-400">
                          {date === 'week'
                            ? Math.round(stats.dismissalPercentage || 0) // Menggunakan stats.dismissalPercentage
                            : stats.total > 0
                              ? Math.round((stats.dismissal / stats.total) * 100)
                              : 0}
                          %
                        </p>
                        {date === 'week' && (
                          <p className="text-xs text-slate-500">Persentase Mingguan</p>
                        )}
                      </div>
                    </div>
                    <ColoredProgress
                      value={
                        date === 'week'
                          ? stats.dismissalPercentage || 0
                          : stats.total > 0
                            ? (stats.dismissal / stats.total) * 100
                            : 0
                      }
                      className="mt-2 h-2 bg-slate-200 dark:bg-slate-700"
                      indicatorColor="bg-amber-500 dark:bg-amber-400"
                    />
                  </CardContent>
                </Card>

                {/* Ijin Statistics */}
                <Card className="overflow-hidden border-purple-200 shadow-sm transition-shadow hover:shadow-md dark:border-slate-700">
                  <CardHeader className="bg-purple-50 pb-2 dark:bg-purple-900/20">
                    <CardTitle className="text-sm font-medium text-purple-700 dark:text-purple-300">
                      Ijin
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      {' '}
                      {/* Tetap justify-between untuk layout utama */}
                      <div className="flex items-center">
                        <div className="mr-2 rounded-full bg-purple-100 p-2 dark:bg-purple-900/30">
                          <Users className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                        </div>
                        <div>
                          <p className="text-2xl font-bold text-slate-800 dark:text-slate-200">
                            {stats.ijin}
                          </p>
                          <p className="text-xs text-slate-500">
                            {date === 'week'
                              ? 'Total Kejadian Ijin (7 Hari)'
                              : 'Total Kejadian Ijin'}
                          </p>
                        </div>
                      </div>
                      {/* Tidak ada lagi blok text-right untuk persentase */}
                    </div>
                    {/* Tidak ada lagi ColoredProgress bar */}
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Replace the grid layout with a sequential layout */}
            {/* Filter Card - Moved to the top */}
            <Card className="mb-6 border-indigo-200 shadow-sm transition-shadow hover:shadow-md dark:border-slate-700">
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Filter Data</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 p-4">
                <div className="grid gap-4 sm:grid-cols-3">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      Tanggal
                    </label>
                    <Select
                      value={date}
                      onValueChange={value => {
                        setDate(value)
                        setLastActiveFilter(prev => ({ ...prev, date: value }))
                      }}
                    >
                      <SelectTrigger className="border-slate-300 dark:border-slate-600">
                        <SelectValue placeholder="Pilih tanggal" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="today">
                          Hari Ini (
                          {getCurrentWITATime().toLocaleDateString('id-ID', {
                            day: '2-digit',
                            month: 'short',
                            year: 'numeric',
                            timeZone: 'Asia/Makassar', // WITA timezone
                          })}
                          )
                        </SelectItem>
                        <SelectItem value="yesterday">
                          Kemarin (
                          {(() => {
                            const yesterday = getCurrentWITATime()
                            yesterday.setDate(yesterday.getDate() - 1)
                            return yesterday.toLocaleDateString('id-ID', {
                              day: '2-digit',
                              month: 'short',
                              year: 'numeric',
                              timeZone: 'Asia/Makassar', // WITA timezone
                            })
                          })()}
                          )
                        </SelectItem>
                        <SelectItem value="week">7 Hari Terakhir (Termasuk Hari Ini)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      Kelas
                    </label>
                    <Select
                      value={classFilter}
                      onValueChange={value => {
                        setClassFilter(value)
                        setLastActiveFilter(prev => ({ ...prev, classFilter: value }))
                      }}
                    >
                      <SelectTrigger className="border-slate-300 dark:border-slate-600">
                        <SelectValue placeholder="Pilih kelas" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Semua Kelas</SelectItem>
                        {classes.map(cls => (
                          <SelectItem key={cls.id} value={cls.name}>
                            {cls.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      Cari
                    </label>
                    <div className="relative">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-slate-500" />
                      <Input
                        placeholder="Cari nama, kelas atau kode unik"
                        className="border-slate-300 pl-10 pr-10 dark:border-slate-600"
                        value={searchQuery}
                        onChange={e => setSearchQuery(e.target.value)}
                      />
                      {searchQuery && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="absolute right-1 top-1 h-8 w-8 hover:bg-slate-200 dark:hover:bg-slate-700"
                          onClick={handleClearSearch}
                          title="Clear search"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                    {debouncedSearchQuery && (
                      <div className="flex items-center justify-between text-xs text-slate-500 dark:text-slate-400">
                        <span>
                          Hasil pencarian: <strong>{filteredReports.length}</strong> dari{' '}
                          {reports.length} siswa
                        </span>
                        {filteredReports.length === 0 && (
                          <span className="text-amber-500 dark:text-amber-400">
                            Tidak ada hasil
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                <Button
                  onClick={handleExport}
                  className="w-full bg-teal-500 text-white hover:bg-teal-600"
                  disabled={isExporting || filteredReports.length === 0}
                >
                  {isExporting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Mengekspor...
                    </>
                  ) : (
                    <>
                      <Download className="mr-2 h-4 w-4" />
                      Export CSV {filteredReports.length > 0 ? `(${filteredReports.length})` : ''}
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* Table section */}
            <div className="overflow-x-auto">
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
                  <span className="ml-2 text-slate-500">Memuat data...</span>
                </div>
              ) : filteredReports.length === 0 ? (
                <div className="rounded-lg border border-dashed border-slate-300 bg-white p-8 text-center dark:border-slate-600 dark:bg-slate-800">
                  <Search className="mx-auto h-10 w-10 text-slate-400" />
                  <h3 className="mt-4 text-lg font-medium text-slate-900 dark:text-slate-200">
                    {debouncedSearchQuery
                      ? `Tidak ada data yang sesuai dengan pencarian "${debouncedSearchQuery}"`
                      : 'Tidak ada data absensi'}
                  </h3>
                  <p className="mt-2 text-sm text-slate-500 dark:text-slate-400">
                    {debouncedSearchQuery
                      ? 'Coba dengan kata kunci lain atau ubah filter tanggal/kelas'
                      : 'Tidak ada data absensi untuk filter yang dipilih'}
                  </p>
                  {debouncedSearchQuery && (
                    <Button variant="outline" className="mt-4" onClick={handleClearSearch}>
                      <X className="mr-2 h-4 w-4" />
                      Hapus Pencarian
                    </Button>
                  )}
                </div>
              ) : (
                <div>
                  {date === 'week' && (
                    <div className="mb-4 rounded-lg border border-indigo-200 bg-indigo-50 p-4 dark:border-indigo-900 dark:bg-indigo-900/20">
                      <h3 className="mb-2 text-sm font-semibold text-indigo-700 dark:text-indigo-300">
                        Tampilan Mingguan: {getReadableDateString()}
                      </h3>
                      <p className="text-xs text-indigo-600 dark:text-indigo-400">
                        Laporan ini menampilkan kehadiran siswa selama 7 hari terakhir. Data
                        dikelompokkan per siswa dengan detail kehadiran untuk setiap hari.
                      </p>
                      <div className="mt-3 grid grid-cols-1 gap-2 text-xs sm:grid-cols-2">
                        <div className="rounded-md bg-white p-2 shadow-sm dark:bg-slate-800">
                          <h4 className="font-medium text-slate-700 dark:text-slate-200">
                            Cara Membaca Data:
                          </h4>
                          <ul className="mt-1 list-inside list-disc space-y-1 text-slate-600 dark:text-slate-400">
                            <li>Setiap baris menunjukkan satu hari dalam seminggu</li>
                            <li>Urutan: hari ini sampai 7 hari ke belakang</li>
                            <li>Warna hijau menandakan kehadiran</li>
                            <li>Warna merah menandakan ketidakhadiran</li>
                            <li>
                              <strong>Semua 7 hari ditampilkan</strong>, hari tanpa data ditampilkan
                              dengan latar belakang abu-abu
                            </li>
                          </ul>
                        </div>
                        <div className="rounded-md bg-white p-2 shadow-sm dark:bg-slate-800">
                          <h4 className="font-medium text-slate-700 dark:text-slate-200">
                            Interaksi:
                          </h4>
                          <ul className="mt-1 list-inside list-disc space-y-1 text-slate-600 dark:text-slate-400">
                            <li>Arahkan kursor ke baris untuk melihat opsi hapus</li>
                            <li>Klik ikon sampah untuk menghapus absensi</li>
                            <li>Filter berdasarkan kelas di panel atas</li>
                            <li>Gunakan fitur pencarian untuk menemukan siswa</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  )}

                  {debouncedSearchQuery && (
                    <div className="mb-4 flex items-center rounded-md bg-indigo-50 px-4 py-2 text-sm text-indigo-700 dark:bg-indigo-950 dark:text-indigo-300">
                      <Search className="mr-2 h-4 w-4" />
                      <span>
                        Menampilkan <strong>{filteredReports.length}</strong> hasil untuk pencarian
                        "<span className="font-medium">{debouncedSearchQuery}</span>"
                      </span>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="ml-auto h-7 text-xs hover:bg-indigo-100 hover:text-indigo-800 dark:hover:bg-indigo-900 dark:hover:text-indigo-200"
                        onClick={handleClearSearch}
                      >
                        <X className="mr-1 h-3 w-3" />
                        Hapus Filter
                      </Button>
                    </div>
                  )}
                  <Table>
                    {date === 'week' ? (
                      <TableHeader className="bg-slate-50 dark:bg-slate-800/50">
                        <TableRow>
                          <TableHead className="w-[120px]">Kode Unik</TableHead>
                          <TableHead>Nama</TableHead>
                          <TableHead>Kelas</TableHead>
                          {reportType === 'prayer' ? (
                            <>
                              <TableHead>Kehadiran Zuhur</TableHead>
                              <TableHead>Kehadiran Asr</TableHead>
                              <TableHead>Kehadiran Pulang</TableHead>
                              <TableHead>Status Ijin</TableHead>
                            </>
                          ) : (
                            <>
                              <TableHead>Kehadiran Masuk</TableHead>
                              <TableHead>Kehadiran Terlambat</TableHead>
                              <TableHead>Status Sakit</TableHead>
                              <TableHead>Ijin Sementara</TableHead>
                              <TableHead>Kembali Ijin</TableHead>
                              <TableHead>Kehadiran Pulang</TableHead>
                            </>
                          )}
                        </TableRow>
                      </TableHeader>
                    ) : (
                      <TableHeader>
                        <TableRow>
                          <TableHead>Kode Unik</TableHead>
                          <TableHead>Nama</TableHead>
                          <TableHead>Kelas</TableHead>
                          {reportType === 'prayer' ? (
                            <>
                              <TableHead>Zuhur</TableHead>
                              <TableHead>Asr</TableHead>
                              <TableHead>Pulang</TableHead>
                              <TableHead>Ijin</TableHead>
                            </>
                          ) : (
                            <>
                              <TableHead>Masuk</TableHead>
                              <TableHead>Terlambat</TableHead>
                              <TableHead>Sakit</TableHead>
                              <TableHead>Ijin Sementara</TableHead>
                              <TableHead>Kembali Ijin</TableHead>
                              <TableHead>Pulang</TableHead>
                            </>
                          )}
                        </TableRow>
                      </TableHeader>
                    )}
                    <TableBody>
                      {filteredReports.map((report, index) => (
                        <TableRow
                          key={index}
                          className="hover:bg-indigo-50 dark:hover:bg-slate-800"
                        >
                          <TableCell>{report.uniqueCode.substring(0, 8)}...</TableCell>
                          <TableCell>{report.name}</TableCell>
                          <TableCell>{report.className}</TableCell>

                          {/* Render cells based on report type */}
                          {reportType === 'prayer' ? (
                            <>
                              {/* Zuhr Cell */}
                              <TableCell>
                                {report.zuhr ? (
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center">
                                      <div className="mr-2 h-2 w-2 rounded-full bg-green-500"></div>
                                      <span className="text-xs italic text-slate-500 dark:text-slate-400">
                                        {report.zuhrTime}
                                      </span>
                                    </div>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="h-6 w-6 text-red-500 hover:bg-red-100 hover:text-red-700 dark:hover:bg-red-900/30"
                                      onClick={() =>
                                        handleDeleteClick(
                                          report.uniqueCode,
                                          report.name,
                                          AttendanceType.ZUHR,
                                          report.zuhrTime
                                        )
                                      }
                                    >
                                      <Trash2 className="h-3 w-3" />
                                    </Button>
                                  </div>
                                ) : (
                                  <div className="flex items-center">
                                    <div className="mr-2 h-2 w-2 rounded-full bg-red-500"></div>
                                    <span className="text-xs text-red-500 dark:text-red-400">
                                      Tidak hadir
                                    </span>
                                  </div>
                                )}
                              </TableCell>

                              {/* Asr Cell */}
                              <TableCell>
                                {report.asr ? (
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center">
                                      <div className="mr-2 h-2 w-2 rounded-full bg-green-500"></div>
                                      <span className="text-xs italic text-slate-500 dark:text-slate-400">
                                        {report.asrTime}
                                      </span>
                                    </div>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="h-6 w-6 text-red-500 hover:bg-red-100 hover:text-red-700 dark:hover:bg-red-900/30"
                                      onClick={() =>
                                        handleDeleteClick(
                                          report.uniqueCode,
                                          report.name,
                                          AttendanceType.ASR,
                                          report.asrTime
                                        )
                                      }
                                    >
                                      <Trash2 className="h-3 w-3" />
                                    </Button>
                                  </div>
                                ) : (
                                  <div className="flex items-center">
                                    <div className="mr-2 h-2 w-2 rounded-full bg-red-500"></div>
                                    <span className="text-xs text-red-500 dark:text-red-400">
                                      Tidak hadir
                                    </span>
                                  </div>
                                )}
                              </TableCell>

                              {/* Pulang Cell */}
                              <TableCell>
                                {report.dismissal ? (
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center">
                                      <div className="mr-2 h-2 w-2 rounded-full bg-green-500"></div>
                                      <span className="text-xs italic text-slate-500 dark:text-slate-400">
                                        {report.dismissalTime}
                                      </span>
                                    </div>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="h-6 w-6 text-red-500 hover:bg-red-100 hover:text-red-700 dark:hover:bg-red-900/30"
                                      onClick={() =>
                                        handleDeleteClick(
                                          report.uniqueCode,
                                          report.name,
                                          AttendanceType.DISMISSAL,
                                          report.dismissalTime
                                        )
                                      }
                                    >
                                      <Trash2 className="h-3 w-3" />
                                    </Button>
                                  </div>
                                ) : (
                                  <div className="flex items-center">
                                    <div className="mr-2 h-2 w-2 rounded-full bg-red-500"></div>
                                    <span className="text-xs text-red-500 dark:text-red-400">
                                      Tidak hadir
                                    </span>
                                  </div>
                                )}
                              </TableCell>

                              {/* Ijin Cell */}
                              <TableCell>
                                {report.ijin ? (
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center">
                                      <div className="mr-2 h-2 w-2 rounded-full bg-green-500"></div>
                                      <span className="text-xs italic text-slate-500 dark:text-slate-400">
                                        Iya
                                      </span>
                                    </div>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="h-6 w-6 text-red-500 hover:bg-red-100 hover:text-red-700 dark:hover:bg-red-900/30"
                                      onClick={() =>
                                        handleDeleteClick(
                                          report.uniqueCode,
                                          report.name,
                                          AttendanceType.IJIN,
                                          report.ijinTime
                                        )
                                      }
                                    >
                                      <Trash2 className="h-3 w-3" />
                                    </Button>
                                  </div>
                                ) : (
                                  <div className="flex items-center">
                                    <div className="mr-2 h-2 w-2 rounded-full bg-red-500"></div>
                                    <span className="text-xs text-red-500 dark:text-red-400">
                                      Tidak
                                    </span>
                                  </div>
                                )}
                              </TableCell>
                            </>
                          ) : (
                            <>
                              {/* School Attendance Cells */}
                              {/* Entry Cell */}
                              <TableCell>
                                {(report as any).entry ? (
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center">
                                      <div className="mr-2 h-2 w-2 rounded-full bg-green-500"></div>
                                      <span className="text-xs italic text-slate-500 dark:text-slate-400">
                                        {(report as any).entryTime}
                                      </span>
                                    </div>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="h-6 w-6 text-red-500 hover:bg-red-100 hover:text-red-700 dark:hover:bg-red-900/30"
                                      onClick={() =>
                                        handleDeleteClick(
                                          report.uniqueCode,
                                          report.name,
                                          AttendanceType.ENTRY,
                                          (report as any).entryTime
                                        )
                                      }
                                    >
                                      <Trash2 className="h-3 w-3" />
                                    </Button>
                                  </div>
                                ) : (
                                  <div className="flex items-center">
                                    <div className="mr-2 h-2 w-2 rounded-full bg-red-500"></div>
                                    <span className="text-xs text-red-500 dark:text-red-400">
                                      Tidak masuk
                                    </span>
                                  </div>
                                )}
                              </TableCell>

                              {/* Late Entry Cell */}
                              <TableCell>
                                {(report as any).lateEntry ? (
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center">
                                      <div className="mr-2 h-2 w-2 rounded-full bg-yellow-500"></div>
                                      <span className="text-xs italic text-slate-500 dark:text-slate-400">
                                        {(report as any).lateEntryTime}
                                      </span>
                                    </div>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="h-6 w-6 text-red-500 hover:bg-red-100 hover:text-red-700 dark:hover:bg-red-900/30"
                                      onClick={() =>
                                        handleDeleteClick(
                                          report.uniqueCode,
                                          report.name,
                                          AttendanceType.LATE_ENTRY,
                                          (report as any).lateEntryTime
                                        )
                                      }
                                    >
                                      <Trash2 className="h-3 w-3" />
                                    </Button>
                                  </div>
                                ) : (
                                  <div className="flex items-center">
                                    <div className="mr-2 h-2 w-2 rounded-full bg-green-500"></div>
                                    <span className="text-xs text-green-500 dark:text-green-400">
                                      Tepat waktu
                                    </span>
                                  </div>
                                )}
                              </TableCell>

                              {/* Sick Cell */}
                              <TableCell>
                                {(report as any).sick ? (
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center">
                                      <div className="mr-2 h-2 w-2 rounded-full bg-orange-500"></div>
                                      <span className="text-xs italic text-slate-500 dark:text-slate-400">
                                        {(report as any).sickTime}
                                      </span>
                                    </div>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="h-6 w-6 text-red-500 hover:bg-red-100 hover:text-red-700 dark:hover:bg-red-900/30"
                                      onClick={() =>
                                        handleDeleteClick(
                                          report.uniqueCode,
                                          report.name,
                                          AttendanceType.SICK,
                                          (report as any).sickTime
                                        )
                                      }
                                    >
                                      <Trash2 className="h-3 w-3" />
                                    </Button>
                                  </div>
                                ) : (
                                  <div className="flex items-center">
                                    <div className="mr-2 h-2 w-2 rounded-full bg-green-500"></div>
                                    <span className="text-xs text-green-500 dark:text-green-400">
                                      Sehat
                                    </span>
                                  </div>
                                )}
                              </TableCell>

                              {/* Temporary Leave Cell */}
                              <TableCell>
                                {(report as any).temporaryLeave ? (
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center">
                                      <div className="mr-2 h-2 w-2 rounded-full bg-blue-500"></div>
                                      <span className="text-xs italic text-slate-500 dark:text-slate-400">
                                        {(report as any).temporaryLeaveTime}
                                      </span>
                                    </div>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="h-6 w-6 text-red-500 hover:bg-red-100 hover:text-red-700 dark:hover:bg-red-900/30"
                                      onClick={() =>
                                        handleDeleteClick(
                                          report.uniqueCode,
                                          report.name,
                                          AttendanceType.TEMPORARY_LEAVE,
                                          (report as any).temporaryLeaveTime
                                        )
                                      }
                                    >
                                      <Trash2 className="h-3 w-3" />
                                    </Button>
                                  </div>
                                ) : (
                                  <div className="flex items-center">
                                    <div className="mr-2 h-2 w-2 rounded-full bg-gray-400"></div>
                                    <span className="text-xs text-gray-500 dark:text-gray-400">
                                      Tidak ijin
                                    </span>
                                  </div>
                                )}
                              </TableCell>

                              {/* Return from Leave Cell */}
                              <TableCell>
                                {(report as any).returnFromLeave ? (
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center">
                                      <div className="mr-2 h-2 w-2 rounded-full bg-purple-500"></div>
                                      <span className="text-xs italic text-slate-500 dark:text-slate-400">
                                        {(report as any).returnFromLeaveTime}
                                      </span>
                                    </div>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="h-6 w-6 text-red-500 hover:bg-red-100 hover:text-red-700 dark:hover:bg-red-900/30"
                                      onClick={() =>
                                        handleDeleteClick(
                                          report.uniqueCode,
                                          report.name,
                                          AttendanceType.RETURN_FROM_LEAVE,
                                          (report as any).returnFromLeaveTime
                                        )
                                      }
                                    >
                                      <Trash2 className="h-3 w-3" />
                                    </Button>
                                  </div>
                                ) : (
                                  <div className="flex items-center">
                                    <div className="mr-2 h-2 w-2 rounded-full bg-gray-400"></div>
                                    <span className="text-xs text-gray-500 dark:text-gray-400">
                                      Tidak kembali
                                    </span>
                                  </div>
                                )}
                              </TableCell>

                              {/* Pulang Cell for School Report */}
                              <TableCell>
                                {report.dismissal ? (
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center">
                                      <div className="mr-2 h-2 w-2 rounded-full bg-green-500"></div>
                                      <span className="text-xs italic text-slate-500 dark:text-slate-400">
                                        {report.dismissalTime}
                                      </span>
                                    </div>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="h-6 w-6 text-red-500 hover:bg-red-100 hover:text-red-700 dark:hover:bg-red-900/30"
                                      onClick={() =>
                                        handleDeleteClick(
                                          report.uniqueCode,
                                          report.name,
                                          AttendanceType.DISMISSAL,
                                          report.dismissalTime
                                        )
                                      }
                                    >
                                      <Trash2 className="h-3 w-3" />
                                    </Button>
                                  </div>
                                ) : (
                                  <div className="flex items-center">
                                    <div className="mr-2 h-2 w-2 rounded-full bg-red-500"></div>
                                    <span className="text-xs text-red-500 dark:text-red-400">
                                      Belum pulang
                                    </span>
                                  </div>
                                )}
                              </TableCell>
                            </>
                          )}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="month" className="mt-4">
            <Card className="overflow-hidden border-teal-200 shadow-sm transition-shadow hover:shadow-md dark:border-slate-700">
              <CardHeader className="bg-teal-50 pb-2 dark:bg-teal-900/20">
                <CardTitle className="text-lg font-medium text-teal-700 dark:text-teal-300">
                  Ekspor Laporan Bulanan
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-6">
                  <div className="flex flex-col space-y-2">
                    <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      Pilih Bulan
                    </label>
                    <div className="flex items-center space-x-4">
                      <Select
                        value={selectedMonth.getMonth().toString()}
                        onValueChange={value => {
                          const newDate = new Date(selectedMonth)
                          newDate.setMonth(parseInt(value))
                          setSelectedMonth(newDate)
                        }}
                      >
                        <SelectTrigger className="w-full border-slate-300 dark:border-slate-600">
                          <SelectValue placeholder="Pilih bulan" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="0">Januari</SelectItem>
                          <SelectItem value="1">Februari</SelectItem>
                          <SelectItem value="2">Maret</SelectItem>
                          <SelectItem value="3">April</SelectItem>
                          <SelectItem value="4">Mei</SelectItem>
                          <SelectItem value="5">Juni</SelectItem>
                          <SelectItem value="6">Juli</SelectItem>
                          <SelectItem value="7">Agustus</SelectItem>
                          <SelectItem value="8">September</SelectItem>
                          <SelectItem value="9">Oktober</SelectItem>
                          <SelectItem value="10">November</SelectItem>
                          <SelectItem value="11">Desember</SelectItem>
                        </SelectContent>
                      </Select>

                      <Select
                        value={selectedMonth.getFullYear().toString()}
                        onValueChange={value => {
                          const newDate = new Date(selectedMonth)
                          newDate.setFullYear(parseInt(value))
                          setSelectedMonth(newDate)
                        }}
                      >
                        <SelectTrigger className="w-full border-slate-300 dark:border-slate-600">
                          <SelectValue placeholder="Pilih tahun" />
                        </SelectTrigger>
                        <SelectContent>
                          {Array.from(
                            { length: 5 },
                            (_, i) => new Date().getFullYear() - 2 + i
                          ).map(year => (
                            <SelectItem key={year} value={year.toString()}>
                              {year}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm text-slate-600 dark:text-slate-400">
                      Ekspor laporan kehadiran untuk bulan{' '}
                      <span className="font-semibold text-teal-600 dark:text-teal-400">
                        {format(selectedMonth, 'MMMM yyyy', { locale: id })}
                      </span>
                    </p>

                    <div className="rounded-md bg-slate-100 p-3 text-xs text-slate-500 dark:bg-slate-800 dark:text-slate-400">
                      <p>
                        Laporan akan mencakup data kehadiran Zuhur, Asr, Pulang, dan Ijin untuk
                        semua siswa selama bulan tersebut. Data akan diformat dalam file CSV yang
                        dapat dibuka dengan aplikasi spreadsheet seperti Microsoft Excel.
                      </p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      Kelas
                    </label>
                    <Select
                      value={classFilter}
                      onValueChange={value => {
                        setClassFilter(value)
                      }}
                    >
                      <SelectTrigger className="border-slate-300 dark:border-slate-600">
                        <SelectValue placeholder="Pilih kelas" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Semua Kelas</SelectItem>
                        {classes.map(cls => (
                          <SelectItem key={cls.id} value={cls.name}>
                            {cls.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <Button
                    onClick={handleExportMonth}
                    className="w-full bg-teal-500 text-white hover:bg-teal-600"
                    disabled={isExportingMonth}
                  >
                    {isExportingMonth ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Mengekspor...
                      </>
                    ) : (
                      <>
                        <Download className="mr-2 h-4 w-4" />
                        Ekspor Laporan Bulanan
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="year" className="mt-4">
            <Card className="overflow-hidden border-blue-200 shadow-sm transition-shadow hover:shadow-md dark:border-slate-700">
              <CardHeader className="bg-blue-50 pb-2 dark:bg-blue-900/20">
                <CardTitle className="text-lg font-medium text-blue-700 dark:text-blue-300">
                  Ekspor Laporan Tahunan
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-6">
                  <div className="flex flex-col space-y-2">
                    <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      Pilih Tahun
                    </label>
                    <Select
                      value={selectedYear.toString()}
                      onValueChange={value => {
                        setSelectedYear(parseInt(value))
                      }}
                    >
                      <SelectTrigger className="border-slate-300 dark:border-slate-600">
                        <SelectValue placeholder="Pilih tahun" />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - 2 + i).map(
                          year => (
                            <SelectItem key={year} value={year.toString()}>
                              {year}
                            </SelectItem>
                          )
                        )}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm text-slate-600 dark:text-slate-400">
                      Ekspor laporan kehadiran untuk tahun{' '}
                      <span className="font-semibold text-blue-600 dark:text-blue-400">
                        {selectedYear}
                      </span>
                    </p>

                    <div className="rounded-md bg-slate-100 p-3 text-xs text-slate-500 dark:bg-slate-800 dark:text-slate-400">
                      <p>
                        Laporan tahunan mencakup semua data kehadiran Zuhur, Asr, Pulang, dan Ijin
                        untuk seluruh tahun. Proses ekspor mungkin membutuhkan waktu lebih lama
                        karena jumlah data yang besar.
                      </p>

                      <p className="mt-2">
                        <span className="font-semibold">Catatan:</span> Pastikan koneksi internet
                        stabil selama proses ekspor.
                      </p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      Kelas
                    </label>
                    <Select
                      value={classFilter}
                      onValueChange={value => {
                        setClassFilter(value)
                      }}
                    >
                      <SelectTrigger className="border-slate-300 dark:border-slate-600">
                        <SelectValue placeholder="Pilih kelas" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Semua Kelas</SelectItem>
                        {classes.map(cls => (
                          <SelectItem key={cls.id} value={cls.name}>
                            {cls.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <Button
                    onClick={handleExportYear}
                    className="w-full bg-blue-500 text-white hover:bg-blue-600"
                    disabled={isExportingYear}
                  >
                    {isExportingYear ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Mengekspor...
                      </>
                    ) : (
                      <>
                        <Download className="mr-2 h-4 w-4" />
                        Ekspor Laporan Tahunan
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>

      {/* Bottom navigation - hanya tampil di mobile */}
      <AdminBottomNav activeTab="reports" />

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Hapus Data Absensi</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menghapus data absensi ini? Tindakan ini tidak dapat
              dibatalkan.
            </DialogDescription>
          </DialogHeader>

          {selectedAttendance && (
            <div className="py-4">
              <p className="text-sm font-medium">Detail:</p>
              <ul className="mt-2 space-y-1 text-sm text-slate-500 dark:text-slate-400">
                <li>
                  <span className="font-medium">Nama:</span> {selectedAttendance.name}
                </li>
                <li>
                  <span className="font-medium">Tipe Absensi:</span>{' '}
                  {getAttendanceTypeName(selectedAttendance.type)}
                </li>
                <li>
                  <span className="font-medium">Tanggal:</span> {selectedAttendance.date}
                </li>
              </ul>
            </div>
          )}

          <DialogFooter className="flex space-x-2 sm:justify-end">
            <Button type="button" variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Batal
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={deleteLoading}
            >
              {deleteLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Menghapus...
                </>
              ) : (
                'Hapus'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Export Confirmation Dialog */}
      <Dialog open={showExportConfirmDialog} onOpenChange={setShowExportConfirmDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Konfirmasi Ekspor Data</DialogTitle>
            <DialogDescription>
              Anda akan mengekspor {filteredReports.length} baris data. Proses ini mungkin
              membutuhkan waktu beberapa saat pada perangkat dengan spesifikasi rendah.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <p className="text-sm text-slate-500 dark:text-slate-400">
              Laporan akan diunduh dalam format CSV yang dapat dibuka dengan Microsoft Excel, Google
              Sheets, atau aplikasi spreadsheet lainnya.
            </p>
          </div>

          <DialogFooter className="flex space-x-2 sm:justify-end">
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowExportConfirmDialog(false)}
            >
              Batal
            </Button>
            <Button
              type="button"
              onClick={() => {
                setShowExportConfirmDialog(false)
                handleExport()
              }}
              disabled={isExporting}
            >
              {isExporting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Mengekspor...
                </>
              ) : (
                'Ekspor Sekarang'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
