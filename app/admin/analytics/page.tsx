/**
 * Analytics Dashboard Page
 * Clean Architecture - Presentation Layer
 */

'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { useToast } from '@/hooks/use-toast'
import { useAdminSession } from '@/hooks/use-admin-session'
import {
  RefreshCw,
  Download,
  Calendar,
  Filter,
  BarChart3,
  TrendingUp,
  Users,
  Target,
} from 'lucide-react'

// Analytics components
import { KPICards, ExtendedKPICards } from '@/components/analytics/KPICards'
import { AttendanceTrendChart, MultiTrendChart } from '@/components/analytics/AttendanceTrendChart'
import {
  ClassComparisonChart,
  ClassComparisonHorizontal,
} from '@/components/analytics/ClassComparisonChart'

// Domain entities
import {
  DashboardKPIs,
  AnalyticsSummary,
  AttendanceAlert,
  TrendData,
  ClassAnalytics,
} from '@/lib/domain/entities/analytics'

/**
 * Analytics dashboard state interface
 */
interface AnalyticsDashboardState {
  kpis: DashboardKPIs | null
  summary: AnalyticsSummary | null
  alerts: AttendanceAlert[]
  isLoading: boolean
  isRefreshing: boolean
  lastUpdated: string | null
  error: string | null
}

/**
 * Analytics Dashboard Page Component
 */
export default function AnalyticsDashboardPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { admin, loading: sessionLoading } = useAdminSession()

  // State management
  const [state, setState] = useState<AnalyticsDashboardState>({
    kpis: null,
    summary: null,
    alerts: [],
    isLoading: true,
    isRefreshing: false,
    lastUpdated: null,
    error: null,
  })

  // Role-based access control
  useEffect(() => {
    if (!sessionLoading && admin) {
      // Only Super Admin and Admin can access analytics dashboard
      if (!['super_admin', 'admin'].includes(admin.role)) {
        router.push('/admin/home')
        return
      }
    }
  }, [admin, sessionLoading, router])

  /**
   * Fetch analytics dashboard data
   */
  const fetchDashboardData = async (refresh = false) => {
    try {
      setState(prev => ({
        ...prev,
        isLoading: !refresh,
        isRefreshing: refresh,
        error: null,
      }))

      const queryParams = new URLSearchParams()
      if (refresh) {
        queryParams.append('refresh', 'true')
      }
      queryParams.append('_t', Date.now().toString())

      const response = await fetch(`/api/analytics/dashboard?${queryParams.toString()}`, {
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
          Expires: '0',
        },
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to fetch analytics data')
      }

      const data = await response.json()

      setState(prev => ({
        ...prev,
        kpis: data.kpis,
        summary: data.summary,
        alerts: data.alerts,
        isLoading: false,
        isRefreshing: false,
        lastUpdated: data.metadata.timestamp,
        error: null,
      }))

      if (refresh) {
        toast({
          title: 'Data berhasil diperbarui',
          description: 'Dashboard analytics telah dimuat ulang dengan data terbaru',
        })
      }
    } catch (error) {
      console.error('Error fetching analytics dashboard:', error)

      setState(prev => ({
        ...prev,
        isLoading: false,
        isRefreshing: false,
        error: error instanceof Error ? error.message : 'Terjadi kesalahan saat memuat data',
      }))

      toast({
        title: 'Gagal memuat data analytics',
        description: error instanceof Error ? error.message : 'Terjadi kesalahan saat memuat data',
        variant: 'destructive',
      })
    }
  }

  /**
   * Handle data refresh
   */
  const handleRefresh = () => {
    fetchDashboardData(true)
  }

  /**
   * Handle CSV export
   */
  const handleExport = async () => {
    try {
      if (!state.kpis || !state.summary) {
        toast({
          title: 'Data tidak tersedia',
          description: 'Tidak ada data analytics untuk diekspor',
          variant: 'destructive',
        })
        return
      }

      // Import export utilities dynamically
      const { generateAnalyticsCSV, generateAnalyticsFilename, downloadCSV } = await import(
        '@/lib/utils/analytics-export'
      )

      // Generate CSV content
      const csvContent = generateAnalyticsCSV(state.kpis, state.summary, {
        includeCharts: false,
        includeRawData: true,
        includeSummary: true,
        includeRecommendations: true,
      })

      // Generate filename
      const filename = generateAnalyticsFilename(state.summary.period)

      // Download CSV
      downloadCSV(csvContent, filename)

      toast({
        title: 'Analytics berhasil diekspor',
        description: `File ${filename} telah diunduh ke perangkat Anda`,
      })
    } catch (error) {
      console.error('Error exporting analytics:', error)
      toast({
        title: 'Gagal mengekspor data',
        description: 'Terjadi kesalahan saat mengekspor data analytics',
        variant: 'destructive',
      })
    }
  }

  // Load data on component mount
  useEffect(() => {
    if (admin && ['super_admin', 'admin'].includes(admin.role)) {
      fetchDashboardData()
    }
  }, [admin])

  // Show loading state
  if (sessionLoading || !admin) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-8 w-64" />
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className="h-32" />
          ))}
        </div>
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <Skeleton className="h-96" />
          <Skeleton className="h-96" />
        </div>
      </div>
    )
  }

  // Access denied for non-authorized roles
  if (!['super_admin', 'admin'].includes(admin.role)) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <h2 className="mb-2 text-xl font-semibold text-red-600">Akses Ditolak</h2>
          <p className="text-gray-600">
            Anda tidak memiliki izin untuk mengakses dashboard analytics.
          </p>
        </CardContent>
      </Card>
    )
  }

  // Error state
  if (state.error) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <h2 className="mb-2 text-xl font-semibold text-red-600">Terjadi Kesalahan</h2>
          <p className="mb-4 text-gray-600">{state.error}</p>
          <Button onClick={() => fetchDashboardData()} variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Coba Lagi
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Dashboard Analytics
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Analisis komprehensif kehadiran dan shalat siswa
          </p>
          {state.lastUpdated && (
            <p className="text-xs text-gray-500">
              Terakhir diperbarui:{' '}
              {new Date(state.lastUpdated).toLocaleString('id-ID', {
                timeZone: 'Asia/Makassar',
                day: '2-digit',
                month: 'long',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
              })}{' '}
              WITA
            </p>
          )}
        </div>
        <div className="flex gap-2">
          <Button onClick={handleRefresh} disabled={state.isRefreshing} variant="outline" size="sm">
            {state.isRefreshing ? (
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="mr-2 h-4 w-4" />
            )}
            Refresh
          </Button>
          <Button onClick={handleExport} variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Ekspor CSV
          </Button>
        </div>
      </div>

      {/* Loading state for KPIs */}
      {state.isLoading ? (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className="h-32" />
          ))}
        </div>
      ) : state.kpis ? (
        <ExtendedKPICards kpis={state.kpis} />
      ) : null}

      {/* Charts Section */}
      {state.isLoading ? (
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <Skeleton className="h-96" />
          <Skeleton className="h-96" />
        </div>
      ) : state.summary ? (
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Attendance Trends */}
          <AttendanceTrendChart
            data={state.summary.trends.attendance}
            title="Tren Kehadiran Mingguan"
            height={350}
          />

          {/* Class Comparison - Desktop */}
          <div className="hidden md:block">
            <ClassComparisonChart
              data={state.summary.classPerformance}
              title="Perbandingan Performa Kelas"
              height={350}
            />
          </div>

          {/* Class Comparison - Mobile */}
          <div className="block md:hidden">
            <ClassComparisonHorizontal
              data={state.summary.classPerformance}
              title="Performa Kelas"
            />
          </div>
        </div>
      ) : null}

      {/* Multi-trend comparison */}
      {!state.isLoading && state.summary && (
        <MultiTrendChart
          attendanceData={state.summary.trends.attendance}
          prayerData={state.summary.trends.prayer}
          title="Perbandingan Tren Kehadiran vs Shalat"
          height={300}
        />
      )}

      {/* Insights and Recommendations */}
      {!state.isLoading && state.summary && (
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Key Insights */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-blue-600" />
                Wawasan Utama
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {state.summary.insights.map((insight, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <div
                      className={`mt-1 h-2 w-2 rounded-full ${
                        insight.trend === 'positive'
                          ? 'bg-green-500'
                          : insight.trend === 'negative'
                            ? 'bg-red-500'
                            : 'bg-gray-500'
                      }`}
                    />
                    <div>
                      <div className="font-medium">{insight.description}</div>
                      <div className="text-sm text-gray-600">
                        Nilai: <span className="font-semibold">{insight.value}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recommendations */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5 text-purple-600" />
                Rekomendasi Tindakan
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {state.summary.recommendations.slice(0, 3).map((rec, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div
                        className={`h-2 w-2 rounded-full ${
                          rec.priority === 'high'
                            ? 'bg-red-500'
                            : rec.priority === 'medium'
                              ? 'bg-yellow-500'
                              : 'bg-green-500'
                        }`}
                      />
                      <div className="font-medium">{rec.title}</div>
                    </div>
                    <div className="ml-4 text-sm text-gray-600">{rec.description}</div>
                    <div className="ml-4">
                      <ul className="space-y-1 text-xs text-gray-500">
                        {rec.actionItems.slice(0, 2).map((action, actionIndex) => (
                          <li key={actionIndex} className="flex items-center gap-1">
                            <span>•</span>
                            <span>{action}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
