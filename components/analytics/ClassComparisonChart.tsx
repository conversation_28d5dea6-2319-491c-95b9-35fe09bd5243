/**
 * Class Comparison Chart Component
 * Clean Architecture - Presentation Layer
 */

'use client'

import { BarChart, Bar, XAxis, YAxis, CartesianGrid, ResponsiveContainer, Tooltip, Legend, Cell } from 'recharts'
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ClassAnalytics } from '@/lib/domain/entities/analytics'
import { TrendingUp, TrendingDown, Minus, Users, Target, AlertTriangle } from 'lucide-react'

interface ClassComparisonChartProps {
  data: ClassAnalytics[]
  title?: string
  height?: number
  showLegend?: boolean
  className?: string
}

/**
 * Chart configuration for consistent theming
 */
const chartConfig = {
  attendance: {
    label: 'Kehadiran Umum',
    color: 'hsl(var(--chart-1))',
  },
  prayer: {
    label: 'Kehadiran Shalat',
    color: 'hsl(var(--chart-2))',
  },
  zuhr: {
    label: '<PERSON>hala<PERSON> Zuhur',
    color: 'hsl(var(--chart-3))',
  },
  asr: {
    label: 'Shalat Asr',
    color: 'hsl(var(--chart-4))',
  },
}

/**
 * Custom tooltip for class comparison
 */
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload

    return (
      <div className="rounded-lg border bg-background p-4 shadow-md">
        <div className="font-semibold text-lg mb-2">{label}</div>
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm">
            <Users className="h-4 w-4 text-blue-500" />
            <span>Total Siswa: {data.totalStudents}</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Target className="h-4 w-4 text-green-500" />
            <span>Kehadiran: {data.attendanceRate}%</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <span className="text-purple-500">🕌</span>
            <span>Shalat: {data.prayerComplianceRate}%</span>
          </div>
          <div className="grid grid-cols-2 gap-2 mt-2 text-xs">
            <div>Zuhur: {data.zuhrRate}%</div>
            <div>Asr: {data.asrRate}%</div>
          </div>
          <div className="flex items-center gap-2 mt-2">
            <Badge 
              variant={data.riskLevel === 'low' ? 'default' : data.riskLevel === 'medium' ? 'secondary' : 'destructive'}
              className="text-xs"
            >
              {data.riskLevel === 'low' ? 'Baik' : data.riskLevel === 'medium' ? 'Perhatian' : 'Risiko Tinggi'}
            </Badge>
            {data.trend === 'improving' && <TrendingUp className="h-3 w-3 text-green-500" />}
            {data.trend === 'declining' && <TrendingDown className="h-3 w-3 text-red-500" />}
            {data.trend === 'stable' && <Minus className="h-3 w-3 text-gray-500" />}
          </div>
        </div>
      </div>
    )
  }
  return null
}

/**
 * Get color based on performance level
 */
const getPerformanceColor = (rate: number) => {
  if (rate >= 90) return '#10b981' // green-500
  if (rate >= 80) return '#f59e0b' // amber-500
  if (rate >= 70) return '#f97316' // orange-500
  return '#ef4444' // red-500
}

/**
 * Class comparison chart component
 */
export function ClassComparisonChart({
  data,
  title = 'Perbandingan Kelas',
  height = 400,
  showLegend = true,
  className = '',
}: ClassComparisonChartProps) {
  // Transform data for chart
  const chartData = data.map(item => ({
    className: item.className,
    attendanceRate: item.attendanceRate,
    prayerComplianceRate: item.prayerComplianceRate,
    zuhrRate: item.zuhrRate,
    asrRate: item.asrRate,
    totalStudents: item.totalStudents,
    riskLevel: item.riskLevel,
    trend: item.trend,
  }))

  // Calculate statistics
  const avgAttendance = data.length > 0 
    ? data.reduce((sum, item) => sum + item.attendanceRate, 0) / data.length
    : 0

  const avgPrayer = data.length > 0 
    ? data.reduce((sum, item) => sum + item.prayerComplianceRate, 0) / data.length
    : 0

  const bestClass = data.reduce((best, current) => 
    current.attendanceRate > best.attendanceRate ? current : best
  )

  const worstClass = data.reduce((worst, current) => 
    current.attendanceRate < worst.attendanceRate ? current : worst
  )

  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">{title}</CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {data.length} Kelas
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className={`h-[${height}px] w-full`}>
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
              <XAxis 
                dataKey="className" 
                className="text-xs"
                tick={{ fontSize: 11 }}
                angle={-45}
                textAnchor="end"
                height={60}
              />
              <YAxis 
                className="text-xs"
                tick={{ fontSize: 11 }}
                domain={[0, 100]}
                tickFormatter={(value) => `${value}%`}
              />
              <Tooltip content={<CustomTooltip />} />
              {showLegend && (
                <Legend 
                  wrapperStyle={{ fontSize: '11px' }}
                  iconType="rect"
                />
              )}
              <Bar
                dataKey="attendanceRate"
                name="Kehadiran Umum"
                radius={[2, 2, 0, 0]}
              >
                {chartData.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={getPerformanceColor(entry.attendanceRate)} 
                  />
                ))}
              </Bar>
              <Bar
                dataKey="prayerComplianceRate"
                name="Kehadiran Shalat"
                radius={[2, 2, 0, 0]}
                fill="var(--color-prayer)"
                opacity={0.8}
              />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>

        {/* Summary Statistics */}
        <div className="mt-6 grid grid-cols-2 gap-4 md:grid-cols-4">
          <div className="text-center space-y-1">
            <div className="text-2xl font-bold text-blue-600">
              {avgAttendance.toFixed(1)}%
            </div>
            <div className="text-xs text-muted-foreground">Rata-rata Kehadiran</div>
          </div>
          <div className="text-center space-y-1">
            <div className="text-2xl font-bold text-purple-600">
              {avgPrayer.toFixed(1)}%
            </div>
            <div className="text-xs text-muted-foreground">Rata-rata Shalat</div>
          </div>
          <div className="text-center space-y-1">
            <div className="text-lg font-bold text-green-600">
              {bestClass.className}
            </div>
            <div className="text-xs text-muted-foreground">Kelas Terbaik</div>
            <div className="text-xs text-green-600">{bestClass.attendanceRate}%</div>
          </div>
          <div className="text-center space-y-1">
            <div className="text-lg font-bold text-red-600">
              {worstClass.className}
            </div>
            <div className="text-xs text-muted-foreground">Perlu Perhatian</div>
            <div className="text-xs text-red-600">{worstClass.attendanceRate}%</div>
          </div>
        </div>

        {/* Risk Analysis */}
        <div className="mt-4 space-y-2">
          <div className="text-sm font-medium">Analisis Risiko:</div>
          <div className="flex flex-wrap gap-2">
            {data.filter(c => c.riskLevel === 'high').length > 0 && (
              <Badge variant="destructive" className="text-xs">
                <AlertTriangle className="mr-1 h-3 w-3" />
                {data.filter(c => c.riskLevel === 'high').length} kelas risiko tinggi
              </Badge>
            )}
            {data.filter(c => c.riskLevel === 'medium').length > 0 && (
              <Badge variant="secondary" className="text-xs">
                {data.filter(c => c.riskLevel === 'medium').length} kelas perlu perhatian
              </Badge>
            )}
            {data.filter(c => c.riskLevel === 'low').length > 0 && (
              <Badge variant="default" className="text-xs">
                {data.filter(c => c.riskLevel === 'low').length} kelas berkinerja baik
              </Badge>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * Simplified horizontal bar chart for mobile
 */
export function ClassComparisonHorizontal({
  data,
  title = 'Performa Kelas',
  className = '',
}: Omit<ClassComparisonChartProps, 'height'>) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {data.map((classData, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="font-medium">{classData.className}</div>
                <div className="flex items-center gap-2 text-sm">
                  <span>{classData.attendanceRate}%</span>
                  <Badge 
                    variant={classData.riskLevel === 'low' ? 'default' : classData.riskLevel === 'medium' ? 'secondary' : 'destructive'}
                    className="text-xs"
                  >
                    {classData.riskLevel === 'low' ? 'Baik' : classData.riskLevel === 'medium' ? 'Perhatian' : 'Risiko'}
                  </Badge>
                </div>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="h-2 rounded-full transition-all duration-300"
                  style={{
                    width: `${classData.attendanceRate}%`,
                    backgroundColor: getPerformanceColor(classData.attendanceRate)
                  }}
                />
              </div>
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>Shalat: {classData.prayerComplianceRate}%</span>
                <span>{classData.totalStudents} siswa</span>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
