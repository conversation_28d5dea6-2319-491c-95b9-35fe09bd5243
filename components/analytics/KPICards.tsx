/**
 * KPI Cards Component
 * Clean Architecture - Presentation Layer
 */

'use client'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { DashboardKPIs } from '@/lib/domain/entities/analytics'
import { 
  Users, 
  UserCheck, 
  UserX, 
  TrendingUp, 
  TrendingDown, 
  Minus,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target
} from 'lucide-react'

interface KPICardsProps {
  kpis: DashboardKPIs
  className?: string
}

/**
 * Individual KPI card component
 */
interface KPICardProps {
  title: string
  value: string | number
  subtitle?: string
  change?: number
  changeLabel?: string
  icon: React.ReactNode
  color: 'blue' | 'green' | 'red' | 'orange' | 'purple' | 'gray'
  trend?: 'up' | 'down' | 'stable'
  className?: string
}

function KPICard({
  title,
  value,
  subtitle,
  change,
  changeLabel,
  icon,
  color,
  trend,
  className = '',
}: KPICardProps) {
  const colorClasses = {
    blue: {
      border: 'border-l-blue-500',
      icon: 'bg-blue-100 text-blue-600',
      value: 'text-blue-600',
    },
    green: {
      border: 'border-l-green-500',
      icon: 'bg-green-100 text-green-600',
      value: 'text-green-600',
    },
    red: {
      border: 'border-l-red-500',
      icon: 'bg-red-100 text-red-600',
      value: 'text-red-600',
    },
    orange: {
      border: 'border-l-orange-500',
      icon: 'bg-orange-100 text-orange-600',
      value: 'text-orange-600',
    },
    purple: {
      border: 'border-l-purple-500',
      icon: 'bg-purple-100 text-purple-600',
      value: 'text-purple-600',
    },
    gray: {
      border: 'border-l-gray-500',
      icon: 'bg-gray-100 text-gray-600',
      value: 'text-gray-600',
    },
  }

  const colors = colorClasses[color]

  const trendIcon = trend === 'up' 
    ? <TrendingUp className="h-3 w-3 text-green-500" />
    : trend === 'down'
    ? <TrendingDown className="h-3 w-3 text-red-500" />
    : <Minus className="h-3 w-3 text-gray-500" />

  const changeColor = change && change > 0 
    ? 'text-green-600'
    : change && change < 0
    ? 'text-red-600'
    : 'text-gray-600'

  return (
    <Card className={`${colors.border} border-l-4 ${className}`}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="text-sm font-medium text-gray-600">{title}</div>
            <div className={`text-3xl font-bold ${colors.value}`}>
              {typeof value === 'number' ? value.toLocaleString() : value}
            </div>
            {subtitle && (
              <div className="text-xs text-gray-500">{subtitle}</div>
            )}
            {change !== undefined && (
              <div className={`flex items-center gap-1 text-xs ${changeColor}`}>
                {trendIcon}
                <span>
                  {change > 0 ? '+' : ''}{change.toFixed(1)}%
                  {changeLabel && ` ${changeLabel}`}
                </span>
              </div>
            )}
          </div>
          <div className={`flex h-12 w-12 items-center justify-center rounded-full ${colors.icon}`}>
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * Main KPI Cards component
 */
export function KPICards({ kpis, className = '' }: KPICardsProps) {
  return (
    <div className={`grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4 ${className}`}>
      {/* Total Students Today */}
      <KPICard
        title="Total Siswa"
        value={kpis.todayAttendance.total}
        subtitle="Siswa terdaftar aktif"
        icon={<Users className="h-6 w-6" />}
        color="blue"
      />

      {/* Present Today */}
      <KPICard
        title="Hadir Hari Ini"
        value={`${kpis.todayAttendance.percentage}%`}
        subtitle={`${kpis.todayAttendance.present} dari ${kpis.todayAttendance.total} siswa`}
        change={kpis.todayAttendance.change}
        changeLabel="dari kemarin"
        icon={<UserCheck className="h-6 w-6" />}
        color="green"
        trend={kpis.todayAttendance.change > 0 ? 'up' : kpis.todayAttendance.change < 0 ? 'down' : 'stable'}
      />

      {/* Absent Today */}
      <KPICard
        title="Tidak Hadir"
        value={kpis.todayAttendance.absent}
        subtitle={`${((kpis.todayAttendance.absent / kpis.todayAttendance.total) * 100).toFixed(1)}% dari total siswa`}
        icon={<UserX className="h-6 w-6" />}
        color="red"
      />

      {/* Prayer Compliance */}
      <KPICard
        title="Kepatuhan Shalat"
        value={`${kpis.prayerMetrics.overallCompliance}%`}
        subtitle={`Zuhur: ${kpis.prayerMetrics.zuhrAttendance} • Asr: ${kpis.prayerMetrics.asrAttendance}`}
        change={kpis.prayerMetrics.change}
        changeLabel="dari kemarin"
        icon={<Target className="h-6 w-6" />}
        color="purple"
        trend={kpis.prayerMetrics.change > 0 ? 'up' : kpis.prayerMetrics.change < 0 ? 'down' : 'stable'}
      />
    </div>
  )
}

/**
 * Extended KPI Cards with additional metrics
 */
interface ExtendedKPICardsProps {
  kpis: DashboardKPIs
  className?: string
}

export function ExtendedKPICards({ kpis, className = '' }: ExtendedKPICardsProps) {
  return (
    <div className={`space-y-6 ${className}`}>
      {/* Primary KPIs */}
      <KPICards kpis={kpis} />

      {/* Secondary KPIs */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        {/* Weekly Trend */}
        <KPICard
          title="Tren Mingguan"
          value={`${kpis.weeklyTrend.currentWeek}%`}
          subtitle="Kehadiran minggu ini"
          change={kpis.weeklyTrend.change}
          changeLabel="dari minggu lalu"
          icon={<TrendingUp className="h-6 w-6" />}
          color="orange"
          trend={kpis.weeklyTrend.trend}
        />

        {/* Alerts */}
        <KPICard
          title="Peringatan"
          value={kpis.alerts.count}
          subtitle={`${kpis.alerts.critical} kritis • ${kpis.alerts.warning} peringatan`}
          icon={<AlertTriangle className="h-6 w-6" />}
          color={kpis.alerts.critical > 0 ? 'red' : kpis.alerts.warning > 0 ? 'orange' : 'green'}
        />

        {/* Prayer Zuhr */}
        <Card className="border-l-4 border-l-emerald-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <div className="text-sm font-medium text-gray-600">Shalat Zuhur</div>
                <div className="text-3xl font-bold text-emerald-600">
                  {kpis.prayerMetrics.zuhrAttendance}
                </div>
                <div className="text-xs text-gray-500">
                  {kpis.todayAttendance.total > 0 
                    ? `${Math.round((kpis.prayerMetrics.zuhrAttendance / kpis.todayAttendance.total) * 100)}% dari total siswa`
                    : 'Tidak ada data'
                  }
                </div>
                <Badge variant="outline" className="text-xs">
                  <CheckCircle className="mr-1 h-3 w-3" />
                  Shalat Berjamaah
                </Badge>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-emerald-100 text-emerald-600">
                <span className="text-xl">🕌</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Prayer Asr */}
        <Card className="border-l-4 border-l-teal-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <div className="text-sm font-medium text-gray-600">Shalat Asr</div>
                <div className="text-3xl font-bold text-teal-600">
                  {kpis.prayerMetrics.asrAttendance}
                </div>
                <div className="text-xs text-gray-500">
                  {kpis.todayAttendance.total > 0 
                    ? `${Math.round((kpis.prayerMetrics.asrAttendance / kpis.todayAttendance.total) * 100)}% dari total siswa`
                    : 'Tidak ada data'
                  }
                </div>
                <Badge variant="outline" className="text-xs">
                  <Clock className="mr-1 h-3 w-3" />
                  Shalat Berjamaah
                </Badge>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-teal-100 text-teal-600">
                <span className="text-xl">🕌</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
