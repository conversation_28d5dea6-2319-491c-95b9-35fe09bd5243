/**
 * Attendance Trend Chart Component
 * Clean Architecture - Presentation Layer
 */

'use client'

import { LineChart, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer, Tooltip, Legend } from 'recharts'
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { TrendData } from '@/lib/domain/entities/analytics'
import { TrendingUp, TrendingDown, Minus } from 'lucide-react'

interface AttendanceTrendChartProps {
  data: TrendData[]
  title?: string
  height?: number
  showLegend?: boolean
  className?: string
}

/**
 * Chart configuration for consistent theming
 */
const chartConfig = {
  attendance: {
    label: 'Kehadiran',
    color: 'hsl(var(--chart-1))',
  },
  prayer: {
    label: 'Shalat',
    color: 'hsl(var(--chart-2))',
  },
  trend: {
    label: 'Tren',
    color: 'hsl(var(--chart-3))',
  },
}

/**
 * Custom tooltip component
 */
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload
    const changeIcon = data.changeType === 'increase' 
      ? <TrendingUp className="h-3 w-3 text-green-500" />
      : data.changeType === 'decrease'
      ? <TrendingDown className="h-3 w-3 text-red-500" />
      : <Minus className="h-3 w-3 text-gray-500" />

    return (
      <div className="rounded-lg border bg-background p-3 shadow-md">
        <div className="font-medium">{label}</div>
        <div className="mt-1 space-y-1">
          {payload.map((entry: any, index: number) => (
            <div key={index} className="flex items-center gap-2 text-sm">
              <div 
                className="h-2 w-2 rounded-full" 
                style={{ backgroundColor: entry.color }}
              />
              <span>{entry.name}: {entry.value}%</span>
            </div>
          ))}
          {data.change !== undefined && (
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              {changeIcon}
              <span>
                {data.change > 0 ? '+' : ''}{data.change.toFixed(1)}% dari periode sebelumnya
              </span>
            </div>
          )}
        </div>
      </div>
    )
  }
  return null
}

/**
 * Attendance trend chart component
 */
export function AttendanceTrendChart({
  data,
  title = 'Tren Kehadiran',
  height = 300,
  showLegend = true,
  className = '',
}: AttendanceTrendChartProps) {
  // Transform data for chart
  const chartData = data.map(item => ({
    date: item.label,
    value: item.value,
    change: item.change,
    changeType: item.changeType,
  }))

  // Calculate overall trend
  const overallTrend = data.length > 1 
    ? data[data.length - 1].value - data[0].value
    : 0

  const trendIcon = overallTrend > 0 
    ? <TrendingUp className="h-4 w-4 text-green-500" />
    : overallTrend < 0
    ? <TrendingDown className="h-4 w-4 text-red-500" />
    : <Minus className="h-4 w-4 text-gray-500" />

  const trendText = overallTrend > 0 
    ? `+${overallTrend.toFixed(1)}% meningkat`
    : overallTrend < 0
    ? `${overallTrend.toFixed(1)}% menurun`
    : 'Stabil'

  const trendColor = overallTrend > 0 
    ? 'text-green-600'
    : overallTrend < 0
    ? 'text-red-600'
    : 'text-gray-600'

  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">{title}</CardTitle>
          <div className="flex items-center gap-1 text-sm">
            {trendIcon}
            <span className={trendColor}>{trendText}</span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-[300px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
              <XAxis 
                dataKey="date" 
                className="text-xs"
                tick={{ fontSize: 12 }}
                tickLine={{ stroke: 'hsl(var(--muted-foreground))' }}
              />
              <YAxis 
                className="text-xs"
                tick={{ fontSize: 12 }}
                tickLine={{ stroke: 'hsl(var(--muted-foreground))' }}
                domain={[0, 100]}
                tickFormatter={(value) => `${value}%`}
              />
              <Tooltip content={<CustomTooltip />} />
              {showLegend && (
                <Legend 
                  wrapperStyle={{ fontSize: '12px' }}
                  iconType="circle"
                />
              )}
              <Line
                type="monotone"
                dataKey="value"
                stroke="var(--color-attendance)"
                strokeWidth={2}
                dot={{ 
                  fill: 'var(--color-attendance)', 
                  strokeWidth: 2, 
                  r: 4 
                }}
                activeDot={{ 
                  r: 6, 
                  stroke: 'var(--color-attendance)',
                  strokeWidth: 2,
                  fill: 'white'
                }}
                name="Kehadiran (%)"
              />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>
        
        {/* Summary statistics */}
        <div className="mt-4 grid grid-cols-3 gap-4 text-center">
          <div className="space-y-1">
            <div className="text-2xl font-bold text-blue-600">
              {data.length > 0 ? data[data.length - 1].value.toFixed(1) : 0}%
            </div>
            <div className="text-xs text-muted-foreground">Hari Ini</div>
          </div>
          <div className="space-y-1">
            <div className="text-2xl font-bold text-green-600">
              {data.length > 0 ? Math.max(...data.map(d => d.value)).toFixed(1) : 0}%
            </div>
            <div className="text-xs text-muted-foreground">Tertinggi</div>
          </div>
          <div className="space-y-1">
            <div className="text-2xl font-bold text-orange-600">
              {data.length > 0 ? (data.reduce((sum, d) => sum + d.value, 0) / data.length).toFixed(1) : 0}%
            </div>
            <div className="text-xs text-muted-foreground">Rata-rata</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * Multiple line chart for comparing different metrics
 */
interface MultiTrendChartProps {
  attendanceData: TrendData[]
  prayerData: TrendData[]
  title?: string
  height?: number
  className?: string
}

export function MultiTrendChart({
  attendanceData,
  prayerData,
  title = 'Perbandingan Tren Kehadiran',
  height = 300,
  className = '',
}: MultiTrendChartProps) {
  // Combine data for chart
  const chartData = attendanceData.map((item, index) => ({
    date: item.label,
    attendance: item.value,
    prayer: prayerData[index]?.value || 0,
  }))

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-[300px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
              <XAxis 
                dataKey="date" 
                className="text-xs"
                tick={{ fontSize: 12 }}
              />
              <YAxis 
                className="text-xs"
                tick={{ fontSize: 12 }}
                domain={[0, 100]}
                tickFormatter={(value) => `${value}%`}
              />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Legend wrapperStyle={{ fontSize: '12px' }} />
              <Line
                type="monotone"
                dataKey="attendance"
                stroke="var(--color-attendance)"
                strokeWidth={2}
                dot={{ r: 4 }}
                name="Kehadiran Umum"
              />
              <Line
                type="monotone"
                dataKey="prayer"
                stroke="var(--color-prayer)"
                strokeWidth={2}
                dot={{ r: 4 }}
                name="Kehadiran Shalat"
              />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}
