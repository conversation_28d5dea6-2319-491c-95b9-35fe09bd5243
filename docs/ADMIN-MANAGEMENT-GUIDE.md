# 👥 Admin Management Guide - New Roles

## Overview
This guide explains how Super Admins can create and manage users with the new roles (Teacher and Receptionist) through the Admin Management interface.

## 🚀 Getting Started

### Access Requirements
- **Role**: Super Admin only
- **Page**: `/admin/admins` (Admin Management)
- **Navigation**: Admin menu → "Admin" tab

## 🆕 New Roles Available

### 🟢 Teacher Role
- **Purpose**: School entry attendance management
- **Permissions**: Can record "Entry" attendance via scanner
- **Access**: Scanner, Reports, Profile pages
- **Icon**: 🎓 GraduationCap (Green)

### 🟣 Receptionist Role  
- **Purpose**: Manual attendance entry for special cases
- **Permissions**: Can record Late Entry, Excused Absence, Temporary Leave, Return from Leave, Sick
- **Access**: Scanner (with Manual Entry tab), Reports, Profile pages
- **Icon**: 👥 Users (Purple)

### Existing Roles
- 🔴 **Super Admin**: Full system access (ShieldCheck icon)
- 🔵 **Admin**: Prayer attendance management (Shield icon)

## 📋 How to Create New Admin Users

### Step 1: Navigate to Admin Management
1. Login as Super Admin
2. Go to Admin Management page
3. Click "Tambah Admin" button

### Step 2: Fill User Information
1. **Nama**: Enter full name
2. **Username**: Enter unique username (minimum 3 characters)
3. **Role**: Select from dropdown:
   - Admin (Prayer attendance)
   - Super Admin (Full access)
   - **Teacher** (School entry)
   - **Receptionist** (Manual entry)
4. **Password**: Enter secure password (minimum 6 characters)

### Step 3: Save User
1. Click "Tambah" button
2. User will be created and can immediately login
3. Role permissions are automatically applied

## 🔍 Managing Existing Users

### Filtering Users
- **All Roles**: View all admin users
- **Admin**: Filter by Admin role only
- **Super Admin**: Filter by Super Admin role only
- **Teacher**: Filter by Teacher role only
- **Receptionist**: Filter by Receptionist role only

### Searching Users
- Use search box to find users by name or username
- Search is case-insensitive and matches partial text

### Editing Users
1. Click edit button (pencil icon) next to user
2. Modify name, role, or password
3. Username cannot be changed after creation
4. Leave password blank to keep existing password
5. Click "Perbarui" to save changes

### Deleting Users
1. Click delete button (trash icon) next to user
2. Confirm deletion in dialog
3. User will be permanently removed

## 🎯 Role-Specific Setup

### Setting Up Teachers
1. Create Teacher account
2. Provide login credentials to teacher
3. Teacher can immediately:
   - Access scanner page
   - Record "Entry" attendance
   - View attendance reports
   - Manage their profile

### Setting Up Receptionists
1. Create Receptionist account
2. Provide login credentials to receptionist
3. Receptionist can immediately:
   - Access scanner page with Manual Entry tab
   - Record manual attendance for special cases
   - Search students for manual entry
   - View attendance reports
   - Manage their profile

## 🔒 Security Features

### Access Control
- Only Super Admins can access Admin Management
- All operations require authentication
- Role permissions are enforced at API level

### Validation
- Username uniqueness is enforced
- Password strength requirements
- Input validation on all fields
- Proper error handling and feedback

## 📊 Visual Indicators

### Role Icons and Colors
- 🔴 **Super Admin**: Red ShieldCheck icon
- 🔵 **Admin**: Blue Shield icon
- 🟢 **Teacher**: Green GraduationCap icon
- 🟣 **Receptionist**: Purple Users icon

### Status Indicators
- User count displayed in table header
- Filter status shown in dropdown
- Search results highlighted
- Loading states during operations

## 🛠️ Troubleshooting

### Common Issues

#### "Username sudah digunakan"
- Choose a different username
- Usernames must be unique across all users

#### "Validasi gagal"
- Check all required fields are filled
- Ensure password meets minimum length
- Verify username meets character requirements

#### "Akses Ditolak"
- Only Super Admins can access this page
- Verify your account has Super Admin role

### Best Practices

#### Username Conventions
- Use clear, professional usernames
- Consider role-based prefixes (e.g., `teacher.john`, `receptionist.mary`)
- Avoid special characters

#### Password Security
- Use strong passwords (minimum 6 characters)
- Include mix of letters, numbers, symbols
- Provide secure password sharing method

#### Role Assignment
- **Teacher**: For staff managing school entry
- **Receptionist**: For front desk staff handling special cases
- **Admin**: For staff managing prayer attendance
- **Super Admin**: For system administrators only

## 📈 Usage Scenarios

### Scenario 1: New School Year Setup
1. Create Teacher accounts for entry management
2. Create Receptionist account for front desk
3. Assign appropriate roles based on responsibilities
4. Provide training on role-specific features

### Scenario 2: Staff Changes
1. Edit existing user roles as needed
2. Delete accounts for departed staff
3. Create new accounts for new hires
4. Update permissions as responsibilities change

### Scenario 3: Temporary Access
1. Create temporary accounts with appropriate roles
2. Set strong passwords for security
3. Delete accounts when no longer needed
4. Monitor usage through reports

## 🎓 Training Resources

### For New Teachers
- Focus on Entry attendance scanning
- Explain QR code scanning process
- Show how to access reports
- Demonstrate profile management

### For New Receptionists
- Emphasize Manual Entry tab usage
- Train on student search functionality
- Explain when to use each attendance type
- Show reason field requirements

## 📞 Support

### Getting Help
- Check this guide for common procedures
- Review error messages for specific issues
- Contact system administrator for technical problems
- Refer to main system documentation for general usage

### Reporting Issues
- Document specific error messages
- Note which role and operation was being performed
- Include screenshots if helpful
- Provide steps to reproduce the issue

---

**🎉 Admin Management is now ready for the new roles!**

Super Admins can immediately start creating Teacher and Receptionist accounts to support the expanded attendance management system.
