# New Roles and Attendance Types Implementation

## Overview

This document describes the implementation of 2 new user roles and 6 new attendance types in the ShalatYuk system, following the requirements specified in `TODO-ATTENDANCE-TYPES-AND-ROLES.md`.

## New Roles

### 1. Teacher Role
- **Access**: Scanner page, Reports, Profile
- **Attendance Types**: Entry only
- **UI**: Standard scanner interface with filtered attendance types

### 2. Receptionist Role  
- **Access**: Scanner page, Reports, Profile
- **Attendance Types**: Late Entry, Excused Absence, Temporary Leave, Return from Leave, Sick
- **UI**: Scanner interface + Manual Entry tab for non-scanning types

## New Attendance Types

### School Attendance (6 new types)
1. **Entry** (Masuk) - Regular school entry - Teacher role
2. **Late Entry** (<PERSON>su<PERSON>) - Late school entry - Receptionist role
3. **Excused Absence** (Izin) - Authorized absence - **NO SCAN REQUIRED** - Receptionist role
4. **Temporary Leave** (Izin Sementara) - Temporary leave - Receptionist role
5. **Return from Leave** (<PERSON><PERSON><PERSON>) - Return from leave - Receptionist role
6. **Sick** (<PERSON>kit) - Sick leave - **NO SCAN REQUIRED** - Receptionist role

### Existing Types (unchanged)
7. **Zuhr** (Shalat Zuhur) - Admin/Super Admin
8. **Asr** (Shalat Ashar) - Admin/Super Admin  
9. **Ijin** (Izin Tidak Shalat) - Admin/Super Admin
10. **Pulang** (Pulang) - Admin/Super Admin

## Key Features

### Manual Entry System
- **Location**: `/admin/home` page, "Entry Manual" tab (Receptionist only)
- **Student Search**: Autocomplete search by name or NIS
- **Attendance Type Selection**: Filtered by role permissions
- **Reason Field**: Required for Excused Absence and Sick types
- **Date/Time Picker**: Defaults to current time, editable
- **Validation**: Comprehensive input validation and duplicate checking

### Role-Based Access Control
- **Teacher**: Only Entry attendance type via scanner
- **Receptionist**: 5 specific types + manual entry tab for non-scanning types
- **Admin/Super Admin**: Existing prayer-related types (unchanged)
- **Student**: QR display only (unchanged)

### Security Features
- Role-based attendance type validation
- API endpoint protection
- Input sanitization and validation
- Audit logging for manual entries
- Session-based authentication

## Technical Implementation

### Database Changes
```sql
-- New roles
ALTER TYPE user_role ADD VALUE 'teacher';
ALTER TYPE user_role ADD VALUE 'receptionist';

-- New attendance types  
ALTER TYPE attendance_type ADD VALUE 'Entry';
ALTER TYPE attendance_type ADD VALUE 'Late Entry';
ALTER TYPE attendance_type ADD VALUE 'Excused Absence';
ALTER TYPE attendance_type ADD VALUE 'Temporary Leave';
ALTER TYPE attendance_type ADD VALUE 'Return from Leave';
ALTER TYPE attendance_type ADD VALUE 'Sick';
```

### API Endpoints

#### New Endpoints
- `GET /api/students/search` - Student search for manual entry
- `POST /api/absence/manual` - Manual attendance recording

#### Updated Endpoints
- `POST /api/absence/record` - Extended to handle new attendance types
- `GET /api/absence/check` - Extended validation for new types

### File Structure

#### Core Files Modified
```
lib/data/drizzle/schema.ts           # Database schema updates
lib/domain/entities/absence.ts       # AttendanceType enum updates
lib/config/role-permissions.ts       # New role configurations
lib/utils/attendance-validation.ts   # Validation logic updates
lib/middleware/auth.ts               # Authentication updates
hooks/use-admin-session.ts           # Session hook updates
```

#### UI Components
```
app/admin/home/<USER>
app/admin/home/<USER>
```

#### API Routes
```
app/api/students/search/route.ts     # Student search API
app/api/absence/manual/route.ts      # Manual entry API
app/api/absence/record/route.ts      # Updated record API
app/api/absence/check/route.ts       # Updated check API
```

## Usage Guide

### For Teachers
1. Login with teacher credentials
2. Navigate to Scanner page
3. Select "Entry" attendance type
4. Scan student QR codes

### For Receptionists
1. Login with receptionist credentials  
2. Navigate to Scanner page
3. **For scanning types**: Use "Scanner QR" tab
4. **For non-scanning types**: Use "Entry Manual" tab
   - Search for student by name or NIS
   - Select attendance type (Excused Absence/Sick)
   - Add reason (required for these types)
   - Set date/time if needed
   - Submit

### For Admins/Super Admins
- No changes to existing workflow
- Continue using existing prayer-related attendance types

## Security Considerations

### Role Validation
- Each API endpoint validates user role before processing
- Attendance type access is strictly controlled by role
- Manual entry is restricted to authorized roles only

### Input Validation
- All inputs are validated using Zod schemas
- UUID format validation for student codes
- Required field validation for reason fields
- Date/time format validation

### Audit Trail
- All manual entries are logged with user information
- Timestamps and reasons are recorded
- Failed attempts are logged for security monitoring

## Testing

### Automated Tests
- Role permission validation tests
- Attendance type access control tests
- API endpoint security tests
- Input validation tests

### Manual Testing Checklist
- [ ] Teacher can only access Entry attendance type
- [ ] Receptionist can access 5 specific types + manual entry
- [ ] Manual entry form works for non-scanning types
- [ ] Student search returns correct results
- [ ] Reason field is required for appropriate types
- [ ] Role-based UI elements display correctly
- [ ] Existing functionality remains unchanged

## Deployment Notes

### Database Migration
Run the migration script to add new enum values:
```bash
npm run db:migrate
```

### Environment Considerations
- No environment-specific changes required
- All configurations are role-based and dynamic
- Works in dev, test, and production environments

## Troubleshooting

### Common Issues
1. **Database connection errors**: Ensure database is accessible
2. **Role permission errors**: Verify user has correct role assigned
3. **Student search not working**: Check database connection and user permissions
4. **Manual entry validation errors**: Verify all required fields are provided

### Debug Information
- Check browser console for client-side errors
- Review server logs for API errors
- Verify database enum values are properly added
- Confirm user sessions are valid

## Future Enhancements

### Potential Improvements
- Bulk manual entry for multiple students
- Advanced search filters (by class, date range)
- Export functionality for manual entries
- Mobile-optimized manual entry interface
- Integration with school management systems

### Scalability Considerations
- Manual entry form is optimized for performance
- Student search is limited to 10 results
- Database queries use proper indexing
- Caching is implemented for frequently accessed data
