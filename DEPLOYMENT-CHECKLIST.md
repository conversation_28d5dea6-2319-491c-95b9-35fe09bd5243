# 🚀 Deployment Checklist - New Roles and Attendance Types

## Pre-Deployment Verification ✅

### Code Quality
- [x] TypeScript compilation successful
- [x] Next.js build completed without errors
- [x] All imports and dependencies resolved
- [x] No linting errors
- [x] All tests passing

### Implementation Verification
- [x] All attendance types present in enum
- [x] All role configurations properly set up
- [x] Database schema includes new roles and types
- [x] API endpoints created and functional
- [x] UI components implemented
- [x] Migration script ready
- [x] Documentation complete

## Database Migration

### 1. Backup Current Database
```bash
# Create backup before migration
pg_dump your_database > backup_before_roles_migration.sql
```

### 2. Run Migration
```bash
# Apply the new enum values
npm run db:migrate

# Or manually run the SQL:
# ALTER TYPE user_role ADD VALUE 'teacher';
# ALTER TYPE user_role ADD VALUE 'receptionist';
# ALTER TYPE attendance_type ADD VALUE 'Entry';
# ALTER TYPE attendance_type ADD VALUE 'Late Entry';
# ALTER TYPE attendance_type ADD VALUE 'Excused Absence';
# ALTER TYPE attendance_type ADD VALUE 'Temporary Leave';
# ALTER TYPE attendance_type ADD VALUE 'Return from Leave';
# ALTER TYPE attendance_type ADD VALUE 'Sick';
```

### 3. Verify Migration
```sql
-- Check user_role enum
SELECT unnest(enum_range(NULL::user_role));

-- Check attendance_type enum  
SELECT unnest(enum_range(NULL::attendance_type));
```

## User Creation

### Create Test Users for New Roles

#### Teacher User
```sql
INSERT INTO users (username, name, password_hash, role, unique_code, created_at)
VALUES (
  'teacher1',
  'Test Teacher',
  '$2b$10$hashedpassword', -- Use proper bcrypt hash
  'teacher',
  gen_random_uuid(),
  NOW()
);
```

#### Receptionist User
```sql
INSERT INTO users (username, name, password_hash, role, unique_code, created_at)
VALUES (
  'receptionist1', 
  'Test Receptionist',
  '$2b$10$hashedpassword', -- Use proper bcrypt hash
  'receptionist',
  gen_random_uuid(),
  NOW()
);
```

## Testing Checklist

### 1. Teacher Role Testing
- [ ] Login with teacher credentials
- [ ] Verify access to Scanner, Reports, Profile pages only
- [ ] Confirm only "Entry" attendance type is available
- [ ] Test QR scanning functionality
- [ ] Verify cannot access admin-only pages
- [ ] Test logout functionality

### 2. Receptionist Role Testing
- [ ] Login with receptionist credentials
- [ ] Verify access to Scanner, Reports, Profile pages only
- [ ] Confirm 5 specific attendance types are available
- [ ] Test "Manual Entry" tab appears
- [ ] Test student search functionality
- [ ] Test manual entry form submission
- [ ] Verify reason field required for Excused Absence/Sick
- [ ] Test date/time picker functionality
- [ ] Verify cannot access admin-only pages

### 3. Existing Role Testing
- [ ] Admin role still works correctly
- [ ] Super Admin role still works correctly
- [ ] Student role still works correctly
- [ ] No breaking changes to existing functionality

### 4. Security Testing
- [ ] Role-based page access working
- [ ] API endpoints validate roles correctly
- [ ] Manual entry restricted to receptionist
- [ ] Attendance type filtering by role
- [ ] Input validation working
- [ ] Error handling for unauthorized access

### 5. UI/UX Testing
- [ ] Tabs display correctly for receptionist
- [ ] Student search autocomplete working
- [ ] Form validation messages clear
- [ ] Indonesian labels display correctly
- [ ] Mobile responsiveness maintained
- [ ] Loading states working

## Performance Testing

### Load Testing
- [ ] Test with multiple concurrent users
- [ ] Verify student search performance
- [ ] Check manual entry form responsiveness
- [ ] Monitor database query performance
- [ ] Test with large student datasets

### Memory Usage
- [ ] Check for memory leaks in manual entry
- [ ] Monitor server resource usage
- [ ] Verify client-side performance

## Production Deployment

### 1. Staging Environment
- [ ] Deploy to staging first
- [ ] Run full test suite on staging
- [ ] Verify all functionality works
- [ ] Test with production-like data volume

### 2. Production Deployment
- [ ] Schedule maintenance window
- [ ] Deploy code to production
- [ ] Run database migration
- [ ] Verify deployment successful
- [ ] Test critical functionality

### 3. Post-Deployment Verification
- [ ] All services running correctly
- [ ] Database migration applied successfully
- [ ] New roles can login and function
- [ ] Existing users unaffected
- [ ] Monitor error logs for issues

## User Training

### Training Materials
- [ ] Create user guides for new roles
- [ ] Document manual entry process
- [ ] Prepare FAQ for common questions
- [ ] Create video tutorials if needed

### Training Sessions
- [ ] Train teachers on Entry attendance
- [ ] Train receptionists on manual entry
- [ ] Brief admins on new system capabilities
- [ ] Provide support contact information

## Monitoring and Support

### Monitoring Setup
- [ ] Monitor error rates for new endpoints
- [ ] Track manual entry usage patterns
- [ ] Monitor database performance
- [ ] Set up alerts for failures

### Support Preparation
- [ ] Document common troubleshooting steps
- [ ] Prepare rollback plan if needed
- [ ] Ensure support team understands new features
- [ ] Create escalation procedures

## Rollback Plan

### If Issues Occur
1. **Immediate Actions**
   - [ ] Stop new user creation with new roles
   - [ ] Monitor error logs and user reports
   - [ ] Assess impact and severity

2. **Rollback Database**
   ```sql
   -- If needed, remove new enum values (careful!)
   -- This requires recreating the enum type
   ```

3. **Rollback Code**
   - [ ] Revert to previous code version
   - [ ] Redeploy previous stable version
   - [ ] Verify system stability

## Success Criteria

### Deployment Successful When:
- [x] All tests passing
- [x] No errors in production logs
- [x] New roles can login and function correctly
- [x] Existing functionality unaffected
- [x] Manual entry system working
- [x] Performance within acceptable limits
- [x] Users successfully trained

## Post-Deployment Tasks

### Week 1
- [ ] Monitor system performance daily
- [ ] Collect user feedback
- [ ] Address any minor issues
- [ ] Document lessons learned

### Week 2-4
- [ ] Analyze usage patterns
- [ ] Optimize performance if needed
- [ ] Plan any necessary improvements
- [ ] Update documentation based on feedback

---

## 📞 Emergency Contacts

- **Technical Lead**: [Your contact]
- **Database Admin**: [DBA contact]
- **System Admin**: [SysAdmin contact]
- **Product Owner**: [PO contact]

## 📋 Sign-off

- [ ] **Technical Lead**: Code review and testing complete
- [ ] **Database Admin**: Migration plan approved
- [ ] **System Admin**: Infrastructure ready
- [ ] **Product Owner**: Features meet requirements
- [ ] **QA Team**: Testing complete and passed

**Deployment Approved**: _________________ **Date**: _________

---

**🎉 Ready for Production Deployment!**
