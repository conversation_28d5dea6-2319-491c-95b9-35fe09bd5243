# Todo List Aplikasi Absensi Shalat

## Setup Awal

- [x] Siapkan struktur folder Clean Architecture (Presentation, Domain, Data)
- [x] Konfigurasi ESLint dan Prettier
- [x] Setup TypeScript dengan konfigurasi yang ketat
- [x] Konfigurasi Tailwind CSS dan shadcn/ui
- [x] Setup next-themes untuk dukungan tema gelap/terang
- [x] Setup environment variables (.env dan .env.local)
- [x] Setup SSH tunnel untuk development

## Clean Architecture Implementation

- [ ] Domain Layer:

  - [x] Definisikan entity User (admin, student)
  - [x] Definisikan entity Attendance (Zuhr, Asr, Pulang)
  - [x] Definisikan entity Class dan AttendanceSummary
  - [x] Implementasi use cases untuk autentikasi (login, forgot password)
  - [ ] Implementasi use cases untuk absensi (record, check, reports)
  - [ ] Implementasi use cases untuk manajemen user
  - [x] Definisikan interface repositories untuk abstraksi data layer

- [ ] Data Layer:

  - [x] Implementasi Drizzle schema untuk PostgreSQL
  - [x] Implementasi repositories untuk User
  - [x] Implementasi repositories untuk Attendance
  - [x] Implementasi repositories untuk Class dan AttendanceSummary
  - [x] Implementasi cache service dengan Redis
  - [ ] Implementasi WhatsApp service dengan n8n webhook
  - [x] Implementasi JWT service untuk autentikasi

- [ ] Presentation Layer:

  - [ ] Implementasi hooks untuk autentikasi (useAuth)
  - [ ] Implementasi hooks untuk absensi (useAttendance)
  - [ ] Implementasi hooks untuk manajemen user (useUsers)
  - [ ] Implementasi context providers (AuthProvider)
  - [x] Implementasi form validation dengan Zod schemas
  - [ ] Implementasi error handling dan toast notifications

- [ ] API Routes:
  - [x] Implementasi API routes dengan dependency injection
  - [x] Implementasi middleware untuk autentikasi dan rate limiting
  - [x] Implementasi error handling untuk API responses

## Database dan Backend

- [x] Buat skema database PostgreSQL sesuai PRD
- [x] Setup Drizzle ORM dan koneksi database
- [x] Konfigurasi Redis untuk caching
- [ ] Setup n8n Webhook untuk WhatsApp OTP
- [x] Implementasi JWT untuk autentikasi
- [x] Implementasi rate limiting untuk API
- [x] Implementasi fallback untuk Redis dan PostgreSQL

## Landing Page

- [x] Buat Hero Section dengan headline dan gambar
- [x] Buat About Section dengan deskripsi aplikasi
- [x] Buat Features Section dengan 3 card (QR Code, Scanner, Reports)
- [x] Buat Footer dengan informasi kontak
- [x] Implementasi Theme Toggle

## Student App

- [x] Implementasi halaman Login dengan validasi Zod
- [x] Implementasi halaman Forgot Password dengan OTP WhatsApp
- [x] Implementasi halaman Home dengan QR Code
- [x] Implementasi halaman Profile
- [x] Implementasi fitur refresh QR Code setiap 5 menit
- [x] Implementasi status absensi real-time

## Admin App

- [ ] Implementasi halaman Login Admin
- [ ] Implementasi halaman Scanner dengan jsQR
- [ ] Implementasi halaman Reports dengan filter dan export CSV
- [ ] Implementasi halaman User Management (CRUD)
  - [x] **NEW: Super Admin Change Student Password Feature** ✅
  - [x] **NEW: Display Student Usernames in Student Table** ✅
- [ ] Implementasi halaman Profile Admin
- [ ] Implementasi ringkasan absensi real-time

## API Routes

- [x] Implementasi `/api/auth/student/google` (Initiates Google OAuth flow)
- [x] Implementasi `/api/auth/student/google/callback` (Handles Google callback)
- [x] Implementasi `/api/auth/logout` (Handles user logout)
- [x] Implementasi `/api/auth/check-session` (Session validation)
- [x] Implementasi `/api/student/whatsapp/send-otp` (Sends OTP via WhatsApp)
- [x] Implementasi `/api/student/whatsapp/verify-otp` (Verifies WhatsApp OTP)
- [x] Implementasi `/api/student/profile` (GET, PATCH)
- [x] Implementasi `/api/absence/record` (POST)
- [x] Implementasi `/api/absence/check` (GET)
- [x] Implementasi `/api/absence/reports` (GET)
- [x] Implementasi `/api/users` (GET, POST)
- [x] Implementasi `/api/users/{id}` (PATCH, DELETE)
- [x] Implementasi `/api/admin/profile` (PATCH)
- [x] Implementasi `/api/admin/sessions` (Session management)
- [ ] Implementasi `/api/refresh-summary` (POST)

## Testing

- [x] Setup simple test scripts untuk database connections
- [x] Setup simple test scripts untuk domain layer
- [ ] Setup Jest untuk unit testing
- [ ] Setup React Testing Library untuk component testing
- [ ] Tulis test untuk utility functions
- [ ] Tulis test untuk komponen UI
- [ ] Tulis test untuk API routes
- [ ] Pastikan coverage testing minimal 80%

## Deployment

- [x] Create deployment script (scripts/deploy.sh)
- [ ] Konfigurasi Docker untuk aplikasi Next.js
- [ ] Setup EasyPanel di VPS
- [ ] Konfigurasi HTTPS dengan Let's Encrypt
- [ ] Setup backup harian untuk PostgreSQL
- [ ] Implementasi logging dengan Winston
- [ ] Setup monitoring (opsional: Sentry)

## Optimasi

- [ ] Optimasi performa frontend (lazy loading, code splitting)
- [ ] Optimasi query database
- [x] Implementasi strategi caching yang efektif dengan Redis
- [x] Implementasi in-memory fallback untuk Redis
- [ ] Pastikan responsif untuk semua ukuran layar (320px-1280px)
- [ ] Pastikan aksesibilitas sesuai WCAG 2.1 Level AA

## Refactoring

- [x] Refactor authentication routes to follow RESTful principles
- [x] Remove [...nextauth] catch-all route in favor of explicit routes
- [x] Implement centralized configuration in lib/config.ts
- [x] Implement proper error handling for database connections
- [x] Remove NextAuth.js completely (unused dependency)
- [x] Remove Google OAuth references (not implemented)
- [x] Remove domain-based routing (DOMAIN, STUDENT_DOMAIN, ADMIN_DOMAIN)
- [x] Clean up Docker and deployment configurations
- [x] Remove unused test scripts and documentation
- [ ] Refactor remaining API routes to use new authentication middleware

## Code Cleanup Completed

- [x] NextAuth.js dependency removed from package.json
- [x] Google OAuth environment variables removed
- [x] Domain configuration completely removed from all files
- [x] Docker and docker-compose.yml cleaned up
- [x] Unused test scripts and documentation files removed
- [x] Over-engineered client-config.ts removed (unused)
- [x] Domain utilities simplified (removed unused functions)
- [x] Test scripts updated to reflect simplified architecture
- [x] TODO.md updated to reflect current implementation

## Architecture Simplification

- [x] Removed over-engineered API client wrapper (lib/client-config.ts)
- [x] Application uses simple, direct fetch() calls (best practice)
- [x] No unnecessary abstraction layers
- [x] Clean, maintainable code without domain-specific complexity
- [x] Single-domain deployment with relative URLs for security

## 📊 Analytics Dashboard Implementation

### ✅ Prayer Reports Analytics (`/admin/prayer-reports`)

- [x] **Modern Analytics Dashboard**: KPI cards with prayer metrics (Total Students, Zuhur, Asr, Pulang)
- [x] **Real Data Integration**: Uses existing `/api/absence/reports` API with `reportType=prayer`
- [x] **Prayer Trend Chart**: Line chart showing Zuhur, Asr, and Dismissal trends
- [x] **Student Data Matrix**: Paginated table (50 per page) for 3000+ students at bottom
- [x] **Advanced Filtering**: Class filter, date filter (today/yesterday/week/30days/monthly/yearly), and search
- [x] **CSV Export**: Prayer-specific export with detailed scoring
- [x] **Role-Based Access**: Super Admin and Admin only
- [x] **Pagination**: Handles 3000+ students with 50 per page
- [x] **Responsive Design**: Mobile-friendly layouts

### 🔄 School Reports Analytics (`/admin/school-reports`)

- [x] **Modern Analytics Dashboard**: KPI cards for school attendance
- [ ] **Attendance Types**: Entry, Late Entry, Excused Absence, Temporary Leave, Return from Leave, Sick
- [ ] **Student Data Matrix**: Paginated table (50 per page) for 3000+ students at bottom
- [ ] **Advanced Filtering**: Class filter, date filter (today/yesterday/week/30days/monthly/yearly), and search
- [x] **CSV Export**: School-specific export with attendance scoring
- [x] **Role-Based Access**: Super Admin, Teacher, and Receptionist
- [ ] **Pagination**: Handles 3000+ students with 50 per page

### 🔧 Technical Implementation

- [x] **Clean Architecture**: Separated UI components and business logic
- [x] **TypeScript Interfaces**: Proper type safety for all data structures
- [x] **Error Handling**: Comprehensive error states and user feedback
- [x] **Performance Optimized**: Efficient data processing and rendering
- [x] **Security First**: Proper role-based access control
- [x] **Pagination Logic**: Smart pagination for large datasets

### 📈 Analytics Features

- [x] **KPI Cards**: Visual metrics with color-coded indicators
- [x] **Trend Charts**: Line charts for attendance patterns
- [x] **Student Data Matrix**: Detailed student attendance tables (moved to bottom)
- [x] **Export Functionality**: CSV downloads with comprehensive data
- [x] **Real-time Updates**: Fresh data with cache-busting
- [x] **Extended Date Filters**: 30 days, monthly, yearly options
- [x] **Removed Class Performance Charts**: As requested by user
