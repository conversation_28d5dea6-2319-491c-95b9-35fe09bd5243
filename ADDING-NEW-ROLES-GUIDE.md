# 📋 Simple Guide: Adding New Roles

## 🎯 Overview

**Super simple process** - just update 3 files to add new roles. The system is already designed for this.

## 🚀 Quick Steps (5 minutes total)

### Step 1: Database Schema (1 minute)

Add new role to database enum:

```sql
-- Add to existing user_role enum
ALTER TYPE user_role ADD VALUE 'teacher';
ALTER TYPE user_role ADD VALUE 'receptionist';
```

### Step 2: Update Type Definition (1 minute)

Edit `lib/config/role-permissions.ts`:

```typescript
// Update the UserRole type
export type UserRole = 'student' | 'admin' | 'super_admin' | 'teacher' | 'receptionist'
```

### Step 3: Add Role Configuration (3 minutes)

Add new roles to `ROLE_CONFIG` in same file:

```typescript
teacher: {
  allowedPages: ['/admin/home', '/admin/reports', '/admin/profile'],
  redirectTo: '/admin/home',
  attendanceTypes: [AttendanceType.ENTRY], // Only school entry
  navigation: [
    { label: 'Scanner', path: '/admin/home', icon: 'camera' },
    { label: 'Laporan', path: '/admin/reports', icon: 'file-text' },
    { label: 'Profil', path: '/admin/profile', icon: 'user' }
  ]
},

receptionist: {
  allowedPages: ['/admin/home', '/admin/reports', '/admin/profile'],
  redirectTo: '/admin/home',
  attendanceTypes: [
    AttendanceType.LATE_ENTRY,
    AttendanceType.EXCUSED_ABSENCE,
    AttendanceType.TEMPORARY_LEAVE,
    AttendanceType.RETURN_FROM_LEAVE,
    AttendanceType.SICK
  ],
  navigation: [
    { label: 'Scanner', path: '/admin/home', icon: 'camera' },
    { label: 'Laporan', path: '/admin/reports', icon: 'file-text' },
    { label: 'Profil', path: '/admin/profile', icon: 'user' }
  ]
}
```

**That's it!** ✅ New roles will automatically work with existing pages and authentication.

### Step 3: Update Database Schema (if needed)

If your database has role constraints, update them to include the new role:

```sql
-- Example: Update role constraint
ALTER TABLE users DROP CONSTRAINT IF EXISTS chk_role_data;
ALTER TABLE users ADD CONSTRAINT chk_role_data CHECK (
  (role = 'student' AND unique_code IS NOT NULL AND nis IS NOT NULL) OR
  (role = 'admin' AND unique_code IS NULL AND nis IS NULL) OR
  (role = 'super_admin' AND unique_code IS NULL AND nis IS NULL) OR
  (role = 'teacher' AND unique_code IS NULL AND nis IS NULL)
);
```

### Step 4: Create Pages for the New Role

Create the necessary page files for your new role:

```
app/
├── teacher/
│   ├── layout.tsx          # Teacher layout wrapper
│   ├── home/
│   │   └── page.tsx        # Teacher home page
│   ├── classes/
│   │   └── page.tsx        # Teacher classes page
│   ├── reports/
│   │   └── page.tsx        # Teacher reports page
│   └── profile/
│       └── page.tsx        # Teacher profile page
```

### Step 5: Create Navigation Component (if needed)

Create a navigation component for the new role:

```typescript
// components/teacher-bottom-nav.tsx
'use client'

import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { getNavigationItems } from '@/lib/config/role-permissions'

export default function TeacherBottomNav() {
  const pathname = usePathname()
  const navItems = getNavigationItems('teacher')

  return (
    <nav className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
      <div className="flex justify-around">
        {navItems.map((item) => (
          <Link
            key={item.path}
            href={item.path}
            className={`flex flex-col items-center py-2 px-4 ${
              pathname === item.path
                ? 'text-blue-600'
                : 'text-gray-600'
            }`}
          >
            {/* Add icon component based on item.icon */}
            <span className="text-xs mt-1">{item.label}</span>
          </Link>
        ))}
      </div>
    </nav>
  )
}
```

### Step 6: Update Authentication (if needed)

If the new role needs special authentication logic, you can add it to the auth middleware:

```typescript
// lib/middleware/auth.ts
export async function authenticateTeacher(req: NextRequest): Promise<number> {
  try {
    const decoded = await authenticate(req, 'admin') // Use admin auth for teachers

    if (decoded.role !== 'teacher') {
      throw new Error('Access denied: Teacher role required')
    }

    return decoded.id
  } catch (error) {
    console.error('Teacher authentication error:', error)
    throw error
  }
}
```

### Step 7: Create API Routes (if needed)

Create API routes specific to the new role:

```typescript
// app/api/teacher/classes/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { authenticateTeacher } from '@/lib/middleware/auth'

export async function GET(req: NextRequest) {
  try {
    const teacherId = await authenticateTeacher(req)

    // Fetch teacher's classes
    const classes = await getTeacherClasses(teacherId)

    return NextResponse.json({ classes })
  } catch (error) {
    return NextResponse.json({ error: 'Authentication failed' }, { status: 401 })
  }
}
```

## 🔧 Why This Works

### Existing Infrastructure

- **Pages**: Teacher and Receptionist use existing `/admin/*` pages
- **Authentication**: Uses existing admin authentication system
- **Navigation**: Reuses existing admin navigation components
- **API**: Uses existing admin API routes with role-based filtering

### Role-Based Filtering

- **Attendance Types**: Automatically filtered by role in UI components
- **Page Access**: Controlled by `allowedPages` configuration
- **API Access**: Validated by existing role-based middleware

## 📋 Testing New Roles

### 1. Create Test Users

```sql
-- Create teacher user
INSERT INTO users (role, name, username, password_hash, created_at)
VALUES ('teacher', 'Test Teacher', 'teacher1', '$hashed_password', NOW());

-- Create receptionist user
INSERT INTO users (role, name, username, password_hash, created_at)
VALUES ('receptionist', 'Test Receptionist', 'receptionist1', '$hashed_password', NOW());
```

### 2. Test Login & Access

- ✅ Can login at `/admin` page
- ✅ Redirects to `/admin/home` (scanner page)
- ✅ Only sees allowed attendance types
- ✅ Can access reports and profile pages
- ❌ Cannot access super admin pages (users, admins, etc.)

### 3. Test Attendance Recording

- **Teacher**: Only sees "Entry" attendance type
- **Receptionist**: Only sees their 5 attendance types + manual entry option

## 📋 Configuration Options

### Page Access Patterns

You can use various patterns for `allowedPages`:

```typescript
allowedPages: [
  '/teacher/home', // Exact match
  '/teacher/*', // Wildcard (all teacher pages)
  '!/teacher/admin', // Exclusion (all teacher pages except admin)
  '/api/teacher/*', // API routes
]
```

### Attendance Type Options

```typescript
attendanceTypes: [
  AttendanceType.ZUHR, // Specific attendance type
  AttendanceType.ASR, // Another specific type
  'all', // All attendance types (like super_admin)
]
```

### Navigation Icons

Available icon options (add more as needed):

- `'home'` - Home icon
- `'camera'` - Camera/Scanner icon
- `'file-text'` - Reports icon
- `'users'` - Users icon
- `'user'` - Profile icon
- `'graduation-cap'` - Classes icon
- `'shield'` - Admin icon
- `'clock'` - Sessions icon

## 🧪 Testing New Roles

### 1. Create Test User

```sql
INSERT INTO users (role, name, username, password_hash, created_at)
VALUES ('teacher', 'Test Teacher', 'testteacher', '$hashed_password', NOW());
```

### 2. Test Authentication

```javascript
// Test login
const response = await fetch('/api/auth/admin/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    username: 'testteacher',
    password: 'password123',
  }),
})
```

### 3. Test Page Access

- ✅ Can access allowed pages
- ❌ Cannot access restricted pages
- ✅ Navigation shows correct items
- ✅ Redirects work properly

### 4. Test API Access

- ✅ Can access role-specific APIs
- ❌ Cannot access unauthorized APIs
- ✅ Attendance type restrictions work

## 🔒 Security Considerations

### 1. Principle of Least Privilege

Only grant the minimum permissions needed:

```typescript
// Good: Specific permissions
allowedPages: ['/teacher/home', '/teacher/classes']

// Avoid: Too broad permissions
allowedPages: ['/admin/*'] // Unless truly needed
```

### 2. Server-Side Validation

Always validate permissions on the server:

```typescript
// In API routes
const teacherId = await authenticateTeacher(req)
// Don't rely on client-side checks only
```

### 3. Attendance Type Security

Be careful with attendance type permissions:

```typescript
// Teachers might not need IJIN (excuse) permissions
attendanceTypes: [AttendanceType.ZUHR, AttendanceType.ASR]
// Instead of: ['all']
```

## 📝 Example: Complete Teacher Role Implementation

Here's a complete example of adding a teacher role:

```typescript
// 1. Update type
export type UserRole = 'student' | 'admin' | 'super_admin' | 'teacher'

// 2. Add configuration
teacher: {
  allowedPages: ['/admin/home', '/admin/reports', '/admin/profile'],
  redirectTo: '/admin/home',
  attendanceTypes: [AttendanceType.ENTRY],
  navigation: [
    { label: 'Scanner', path: '/admin/home', icon: 'camera' },
    { label: 'Laporan', path: '/admin/reports', icon: 'file-text' },
    { label: 'Profil', path: '/admin/profile', icon: 'user' }
  ]
}
```

## 🎯 Key Points

### Simple Design

- **No new pages needed** - Teacher/Receptionist use existing admin pages
- **No new authentication** - Uses existing admin login system
- **No new navigation** - Uses existing admin navigation
- **Role-based filtering** - Attendance types automatically filtered by role

### Manual Entry for Non-Scanning Types

For **Excused Absence** and **Sick** attendance types (where students can't scan):

- Add "Manual Entry" tab to scanner page (for Receptionist only)
- Simple form: Student search + Attendance type + Reason + Submit
- Follows existing UI patterns and components

### Security

- All permissions validated server-side
- Role-based access control already implemented
- Follows principle of least privilege

---

**This approach reuses existing infrastructure and keeps the implementation simple!** ✅
