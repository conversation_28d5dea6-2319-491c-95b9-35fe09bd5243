import { describe, it, expect } from '@jest/globals'
import { AttendanceType } from '@/lib/domain/entities/absence'
import { 
  getAllowedAttendanceTypes, 
  getAttendanceTypeLabel, 
  validateAttendanceTypeAccess 
} from '@/lib/utils/attendance-validation'
import { canHandleAttendanceType, canAccessPage, type UserRole } from '@/lib/config/role-permissions'

describe('New Attendance Types and Roles', () => {
  describe('AttendanceType enum', () => {
    it('should include all new attendance types', () => {
      expect(AttendanceType.ENTRY).toBe('Entry')
      expect(AttendanceType.LATE_ENTRY).toBe('Late Entry')
      expect(AttendanceType.EXCUSED_ABSENCE).toBe('Excused Absence')
      expect(AttendanceType.TEMPORARY_LEAVE).toBe('Temporary Leave')
      expect(AttendanceType.RETURN_FROM_LEAVE).toBe('Return from Leave')
      expect(AttendanceType.SICK).toBe('Sick')
    })

    it('should maintain existing attendance types', () => {
      expect(AttendanceType.ZUHR).toBe('Zuhr')
      expect(AttendanceType.ASR).toBe('Asr')
      expect(AttendanceType.DISMISSAL).toBe('Pulang')
      expect(AttendanceType.IJIN).toBe('Ijin')
    })
  })

  describe('Role-based attendance type access', () => {
    describe('Teacher role', () => {
      it('should only allow Entry attendance type', () => {
        const allowedTypes = getAllowedAttendanceTypes('teacher')
        expect(allowedTypes).toEqual([AttendanceType.ENTRY])
      })

      it('should validate access correctly', () => {
        expect(validateAttendanceTypeAccess('teacher', AttendanceType.ENTRY)).toBe(true)
        expect(validateAttendanceTypeAccess('teacher', AttendanceType.ZUHR)).toBe(false)
        expect(validateAttendanceTypeAccess('teacher', AttendanceType.LATE_ENTRY)).toBe(false)
      })

      it('should have correct page access', () => {
        expect(canAccessPage('teacher', '/admin/home')).toBe(true)
        expect(canAccessPage('teacher', '/admin/reports')).toBe(true)
        expect(canAccessPage('teacher', '/admin/profile')).toBe(true)
        expect(canAccessPage('teacher', '/admin/classes')).toBe(false)
        expect(canAccessPage('teacher', '/admin/users')).toBe(false)
      })
    })

    describe('Receptionist role', () => {
      it('should allow specific attendance types', () => {
        const allowedTypes = getAllowedAttendanceTypes('receptionist')
        expect(allowedTypes).toEqual([
          AttendanceType.LATE_ENTRY,
          AttendanceType.EXCUSED_ABSENCE,
          AttendanceType.TEMPORARY_LEAVE,
          AttendanceType.RETURN_FROM_LEAVE,
          AttendanceType.SICK
        ])
      })

      it('should validate access correctly', () => {
        expect(validateAttendanceTypeAccess('receptionist', AttendanceType.LATE_ENTRY)).toBe(true)
        expect(validateAttendanceTypeAccess('receptionist', AttendanceType.EXCUSED_ABSENCE)).toBe(true)
        expect(validateAttendanceTypeAccess('receptionist', AttendanceType.SICK)).toBe(true)
        expect(validateAttendanceTypeAccess('receptionist', AttendanceType.ZUHR)).toBe(false)
        expect(validateAttendanceTypeAccess('receptionist', AttendanceType.ENTRY)).toBe(false)
      })

      it('should have correct page access', () => {
        expect(canAccessPage('receptionist', '/admin/home')).toBe(true)
        expect(canAccessPage('receptionist', '/admin/reports')).toBe(true)
        expect(canAccessPage('receptionist', '/admin/profile')).toBe(true)
        expect(canAccessPage('receptionist', '/admin/classes')).toBe(false)
        expect(canAccessPage('receptionist', '/admin/users')).toBe(false)
      })
    })

    describe('Admin role (unchanged)', () => {
      it('should maintain existing attendance types', () => {
        const allowedTypes = getAllowedAttendanceTypes('admin')
        expect(allowedTypes).toEqual([
          AttendanceType.ZUHR,
          AttendanceType.ASR,
          AttendanceType.DISMISSAL,
          AttendanceType.IJIN
        ])
      })

      it('should not have access to new attendance types', () => {
        expect(validateAttendanceTypeAccess('admin', AttendanceType.ENTRY)).toBe(false)
        expect(validateAttendanceTypeAccess('admin', AttendanceType.SICK)).toBe(false)
      })
    })

    describe('Super Admin role (unchanged)', () => {
      it('should have access to all attendance types', () => {
        expect(canHandleAttendanceType('super_admin', AttendanceType.ZUHR)).toBe(true)
        expect(canHandleAttendanceType('super_admin', AttendanceType.ENTRY)).toBe(true)
        expect(canHandleAttendanceType('super_admin', AttendanceType.SICK)).toBe(true)
      })
    })
  })

  describe('Attendance type labels', () => {
    it('should have Indonesian labels for new attendance types', () => {
      expect(getAttendanceTypeLabel(AttendanceType.ENTRY)).toBe('Masuk')
      expect(getAttendanceTypeLabel(AttendanceType.LATE_ENTRY)).toBe('Masuk Terlambat')
      expect(getAttendanceTypeLabel(AttendanceType.EXCUSED_ABSENCE)).toBe('Izin')
      expect(getAttendanceTypeLabel(AttendanceType.TEMPORARY_LEAVE)).toBe('Izin Sementara')
      expect(getAttendanceTypeLabel(AttendanceType.RETURN_FROM_LEAVE)).toBe('Kembali dari Izin')
      expect(getAttendanceTypeLabel(AttendanceType.SICK)).toBe('Sakit')
    })

    it('should maintain existing labels', () => {
      expect(getAttendanceTypeLabel(AttendanceType.ZUHR)).toBe('Shalat Zuhur')
      expect(getAttendanceTypeLabel(AttendanceType.ASR)).toBe('Shalat Ashar')
      expect(getAttendanceTypeLabel(AttendanceType.DISMISSAL)).toBe('Pulang')
      expect(getAttendanceTypeLabel(AttendanceType.IJIN)).toBe('Izin Tidak Shalat')
    })
  })

  describe('Role permissions configuration', () => {
    it('should have navigation items for teacher', () => {
      const teacherRole: UserRole = 'teacher'
      expect(canAccessPage(teacherRole, '/admin/home')).toBe(true)
      expect(canAccessPage(teacherRole, '/admin/reports')).toBe(true)
      expect(canAccessPage(teacherRole, '/admin/profile')).toBe(true)
    })

    it('should have navigation items for receptionist', () => {
      const receptionistRole: UserRole = 'receptionist'
      expect(canAccessPage(receptionistRole, '/admin/home')).toBe(true)
      expect(canAccessPage(receptionistRole, '/admin/reports')).toBe(true)
      expect(canAccessPage(receptionistRole, '/admin/profile')).toBe(true)
    })
  })

  describe('Security validations', () => {
    it('should prevent unauthorized attendance type access', () => {
      // Teacher cannot record prayer attendance
      expect(validateAttendanceTypeAccess('teacher', AttendanceType.ZUHR)).toBe(false)
      expect(validateAttendanceTypeAccess('teacher', AttendanceType.ASR)).toBe(false)
      
      // Receptionist cannot record school entry
      expect(validateAttendanceTypeAccess('receptionist', AttendanceType.ENTRY)).toBe(false)
      
      // Admin cannot record receptionist-specific types
      expect(validateAttendanceTypeAccess('admin', AttendanceType.SICK)).toBe(false)
      expect(validateAttendanceTypeAccess('admin', AttendanceType.EXCUSED_ABSENCE)).toBe(false)
    })

    it('should handle invalid roles gracefully', () => {
      // @ts-ignore - Testing invalid role
      expect(getAllowedAttendanceTypes('invalid_role')).toEqual([])
      // @ts-ignore - Testing invalid role
      expect(validateAttendanceTypeAccess('invalid_role', AttendanceType.ENTRY)).toBe(false)
    })
  })
})

describe('Manual Entry Requirements', () => {
  describe('Reason field requirements', () => {
    it('should identify attendance types that require reason', () => {
      const requiresReason = [AttendanceType.EXCUSED_ABSENCE, AttendanceType.SICK]
      const doesNotRequireReason = [
        AttendanceType.ENTRY,
        AttendanceType.LATE_ENTRY,
        AttendanceType.TEMPORARY_LEAVE,
        AttendanceType.RETURN_FROM_LEAVE,
        AttendanceType.ZUHR,
        AttendanceType.ASR,
        AttendanceType.DISMISSAL,
        AttendanceType.IJIN
      ]

      // This test documents the business logic for which types require reasons
      expect(requiresReason).toContain(AttendanceType.EXCUSED_ABSENCE)
      expect(requiresReason).toContain(AttendanceType.SICK)
      
      doesNotRequireReason.forEach(type => {
        expect(requiresReason).not.toContain(type)
      })
    })
  })

  describe('Non-scanning attendance types', () => {
    it('should identify attendance types that do not require QR scanning', () => {
      const nonScanningTypes = [AttendanceType.EXCUSED_ABSENCE, AttendanceType.SICK]
      
      // These types are handled via manual entry only
      expect(nonScanningTypes).toContain(AttendanceType.EXCUSED_ABSENCE)
      expect(nonScanningTypes).toContain(AttendanceType.SICK)
    })
  })
})
