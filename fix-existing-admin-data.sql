-- Fix existing admin users data and apply correct constraints
-- This fixes the issue where existing admin users have class_id values

-- Step 1: Fix existing admin users by setting their class_id to NULL
UPDATE users 
SET class_id = NULL 
WHERE role IN ('admin', 'super_admin', 'teacher', 'receptionist');

-- Step 2: Verify the update worked
SELECT id, role, class_id, username, name 
FROM users 
WHERE role IN ('admin', 'super_admin', 'teacher', 'receptionist');

-- Step 3: Now apply the correct constraint
ALTER TABLE users DROP CONSTRAINT IF EXISTS chk_role_data;

ALTER TABLE users ADD CONSTRAINT chk_role_data CHECK (
  (role = 'student' AND unique_code IS NOT NULL AND username IS NOT NULL AND password_hash IS NOT NULL) OR 
  (role IN ('admin', 'super_admin', 'teacher', 'receptionist') AND google_email IS NULL AND unique_code IS NULL AND username IS NOT NULL AND password_hash IS NOT NULL AND class_id IS NULL)
);

-- Step 4: Verify constraint was applied successfully
SELECT conname, consrc 
FROM pg_constraint 
WHERE conname = 'chk_role_data';
